/**
 * 表单填充器 - 智能表单填充和进度管理
 * 负责将解析的数据填充到网页表单中
 * 创建日期: 2025-01-11
 */

class FormFiller {
    constructor(eventBus = window.mdacEventBus, stateManager = null, fieldMatcher = null, dataValidator = null) {
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        this.fieldMatcher = fieldMatcher;
        this.dataValidator = dataValidator;
        
        // 填充配置
        this.config = {
            fillDelay: 100,        // 字段间填充延迟
            retryAttempts: 3,      // 重试次数
            waitTimeout: 5000,     // 等待超时
            validateAfterFill: true, // 填充后验证
            skipReadonly: true,    // 跳过只读字段
            skipDisabled: true,    // 跳过禁用字段
            autoSubmit: false      // 自动提交
        };

        // 填充状态
        this.fillingSessions = new Map();
        this.currentSession = null;
        
        // 填充统计
        this.fillStats = {
            totalSessions: 0,
            successfulFills: 0,
            failedFills: 0,
            totalFieldsFilled: 0
        };

        console.log('📝 [FormFiller] 表单填充器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听填充请求
        this.eventBus.on('form:fill-request', (data) => {
            this.handleFillRequest(data);
        });

        // 监听配置更新
        this.eventBus.on('form:config-updated', (config) => {
            this.updateConfig(config);
        });
    }

    /**
     * 填充表单
     * @param {Object} data - 要填充的数据
     * @param {Object} options - 填充选项
     */
    async fillForm(data, options = {}) {
        try {
            console.log('📝 [FormFiller] 开始填充表单', { data, options });

            // 创建填充会话
            const session = this.createFillSession(data, options);
            this.currentSession = session;

            // 发布填充开始事件
            if (this.eventBus) {
                this.eventBus.emit('form:fill-start', {
                    sessionId: session.id,
                    timestamp: Date.now()
                });
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'form.isAutoFilling': true,
                    'form.fillProgress': 0
                });
            }

            // 步骤1: 验证数据
            const validationResult = await this.validateFillData(data, options);
            if (!validationResult.isValid && options.strict) {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
            }

            // 步骤2: 扫描和匹配字段
            const fieldMatching = await this.scanAndMatchFields(options);
            if (!fieldMatching.success) {
                throw new Error(`字段匹配失败: ${fieldMatching.error}`);
            }

            // 步骤3: 执行填充
            const fillResult = await this.executeFilling(
                validationResult.normalizedData || data,
                fieldMatching.matchedFields,
                session
            );

            // 步骤4: 验证填充结果
            const verificationResult = await this.verifyFillResult(fillResult, session);

            // 完成会话
            session.endTime = Date.now();
            session.duration = session.endTime - session.startTime;
            session.success = true;
            session.result = fillResult;
            session.verification = verificationResult;

            // 更新统计
            this.updateFillStats(session);

            const result = {
                success: true,
                sessionId: session.id,
                fillResult: fillResult,
                verification: verificationResult,
                duration: session.duration,
                timestamp: Date.now()
            };

            console.log('✅ [FormFiller] 表单填充完成', result);

            // 发布填充完成事件
            if (this.eventBus) {
                this.eventBus.emit('form:fill-complete', result);
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'form.isAutoFilling': false,
                    'form.fillProgress': 100,
                    'form.lastFillResult': result
                });
            }

            return result;

        } catch (error) {
            console.error('❌ [FormFiller] 表单填充失败', error);

            // 更新会话状态
            if (this.currentSession) {
                this.currentSession.success = false;
                this.currentSession.error = error.message;
                this.currentSession.endTime = Date.now();
            }

            const errorResult = {
                success: false,
                error: error.message,
                sessionId: this.currentSession?.id,
                timestamp: Date.now()
            };

            // 发布填充错误事件
            if (this.eventBus) {
                this.eventBus.emit('form:fill-error', errorResult);
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'form.isAutoFilling': false,
                    'errors.lastError': error.message
                });
            }

            throw error;
        } finally {
            this.currentSession = null;
        }
    }

    /**
     * 创建填充会话
     * @param {Object} data - 填充数据
     * @param {Object} options - 选项
     */
    createFillSession(data, options) {
        const session = {
            id: this.generateSessionId(),
            startTime: Date.now(),
            endTime: null,
            duration: null,
            data: data,
            options: { ...this.config, ...options },
            progress: {
                total: 0,
                completed: 0,
                failed: 0,
                skipped: 0
            },
            fieldResults: {},
            success: false,
            error: null
        };

        this.fillingSessions.set(session.id, session);
        this.fillStats.totalSessions++;

        return session;
    }

    /**
     * 验证填充数据
     * @param {Object} data - 数据
     * @param {Object} options - 选项
     */
    async validateFillData(data, options) {
        if (!this.dataValidator) {
            console.warn('⚠️ [FormFiller] 数据验证器不可用，跳过验证');
            return { isValid: true, normalizedData: data };
        }

        try {
            const validationResult = this.dataValidator.validateData(data, {
                strict: options.strict || false,
                normalizeData: true
            });

            console.log('🔍 [FormFiller] 数据验证完成', {
                isValid: validationResult.isValid,
                errorCount: validationResult.errors.length,
                warningCount: validationResult.warnings.length
            });

            return validationResult;

        } catch (error) {
            console.error('❌ [FormFiller] 数据验证失败', error);
            return {
                isValid: false,
                errors: [error.message],
                warnings: [],
                normalizedData: data
            };
        }
    }

    /**
     * 扫描和匹配字段
     * @param {Object} options - 选项
     */
    async scanAndMatchFields(options) {
        if (!this.fieldMatcher) {
            console.warn('⚠️ [FormFiller] 字段匹配器不可用，使用默认匹配');
            return { success: true, matchedFields: {} };
        }

        try {
            const matchingResult = await this.fieldMatcher.scanAndMatchFields({
                useCache: options.useCache !== false,
                forceRescan: options.forceRescan || false
            });

            console.log('🎯 [FormFiller] 字段匹配完成', {
                success: matchingResult.success,
                matchedCount: matchingResult.success ? 
                    Object.values(matchingResult.matchedFields).reduce((count, category) => 
                        count + Object.keys(category).length, 0) : 0
            });

            return matchingResult;

        } catch (error) {
            console.error('❌ [FormFiller] 字段匹配失败', error);
            return {
                success: false,
                error: error.message,
                matchedFields: {}
            };
        }
    }

    /**
     * 执行填充
     * @param {Object} data - 数据
     * @param {Object} matchedFields - 匹配的字段
     * @param {Object} session - 会话
     */
    async executeFilling(data, matchedFields, session) {
        const fillResult = {
            totalFields: 0,
            filledFields: 0,
            failedFields: 0,
            skippedFields: 0,
            fieldResults: {},
            errors: []
        };

        // 计算总字段数
        Object.values(matchedFields).forEach(category => {
            fillResult.totalFields += Object.keys(category).length;
        });

        session.progress.total = fillResult.totalFields;

        // 按类别填充字段
        for (const [categoryName, categoryFields] of Object.entries(matchedFields)) {
            if (!data[categoryName]) continue;

            for (const [fieldName, fieldInfo] of Object.entries(categoryFields)) {
                const fieldValue = data[categoryName][fieldName];
                
                if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
                    fillResult.skippedFields++;
                    session.progress.skipped++;
                    continue;
                }

                try {
                    // 执行单个字段填充
                    const fieldResult = await this.fillSingleField(
                        fieldInfo,
                        fieldValue,
                        `${categoryName}.${fieldName}`,
                        session
                    );

                    fillResult.fieldResults[`${categoryName}.${fieldName}`] = fieldResult;

                    if (fieldResult.success) {
                        fillResult.filledFields++;
                        session.progress.completed++;
                    } else {
                        fillResult.failedFields++;
                        session.progress.failed++;
                        fillResult.errors.push(fieldResult.error);
                    }

                } catch (error) {
                    fillResult.failedFields++;
                    session.progress.failed++;
                    fillResult.errors.push(`${categoryName}.${fieldName}: ${error.message}`);
                }

                // 更新进度
                const progress = Math.round((session.progress.completed + session.progress.failed + session.progress.skipped) / session.progress.total * 100);
                
                if (this.stateManager) {
                    this.stateManager.set('form.fillProgress', progress);
                }

                // 发布进度事件
                if (this.eventBus) {
                    this.eventBus.emit('form:fill-progress', {
                        sessionId: session.id,
                        progress: progress,
                        completed: session.progress.completed,
                        total: session.progress.total
                    });
                }

                // 字段间延迟
                if (session.options.fillDelay > 0) {
                    await this.delay(session.options.fillDelay);
                }
            }
        }

        return fillResult;
    }

    /**
     * 填充单个字段
     * @param {Object} fieldInfo - 字段信息
     * @param {*} value - 值
     * @param {string} fieldPath - 字段路径
     * @param {Object} session - 会话
     */
    async fillSingleField(fieldInfo, value, fieldPath, session) {
        const result = {
            success: false,
            value: value,
            attempts: 0,
            error: null,
            timestamp: Date.now()
        };

        const maxAttempts = session.options.retryAttempts;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            result.attempts = attempt;

            try {
                console.log(`📝 [FormFiller] 填充字段 ${fieldPath} (尝试 ${attempt}/${maxAttempts})`);

                // 发送填充命令到content script
                const response = await chrome.tabs.query({ active: true, currentWindow: true });
                const activeTab = response[0];

                if (!activeTab) {
                    throw new Error('无法获取当前活跃标签页');
                }

                const fillResponse = await chrome.tabs.sendMessage(activeTab.id, {
                    action: 'fillFormField',
                    fieldInfo: fieldInfo,
                    value: value,
                    options: {
                        skipReadonly: session.options.skipReadonly,
                        skipDisabled: session.options.skipDisabled,
                        waitTimeout: session.options.waitTimeout
                    }
                });

                if (fillResponse && fillResponse.success) {
                    result.success = true;
                    console.log(`✅ [FormFiller] 字段填充成功: ${fieldPath}`);
                    break;
                } else {
                    throw new Error(fillResponse?.error || '字段填充失败');
                }

            } catch (error) {
                result.error = error.message;
                console.warn(`⚠️ [FormFiller] 字段填充失败 ${fieldPath} (尝试 ${attempt}/${maxAttempts}): ${error.message}`);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxAttempts) {
                    await this.delay(1000 * attempt); // 递增延迟
                }
            }
        }

        return result;
    }

    /**
     * 验证填充结果
     * @param {Object} fillResult - 填充结果
     * @param {Object} session - 会话
     */
    async verifyFillResult(fillResult, session) {
        if (!session.options.validateAfterFill) {
            return { verified: false, reason: '跳过验证' };
        }

        try {
            // 获取填充后的表单值
            const response = await chrome.tabs.query({ active: true, currentWindow: true });
            const activeTab = response[0];

            if (!activeTab) {
                return { verified: false, reason: '无法获取当前标签页' };
            }

            const verifyResponse = await chrome.tabs.sendMessage(activeTab.id, {
                action: 'verifyFormValues',
                fieldResults: fillResult.fieldResults
            });

            if (verifyResponse && verifyResponse.success) {
                const verification = {
                    verified: true,
                    matchedValues: verifyResponse.matchedValues || 0,
                    totalValues: verifyResponse.totalValues || 0,
                    accuracy: verifyResponse.totalValues > 0 ? 
                        verifyResponse.matchedValues / verifyResponse.totalValues : 0,
                    discrepancies: verifyResponse.discrepancies || []
                };

                console.log('🔍 [FormFiller] 填充验证完成', verification);
                return verification;
            } else {
                return { 
                    verified: false, 
                    reason: verifyResponse?.error || '验证失败' 
                };
            }

        } catch (error) {
            console.error('❌ [FormFiller] 填充验证失败', error);
            return { 
                verified: false, 
                reason: error.message 
            };
        }
    }

    /**
     * 更新填充统计
     * @param {Object} session - 会话
     */
    updateFillStats(session) {
        if (session.success) {
            this.fillStats.successfulFills++;
        } else {
            this.fillStats.failedFills++;
        }

        this.fillStats.totalFieldsFilled += session.progress.completed;
    }

    /**
     * 获取填充会话
     * @param {string} sessionId - 会话ID
     */
    getFillSession(sessionId) {
        return this.fillingSessions.get(sessionId);
    }

    /**
     * 获取所有填充会话
     */
    getAllFillSessions() {
        return Array.from(this.fillingSessions.values());
    }

    /**
     * 获取填充统计
     */
    getFillStats() {
        return {
            ...this.fillStats,
            activeSessions: this.fillingSessions.size,
            successRate: this.fillStats.totalSessions > 0 ? 
                this.fillStats.successfulFills / this.fillStats.totalSessions : 0
        };
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ [FormFiller] 配置已更新', this.config);
    }

    /**
     * 处理填充请求事件
     * @param {Object} data - 请求数据
     */
    async handleFillRequest(data) {
        try {
            const { formData, options, callback } = data;
            const result = await this.fillForm(formData, options);
            
            if (callback && typeof callback === 'function') {
                callback(null, result);
            }
        } catch (error) {
            if (data.callback && typeof data.callback === 'function') {
                data.callback(error, null);
            }
        }
    }

    /**
     * 清理过期会话
     * @param {number} maxAge - 最大年龄（毫秒）
     */
    cleanupSessions(maxAge = 3600000) { // 默认1小时
        const now = Date.now();
        const expiredSessions = [];

        for (const [sessionId, session] of this.fillingSessions) {
            if (now - session.startTime > maxAge) {
                expiredSessions.push(sessionId);
            }
        }

        expiredSessions.forEach(sessionId => {
            this.fillingSessions.delete(sessionId);
        });

        if (expiredSessions.length > 0) {
            console.log(`🧹 [FormFiller] 清理了 ${expiredSessions.length} 个过期会话`);
        }
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        return 'fill_session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 销毁表单填充器
     */
    destroy() {
        // 清理会话
        this.fillingSessions.clear();
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('form:fill-request');
            this.eventBus.off('form:config-updated');
        }

        console.log('🗑️ [FormFiller] 表单填充器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormFiller;
} else {
    window.FormFiller = FormFiller;
}

console.log('✅ [FormFiller] 表单填充器模块已加载');
