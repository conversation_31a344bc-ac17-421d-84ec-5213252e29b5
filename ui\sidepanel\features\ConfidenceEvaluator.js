/**
 * 置信度评估器 - 智能数据质量评估和置信度分析系统
 *
 * 功能概述：
 * - 评估AI解析结果的可信度和准确性
 * - 分析数据完整性和一致性
 * - 提供字段级别的质量评估
 * - 生成详细的评估报告和建议
 * - 支持多维度评估算法和权重配置
 *
 * 依赖关系：
 * - EventBus: 事件通信
 * - DataValidator: 数据验证器
 *
 * 主要模块：
 * - CompletenessAnalyzer: 完整性分析器
 * - AccuracyEvaluator: 准确性评估器
 * - ConsistencyChecker: 一致性检查器
 * - ContextAnalyzer: 上下文分析器
 * - QualityReporter: 质量报告生成器
 *
 * 评估维度：
 * - 数据完整性 (25%): 必填字段覆盖率
 * - 数据准确性 (25%): 格式和内容正确性
 * - 字段一致性 (20%): 字段间逻辑一致性
 * - 上下文相关性 (15%): 上下文匹配度
 * - 源数据质量 (15%): 原始数据质量
 *
 * 创建日期: 2025-01-11
 * 重构日期: 2025-01-11
 * 版本: 2.0.0 (重构优化版本)
 */

class ConfidenceEvaluator {
    constructor(eventBus = window.mdacEventBus, dataValidator = null) {
        this.eventBus = eventBus;
        this.dataValidator = dataValidator;
        
        // 评估配置
        this.config = {
            minConfidenceThreshold: 0.7,
            warningThreshold: 0.5,
            enableDetailedAnalysis: true,
            enableFieldLevelAnalysis: true,
            enableContextAnalysis: true
        };

        // 评估权重
        this.weights = {
            dataCompleteness: 0.25,    // 数据完整性
            dataAccuracy: 0.25,       // 数据准确性
            fieldConsistency: 0.20,   // 字段一致性
            contextRelevance: 0.15,   // 上下文相关性
            sourceQuality: 0.15       // 源数据质量
        };

        // 字段重要性权重
        this.fieldImportance = {
            personalInfo: {
                name: 1.0,
                passportNumber: 0.9,
                birthDate: 0.8,
                nationality: 0.7,
                gender: 0.6
            },
            contactInfo: {
                email: 0.9,
                phone: 0.8,
                address: 0.7
            },
            travelInfo: {
                entryDate: 0.9,
                exitDate: 0.9,
                destination: 0.8,
                accommodation: 0.6
            },
            transportInfo: {
                flightNumber: 0.8,
                vehicleNumber: 0.8,
                modeOfTravel: 0.7
            }
        };

        // 评估历史
        this.evaluationHistory = [];
        this.maxHistorySize = 100;

        console.log('🎯 [ConfidenceEvaluator] 置信度评估器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听AI解析完成事件
        this.eventBus.on('ai:processing-complete', (data) => {
            this.handleParseComplete(data);
        });

        // 监听配置更新
        this.eventBus.on('confidence:config-updated', (config) => {
            this.updateConfig(config);
        });

        // 监听评估请求
        this.eventBus.on('confidence:evaluate-request', (data) => {
            this.handleEvaluationRequest(data);
        });
    }

    /**
     * 评估AI解析结果的置信度
     *
     * 执行多维度的质量评估，包括完整性、准确性、一致性等指标
     *
     * @param {Object} parseResult - AI解析结果对象
     * @param {Object} parseResult.data - 解析出的结构化数据
     * @param {string} parseResult.originalText - 原始输入文本
     * @param {number} parseResult.processingTime - 处理时间(毫秒)
     * @param {Object} options - 评估选项配置
     * @param {boolean} [options.includeFieldLevel=true] - 是否包含字段级评估
     * @param {boolean} [options.includeContextAnalysis=true] - 是否包含上下文分析
     * @param {boolean} [options.includeDetailedAnalysis=true] - 是否包含详细分析
     * @returns {Object} 完整的评估结果对象
     *
     * @example
     * const evaluation = evaluator.evaluateConfidence({
     *   data: { personalInfo: { name: 'John Doe' } },
     *   originalText: 'Name: John Doe',
     *   processingTime: 1500
     * });
     * console.log(`置信度: ${evaluation.overall}`);
     */
    evaluateConfidence(parseResult, options = {}) {
        try {
            console.log('🎯 [ConfidenceEvaluator] 开始置信度评估');

            // 解析和验证选项
            const config = this._parseEvaluationOptions(options);

            // 初始化评估结果结构
            const evaluation = this._initializeEvaluationResult(parseResult);

            // 执行核心评估组件
            this._performCoreEvaluations(evaluation, parseResult, config);

            // 执行可选评估组件
            this._performOptionalEvaluations(evaluation, parseResult, config);

            // 计算最终结果
            this._finalizeEvaluation(evaluation, parseResult, config);

            // 记录和发布评估结果
            this._recordEvaluationResult(evaluation);

            return evaluation;

        } catch (error) {
            console.error('❌ [ConfidenceEvaluator] 评估失败', error);
            return this._createErrorEvaluation(error, parseResult);
        }
    }

    /**
     * 解析评估选项
     * @private
     * @param {Object} options - 原始选项
     * @returns {Object} 解析后的配置
     */
    _parseEvaluationOptions(options) {
        return {
            includeFieldLevel: options.includeFieldLevel !== false && this.config.enableFieldLevelAnalysis,
            includeContextAnalysis: options.includeContextAnalysis !== false && this.config.enableContextAnalysis,
            includeDetailedAnalysis: options.includeDetailedAnalysis !== false && this.config.enableDetailedAnalysis
        };
    }

    /**
     * 初始化评估结果结构
     * @private
     * @param {Object} parseResult - 解析结果
     * @returns {Object} 初始化的评估结果
     */
    _initializeEvaluationResult(parseResult) {
        return {
            overall: 0,
            components: {},
            fieldLevel: {},
            analysis: {
                strengths: [],
                weaknesses: [],
                recommendations: []
            },
            metadata: {
                evaluatedAt: Date.now(),
                evaluationId: this.generateEvaluationId(),
                sourceType: this.detectSourceType(parseResult),
                version: '2.0.0'
            }
        };
    }

    /**
     * 执行核心评估组件
     * @private
     * @param {Object} evaluation - 评估结果对象
     * @param {Object} parseResult - 解析结果
     * @param {Object} config - 配置选项
     */
    _performCoreEvaluations(evaluation, parseResult, config) {
        console.log('📊 [ConfidenceEvaluator] 执行核心评估组件');

        // 数据完整性评估
        evaluation.components.completeness = this.evaluateCompleteness(parseResult.data);

        // 数据准确性评估
        evaluation.components.accuracy = this.evaluateAccuracy(parseResult.data, parseResult.originalText);

        // 字段一致性评估
        evaluation.components.consistency = this.evaluateConsistency(parseResult.data);

        // 源数据质量评估
        evaluation.components.sourceQuality = this.evaluateSourceQuality(parseResult);
    }

    /**
     * 执行可选评估组件
     * @private
     * @param {Object} evaluation - 评估结果对象
     * @param {Object} parseResult - 解析结果
     * @param {Object} config - 配置选项
     */
    _performOptionalEvaluations(evaluation, parseResult, config) {
        console.log('🔍 [ConfidenceEvaluator] 执行可选评估组件');

        // 上下文相关性评估
        if (config.includeContextAnalysis) {
            evaluation.components.contextRelevance = this.evaluateContextRelevance(
                parseResult.data,
                parseResult.originalText
            );
        }

        // 字段级别评估
        if (config.includeFieldLevel) {
            evaluation.fieldLevel = this.evaluateFieldLevel(parseResult.data);
        }
    }

    /**
     * 完成评估并计算最终结果
     * @private
     * @param {Object} evaluation - 评估结果对象
     * @param {Object} parseResult - 解析结果
     * @param {Object} config - 配置选项
     */
    _finalizeEvaluation(evaluation, parseResult, config) {
        console.log('🎯 [ConfidenceEvaluator] 计算最终评估结果');

        // 计算总体置信度
        evaluation.overall = this.calculateOverallConfidence(evaluation.components);

        // 详细分析
        if (config.includeDetailedAnalysis) {
            this.performDetailedAnalysis(evaluation, parseResult);
        }

        // 生成建议
        this.generateRecommendations(evaluation);

            // 10. 记录评估历史
            this.recordEvaluation(evaluation);

            console.log('✅ [ConfidenceEvaluator] 置信度评估完成', {
                overall: evaluation.overall,
                evaluationId: evaluation.metadata.evaluationId
            });

            // 发布评估完成事件
            if (this.eventBus) {
                this.eventBus.emit('confidence:evaluation-complete', evaluation);
            }

            return evaluation;

        } catch (error) {
            console.error('❌ [ConfidenceEvaluator] 置信度评估失败', error);
            
            const errorEvaluation = {
                overall: 0,
                error: error.message,
                metadata: {
                    evaluatedAt: Date.now(),
                    evaluationId: this.generateEvaluationId(),
                    failed: true
                }
            };

            // 发布评估错误事件
            if (this.eventBus) {
                this.eventBus.emit('confidence:evaluation-error', errorEvaluation);
            }

            return errorEvaluation;
        }
    }

    /**
     * 评估数据完整性
     * @param {Object} data - 解析的数据
     */
    evaluateCompleteness(data) {
        let totalFields = 0;
        let filledFields = 0;
        let weightedScore = 0;
        let totalWeight = 0;

        Object.entries(this.fieldImportance).forEach(([category, fields]) => {
            if (!data[category]) return;

            Object.entries(fields).forEach(([fieldName, weight]) => {
                totalFields++;
                totalWeight += weight;

                const value = data[category][fieldName];
                if (value !== undefined && value !== null && value !== '') {
                    filledFields++;
                    weightedScore += weight;
                }
            });
        });

        const completeness = {
            score: totalWeight > 0 ? weightedScore / totalWeight : 0,
            filledFields: filledFields,
            totalFields: totalFields,
            fillRate: totalFields > 0 ? filledFields / totalFields : 0,
            details: this.getCompletenessDetails(data)
        };

        return completeness;
    }

    /**
     * 评估数据准确性
     * @param {Object} data - 解析的数据
     * @param {string} originalText - 原始文本
     */
    evaluateAccuracy(data, originalText = '') {
        let accuracyScore = 0.8; // 基础分数
        const issues = [];

        // 使用数据验证器验证
        if (this.dataValidator) {
            try {
                const validation = this.dataValidator.validateData(data, { strict: false });
                
                if (validation.isValid) {
                    accuracyScore += 0.2;
                } else {
                    accuracyScore -= validation.errors.length * 0.1;
                    issues.push(...validation.errors);
                }

                if (validation.warnings.length > 0) {
                    accuracyScore -= validation.warnings.length * 0.05;
                    issues.push(...validation.warnings);
                }
            } catch (error) {
                accuracyScore -= 0.1;
                issues.push('数据验证失败');
            }
        }

        // 检查数据格式一致性
        const formatConsistency = this.checkFormatConsistency(data);
        accuracyScore += formatConsistency.score * 0.1;
        issues.push(...formatConsistency.issues);

        // 检查逻辑一致性
        const logicalConsistency = this.checkLogicalConsistency(data);
        accuracyScore += logicalConsistency.score * 0.1;
        issues.push(...logicalConsistency.issues);

        return {
            score: Math.max(0, Math.min(1, accuracyScore)),
            issues: issues,
            formatConsistency: formatConsistency,
            logicalConsistency: logicalConsistency
        };
    }

    /**
     * 评估字段一致性
     * @param {Object} data - 解析的数据
     */
    evaluateConsistency(data) {
        const consistencyChecks = [];
        let totalScore = 0;
        let checkCount = 0;

        // 检查日期一致性
        if (data.travelInfo?.entryDate && data.travelInfo?.exitDate) {
            const entryDate = new Date(data.travelInfo.entryDate.split('/').reverse().join('-'));
            const exitDate = new Date(data.travelInfo.exitDate.split('/').reverse().join('-'));
            
            const isConsistent = exitDate > entryDate;
            consistencyChecks.push({
                type: 'date_logic',
                consistent: isConsistent,
                message: isConsistent ? '入境和离境日期逻辑正确' : '离境日期应晚于入境日期'
            });
            
            totalScore += isConsistent ? 1 : 0;
            checkCount++;
        }

        // 检查交通方式一致性
        if (data.transportInfo) {
            const { flightNumber, vehicleNumber, modeOfTravel } = data.transportInfo;
            let isConsistent = true;
            let message = '';

            if (flightNumber && modeOfTravel && modeOfTravel !== 'AIR') {
                isConsistent = false;
                message = '有航班号但交通方式不是航空';
            } else if (vehicleNumber && modeOfTravel && modeOfTravel !== 'LAND') {
                isConsistent = false;
                message = '有车牌号但交通方式不是陆路';
            } else {
                message = '交通信息一致';
            }

            consistencyChecks.push({
                type: 'transport_logic',
                consistent: isConsistent,
                message: message
            });

            totalScore += isConsistent ? 1 : 0;
            checkCount++;
        }

        // 检查联系信息格式一致性
        if (data.contactInfo?.email) {
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const isValid = emailPattern.test(data.contactInfo.email);
            
            consistencyChecks.push({
                type: 'email_format',
                consistent: isValid,
                message: isValid ? '邮箱格式正确' : '邮箱格式不正确'
            });

            totalScore += isValid ? 1 : 0;
            checkCount++;
        }

        return {
            score: checkCount > 0 ? totalScore / checkCount : 1,
            checks: consistencyChecks,
            totalChecks: checkCount,
            passedChecks: totalScore
        };
    }

    /**
     * 评估上下文相关性
     * @param {Object} data - 解析的数据
     * @param {string} originalText - 原始文本
     */
    evaluateContextRelevance(data, originalText = '') {
        if (!originalText) {
            return { score: 0.5, reason: '无原始文本进行上下文分析' };
        }

        let relevanceScore = 0;
        let totalChecks = 0;
        const details = [];

        // 检查关键词匹配
        Object.entries(data).forEach(([category, fields]) => {
            if (!fields || typeof fields !== 'object') return;

            Object.entries(fields).forEach(([fieldName, value]) => {
                if (!value) return;

                totalChecks++;
                const valueStr = String(value).toLowerCase();
                const textLower = originalText.toLowerCase();

                // 检查值是否在原文中出现
                if (textLower.includes(valueStr)) {
                    relevanceScore++;
                    details.push({
                        field: `${category}.${fieldName}`,
                        value: value,
                        found: true,
                        confidence: 1.0
                    });
                } else {
                    // 检查部分匹配
                    const words = valueStr.split(/\s+/);
                    const matchedWords = words.filter(word => 
                        word.length > 2 && textLower.includes(word)
                    );
                    
                    if (matchedWords.length > 0) {
                        const partialScore = matchedWords.length / words.length;
                        relevanceScore += partialScore;
                        details.push({
                            field: `${category}.${fieldName}`,
                            value: value,
                            found: true,
                            confidence: partialScore,
                            matchedWords: matchedWords
                        });
                    } else {
                        details.push({
                            field: `${category}.${fieldName}`,
                            value: value,
                            found: false,
                            confidence: 0
                        });
                    }
                }
            });
        });

        return {
            score: totalChecks > 0 ? relevanceScore / totalChecks : 0,
            totalChecks: totalChecks,
            matchedFields: relevanceScore,
            details: details
        };
    }

    /**
     * 评估源数据质量
     * @param {Object} parseResult - 解析结果
     */
    evaluateSourceQuality(parseResult) {
        let qualityScore = 0.5; // 基础分数
        const factors = [];

        // 检查处理时间（快速处理通常质量更高）
        if (parseResult.processingTime) {
            if (parseResult.processingTime < 5000) {
                qualityScore += 0.1;
                factors.push('处理速度快');
            } else if (parseResult.processingTime > 15000) {
                qualityScore -= 0.1;
                factors.push('处理速度慢');
            }
        }

        // 检查是否有图片
        if (parseResult.hasImage) {
            qualityScore += 0.2;
            factors.push('包含图片信息');
        }

        // 检查原始文本长度
        if (parseResult.originalText) {
            const textLength = parseResult.originalText.length;
            if (textLength > 100 && textLength < 5000) {
                qualityScore += 0.1;
                factors.push('文本长度适中');
            } else if (textLength < 50) {
                qualityScore -= 0.2;
                factors.push('文本过短');
            } else if (textLength > 10000) {
                qualityScore -= 0.1;
                factors.push('文本过长');
            }
        }

        // 检查是否有解析错误
        if (parseResult.error) {
            qualityScore -= 0.3;
            factors.push('存在解析错误');
        }

        return {
            score: Math.max(0, Math.min(1, qualityScore)),
            factors: factors,
            processingTime: parseResult.processingTime,
            hasImage: parseResult.hasImage,
            textLength: parseResult.originalText?.length || 0
        };
    }

    /**
     * 字段级别评估
     * @param {Object} data - 解析的数据
     */
    evaluateFieldLevel(data) {
        const fieldEvaluations = {};

        Object.entries(data).forEach(([category, fields]) => {
            if (!fields || typeof fields !== 'object') return;

            fieldEvaluations[category] = {};

            Object.entries(fields).forEach(([fieldName, value]) => {
                const evaluation = this.evaluateSingleField(category, fieldName, value);
                fieldEvaluations[category][fieldName] = evaluation;
            });
        });

        return fieldEvaluations;
    }

    /**
     * 评估单个字段
     * @param {string} category - 类别
     * @param {string} fieldName - 字段名
     * @param {*} value - 字段值
     */
    evaluateSingleField(category, fieldName, value) {
        const evaluation = {
            confidence: 0,
            quality: 'unknown',
            issues: [],
            suggestions: []
        };

        if (!value || value === '') {
            evaluation.quality = 'empty';
            evaluation.confidence = 0;
            return evaluation;
        }

        // 基础置信度
        evaluation.confidence = 0.7;

        // 根据字段类型进行特定评估
        switch (fieldName) {
            case 'email':
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (emailPattern.test(value)) {
                    evaluation.confidence = 0.9;
                    evaluation.quality = 'high';
                } else {
                    evaluation.confidence = 0.3;
                    evaluation.quality = 'low';
                    evaluation.issues.push('邮箱格式不正确');
                }
                break;

            case 'phone':
                const phonePattern = /^\+?[\d\s\-\(\)]{8,15}$/;
                if (phonePattern.test(value)) {
                    evaluation.confidence = 0.8;
                    evaluation.quality = 'high';
                } else {
                    evaluation.confidence = 0.4;
                    evaluation.quality = 'medium';
                    evaluation.issues.push('电话格式可能不正确');
                }
                break;

            case 'passportNumber':
                const passportPattern = /^[A-Z][0-9]{8}$/;
                if (passportPattern.test(value)) {
                    evaluation.confidence = 0.9;
                    evaluation.quality = 'high';
                } else {
                    evaluation.confidence = 0.5;
                    evaluation.quality = 'medium';
                    evaluation.issues.push('护照号格式可能不正确');
                }
                break;

            case 'birthDate':
            case 'entryDate':
            case 'exitDate':
                const datePattern = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
                if (datePattern.test(value)) {
                    evaluation.confidence = 0.8;
                    evaluation.quality = 'high';
                } else {
                    evaluation.confidence = 0.4;
                    evaluation.quality = 'medium';
                    evaluation.issues.push('日期格式可能不正确');
                }
                break;

            default:
                // 通用评估
                if (value.length > 2 && value.length < 100) {
                    evaluation.confidence = 0.7;
                    evaluation.quality = 'medium';
                } else if (value.length <= 2) {
                    evaluation.confidence = 0.4;
                    evaluation.quality = 'low';
                    evaluation.issues.push('内容过短');
                } else {
                    evaluation.confidence = 0.6;
                    evaluation.quality = 'medium';
                    evaluation.issues.push('内容较长');
                }
        }

        return evaluation;
    }

    /**
     * 计算总体置信度
     * @param {Object} components - 各组件评分
     */
    calculateOverallConfidence(components) {
        let totalScore = 0;
        let totalWeight = 0;

        Object.entries(this.weights).forEach(([component, weight]) => {
            if (components[component] && components[component].score !== undefined) {
                totalScore += components[component].score * weight;
                totalWeight += weight;
            }
        });

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    /**
     * 执行详细分析
     * @param {Object} evaluation - 评估结果
     * @param {Object} parseResult - 解析结果
     */
    performDetailedAnalysis(evaluation, parseResult) {
        // 分析优势
        if (evaluation.components.completeness?.score > 0.8) {
            evaluation.analysis.strengths.push('数据完整性高');
        }
        if (evaluation.components.accuracy?.score > 0.8) {
            evaluation.analysis.strengths.push('数据准确性高');
        }
        if (evaluation.components.consistency?.score > 0.8) {
            evaluation.analysis.strengths.push('字段一致性好');
        }

        // 分析弱点
        if (evaluation.components.completeness?.score < 0.5) {
            evaluation.analysis.weaknesses.push('数据完整性不足');
        }
        if (evaluation.components.accuracy?.score < 0.5) {
            evaluation.analysis.weaknesses.push('数据准确性有待提高');
        }
        if (evaluation.components.consistency?.score < 0.5) {
            evaluation.analysis.weaknesses.push('字段一致性存在问题');
        }
    }

    /**
     * 生成建议
     * @param {Object} evaluation - 评估结果
     */
    generateRecommendations(evaluation) {
        if (evaluation.overall < this.config.warningThreshold) {
            evaluation.analysis.recommendations.push('建议手动检查和修正数据');
        }

        if (evaluation.components.completeness?.score < 0.6) {
            evaluation.analysis.recommendations.push('建议补充缺失的重要信息');
        }

        if (evaluation.components.accuracy?.issues?.length > 0) {
            evaluation.analysis.recommendations.push('建议验证和修正格式错误');
        }

        if (evaluation.overall > this.config.minConfidenceThreshold) {
            evaluation.analysis.recommendations.push('数据质量良好，可以直接使用');
        }
    }

    /**
     * 检查格式一致性
     * @param {Object} data - 数据
     */
    checkFormatConsistency(data) {
        const issues = [];
        let score = 1.0;

        // 检查日期格式
        const dateFields = [];
        if (data.personalInfo?.birthDate) dateFields.push(data.personalInfo.birthDate);
        if (data.travelInfo?.entryDate) dateFields.push(data.travelInfo.entryDate);
        if (data.travelInfo?.exitDate) dateFields.push(data.travelInfo.exitDate);

        const datePattern = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
        dateFields.forEach(date => {
            if (!datePattern.test(date)) {
                issues.push(`日期格式不一致: ${date}`);
                score -= 0.1;
            }
        });

        return { score: Math.max(0, score), issues };
    }

    /**
     * 检查逻辑一致性
     * @param {Object} data - 数据
     */
    checkLogicalConsistency(data) {
        const issues = [];
        let score = 1.0;

        // 检查年龄逻辑
        if (data.personalInfo?.birthDate) {
            const birthYear = parseInt(data.personalInfo.birthDate.split('/')[2]);
            const currentYear = new Date().getFullYear();
            const age = currentYear - birthYear;

            if (age < 0 || age > 120) {
                issues.push('出生日期不合理');
                score -= 0.2;
            }
        }

        return { score: Math.max(0, score), issues };
    }

    /**
     * 获取完整性详情
     * @param {Object} data - 数据
     */
    getCompletenessDetails(data) {
        const details = {};

        Object.entries(this.fieldImportance).forEach(([category, fields]) => {
            details[category] = {
                total: Object.keys(fields).length,
                filled: 0,
                missing: []
            };

            Object.keys(fields).forEach(fieldName => {
                const value = data[category]?.[fieldName];
                if (value !== undefined && value !== null && value !== '') {
                    details[category].filled++;
                } else {
                    details[category].missing.push(fieldName);
                }
            });
        });

        return details;
    }

    /**
     * 检测源数据类型
     * @param {Object} parseResult - 解析结果
     */
    detectSourceType(parseResult) {
        if (parseResult.hasImage && parseResult.originalText) {
            return 'mixed';
        } else if (parseResult.hasImage) {
            return 'image';
        } else if (parseResult.originalText) {
            return 'text';
        } else {
            return 'unknown';
        }
    }

    /**
     * 记录评估历史
     * @param {Object} evaluation - 评估结果
     */
    recordEvaluation(evaluation) {
        this.evaluationHistory.push({
            id: evaluation.metadata.evaluationId,
            timestamp: evaluation.metadata.evaluatedAt,
            overall: evaluation.overall,
            sourceType: evaluation.metadata.sourceType
        });

        // 限制历史记录大小
        if (this.evaluationHistory.length > this.maxHistorySize) {
            this.evaluationHistory.shift();
        }
    }

    /**
     * 处理解析完成事件
     * @param {Object} data - 解析结果
     */
    handleParseComplete(data) {
        // 自动评估置信度
        const evaluation = this.evaluateConfidence(data.result);
        
        // 如果置信度过低，发出警告
        if (evaluation.overall < this.config.warningThreshold) {
            if (this.eventBus) {
                this.eventBus.emit('confidence:low-confidence-warning', {
                    evaluation: evaluation,
                    threshold: this.config.warningThreshold
                });
            }
        }
    }

    /**
     * 处理评估请求事件
     * @param {Object} data - 请求数据
     */
    handleEvaluationRequest(data) {
        const { parseResult, options, callback } = data;
        
        try {
            const evaluation = this.evaluateConfidence(parseResult, options);
            
            if (callback && typeof callback === 'function') {
                callback(null, evaluation);
            }
        } catch (error) {
            if (callback && typeof callback === 'function') {
                callback(error, null);
            }
        }
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ [ConfidenceEvaluator] 配置已更新', this.config);
    }

    /**
     * 获取评估统计
     */
    getEvaluationStats() {
        if (this.evaluationHistory.length === 0) {
            return {
                totalEvaluations: 0,
                averageConfidence: 0,
                highConfidenceRate: 0,
                lowConfidenceRate: 0
            };
        }

        const totalEvaluations = this.evaluationHistory.length;
        const averageConfidence = this.evaluationHistory.reduce((sum, evaluation) => sum + evaluation.overall, 0) / totalEvaluations;
        const highConfidenceCount = this.evaluationHistory.filter(evaluation => evaluation.overall >= this.config.minConfidenceThreshold).length;
        const lowConfidenceCount = this.evaluationHistory.filter(evaluation => evaluation.overall < this.config.warningThreshold).length;

        return {
            totalEvaluations: totalEvaluations,
            averageConfidence: averageConfidence,
            highConfidenceRate: highConfidenceCount / totalEvaluations,
            lowConfidenceRate: lowConfidenceCount / totalEvaluations,
            recentEvaluations: this.evaluationHistory.slice(-10)
        };
    }

    /**
     * 生成评估ID
     */
    generateEvaluationId() {
        return 'eval_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 销毁置信度评估器
     */
    destroy() {
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('ai:processing-complete');
            this.eventBus.off('confidence:config-updated');
            this.eventBus.off('confidence:evaluate-request');
        }

        // 清理历史记录
        this.evaluationHistory = [];

        console.log('🗑️ [ConfidenceEvaluator] 置信度评估器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfidenceEvaluator;
} else {
    window.ConfidenceEvaluator = ConfidenceEvaluator;
}

console.log('✅ [ConfidenceEvaluator] 置信度评估器模块已加载');
