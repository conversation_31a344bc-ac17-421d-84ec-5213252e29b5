# MDAC Chrome扩展 - 模块化版本快速启动指南

## 🚀 快速开始

### 1. 选择启动方式

#### 方式A: 使用模块化版本 (推荐)
```html
<!-- 在sidepanel.html中使用 -->
<script src="ui-sidepanel-modular.js"></script>
```

#### 方式B: 使用原版本 (兼容性)
```html
<!-- 在sidepanel.html中使用 -->
<script src="ui-sidepanel.js"></script>
```

### 2. 验证加载状态

打开Chrome开发者工具，在控制台中检查：

```javascript
// 检查模块化版本是否加载
console.log(window.mdacModularSidePanel);

// 查看初始化状态
console.log(window.mdacModularSidePanel.getModuleStatus());

// 查看版本信息
console.log(window.mdacModularSidePanel.getVersionInfo());
```

### 3. 运行集成测试

```javascript
// 创建测试实例
const test = new ModularIntegrationTest();

// 运行所有测试
await test.runAllTests();

// 查看测试结果
console.log(test.getTestResults());
```

## 🔧 开发模式

### 启用调试模式

```javascript
// 方法1: 使用快捷键
// 按 Ctrl+Shift+D 切换调试模式

// 方法2: 编程方式
window.mdacModularSidePanel.toggleDebugMode();

// 方法3: URL参数
// 在URL中添加 ?debug=true
```

### 查看模块状态

```javascript
// 获取所有模块状态
const status = window.mdacModularSidePanel.getModuleStatus();
console.log('模块状态:', status);

// 检查特定模块
const aiService = window.mdacModularSidePanel.modules.aiService;
console.log('AI服务状态:', !!aiService);

// 查看失败的模块
console.log('失败的模块:', status.failedModules);
```

### 性能监控

```javascript
// 应用性能优化
const perfApplier = new PerformanceConfigApplier();
perfApplier.applyAllOptimizations();

// 查看内存使用
if (window.performance && window.performance.memory) {
    console.log('内存使用:', {
        used: Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(window.performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(window.performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
    });
}
```

## 🧪 测试功能

### 自动运行测试

在URL中添加参数自动运行测试：
```
chrome-extension://your-extension-id/ui/sidepanel/sidepanel.html?run-tests=true
```

### 手动运行特定测试

```javascript
const test = new ModularIntegrationTest();

// 只测试核心模块
await test.runTestSuite('coreModules');

// 只测试UI模块
await test.runTestSuite('uiModules');

// 只测试性能
await test.runTestSuite('performance');
```

### 查看测试报告

```javascript
// 从localStorage获取最新测试报告
const report = JSON.parse(localStorage.getItem('mdac_test_report'));
console.log('测试报告:', report);
```

## 🔍 故障排除

### 常见问题

#### 1. 模块加载失败
```javascript
// 检查失败的模块
const status = window.mdacModularSidePanel.getModuleStatus();
console.log('失败的模块:', status.failedModules);

// 尝试重新加载模块
window.mdacModularSidePanel.reloadModules();
```

#### 2. 初始化超时
```javascript
// 检查初始化进度
console.log('初始化进度:', status.progress + '%');

// 如果卡住，尝试降级模式
window.mdacModularSidePanel.enterDegradedMode();
```

#### 3. 内存使用过高
```javascript
// 手动触发垃圾回收
if (window.gc) {
    window.gc();
}

// 清理缓存
window.mdacModularSidePanel.modules.storageService.clearCache();
```

#### 4. 事件系统问题
```javascript
// 检查事件管理器状态
const eventManager = window.mdacModularSidePanel.modules.eventManager;
console.log('事件管理器:', eventManager);

// 查看注册的事件
console.log('注册的事件:', eventManager.getRegisteredEvents());
```

### 错误日志

```javascript
// 查看错误日志
const logger = window.mdacModularSidePanel.modules.debugLogger;
const errorLogs = logger.getLogs({ level: 'ERROR' });
console.log('错误日志:', errorLogs);

// 导出日志用于分析
logger.exportLogs('json');
```

### 重置系统

```javascript
// 完全重置（谨慎使用）
window.mdacModularSidePanel.destroy();

// 清除所有存储数据
localStorage.clear();
sessionStorage.clear();

// 刷新页面
location.reload();
```

## 📊 监控和分析

### 实时监控

```javascript
// 启用实时性能监控
const logger = window.mdacModularSidePanel.modules.debugLogger;
logger.updateConfig({
    enablePerformanceMonitoring: true,
    monitoringInterval: 5000
});

// 监听性能事件
window.mdacModularSidePanel.modules.eventManager.on('debug:performance', (data) => {
    console.log('性能数据:', data);
});
```

### 用户行为分析

```javascript
// 启用用户行为跟踪
logger.updateConfig({
    enableUserActions: true
});

// 查看用户行为日志
const userActions = logger.userActionLogs;
console.log('用户行为:', userActions);
```

### 数据导出

```javascript
// 导出所有调试数据
logger.exportLogs('json', {
    includePerformance: true,
    includeUserActions: true
});

// 导出模块状态
const moduleStatus = window.mdacModularSidePanel.getModuleStatus();
console.log('模块状态导出:', JSON.stringify(moduleStatus, null, 2));
```

## 🛠️ 开发工具

### Chrome DevTools 扩展

1. 打开Chrome DevTools (F12)
2. 切换到Console标签
3. 使用以下命令：

```javascript
// 全局调试对象
window.MDAC_DEBUG = {
    sidePanel: window.mdacModularSidePanel,
    test: new ModularIntegrationTest(),
    perf: new PerformanceConfigApplier()
};

// 快速访问
console.log('MDAC调试工具已准备就绪');
console.log('使用 MDAC_DEBUG 对象访问调试功能');
```

### 快捷键

- `Ctrl+Shift+D`: 切换调试模式
- `Ctrl+Shift+R`: 重新加载模块
- `Ctrl+Shift+T`: 运行测试
- `Ctrl+Shift+C`: 清除缓存

### 调试面板

模块化版本提供了内置的调试面板：

```javascript
// 显示调试面板
window.mdacModularSidePanel.modules.uiRenderer.showDebugPanel();

// 隐藏调试面板
window.mdacModularSidePanel.modules.uiRenderer.hideDebugPanel();
```

## 📝 最佳实践

### 1. 开发流程
1. 启用调试模式
2. 运行集成测试
3. 监控性能指标
4. 开发新功能
5. 运行回归测试
6. 性能优化
7. 部署前测试

### 2. 性能优化
- 定期运行性能测试
- 监控内存使用情况
- 优化事件监听器
- 使用懒加载策略
- 及时清理无用资源

### 3. 错误处理
- 启用错误追踪
- 定期查看错误日志
- 实现优雅降级
- 提供用户友好的错误提示

### 4. 测试策略
- 每次修改后运行测试
- 定期运行完整测试套件
- 监控测试覆盖率
- 及时修复失败的测试

## 🆘 获取帮助

### 查看文档
- `MODULAR_REFACTOR_SUMMARY.md`: 重构总结
- 各模块文件头部注释: 详细API文档

### 调试信息
```javascript
// 获取系统信息
console.log('系统信息:', {
    version: window.mdacModularSidePanel.getVersionInfo(),
    status: window.mdacModularSidePanel.getModuleStatus(),
    performance: window.performance.memory,
    userAgent: navigator.userAgent
});
```

### 常用命令速查

```javascript
// 快速诊断
const diagnose = () => {
    const sp = window.mdacModularSidePanel;
    return {
        initialized: sp.initializationState.isInitialized,
        moduleCount: Object.values(sp.modules).filter(Boolean).length,
        failedModules: sp.initializationState.failedModules,
        memoryUsage: window.performance.memory?.usedJSHeapSize,
        debugMode: sp.config.enableDebugMode
    };
};

console.log('系统诊断:', diagnose());
```

---

**提示**: 如果遇到问题，请先运行诊断命令，然后查看控制台错误信息。大多数问题都可以通过重新加载模块或重置系统来解决。
