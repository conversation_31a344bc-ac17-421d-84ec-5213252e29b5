/**
 * 模块加载器 - 动态加载和管理模块
 * 负责按依赖顺序加载模块，处理模块间依赖关系
 * 创建日期: 2025-01-11
 */

class ModuleLoader {
    constructor() {
        // 已加载的模块
        this.loadedModules = new Map();
        // 模块依赖关系
        this.dependencies = new Map();
        // 加载状态
        this.loadingStatus = new Map();
        // 模块配置
        this.moduleConfig = new Map();
        
        console.log('📦 [ModuleLoader] 模块加载器已初始化');
    }

    /**
     * 注册模块配置
     * @param {string} moduleName - 模块名称
     * @param {Object} config - 模块配置
     */
    registerModule(moduleName, config) {
        const moduleConfig = {
            path: config.path,
            dependencies: config.dependencies || [],
            lazy: config.lazy || false,
            timeout: config.timeout || 10000,
            retries: config.retries || 3,
            ...config
        };

        this.moduleConfig.set(moduleName, moduleConfig);
        this.dependencies.set(moduleName, moduleConfig.dependencies);

        console.log(`📋 [ModuleLoader] 注册模块: ${moduleName}`, moduleConfig);
    }

    /**
     * 加载单个模块
     * @param {string} moduleName - 模块名称
     * @param {Object} options - 加载选项
     */
    async loadModule(moduleName, options = {}) {
        // 检查是否已加载
        if (this.loadedModules.has(moduleName)) {
            console.log(`✅ [ModuleLoader] 模块已加载: ${moduleName}`);
            return this.loadedModules.get(moduleName);
        }

        // 检查是否正在加载
        if (this.loadingStatus.has(moduleName)) {
            console.log(`⏳ [ModuleLoader] 等待模块加载: ${moduleName}`);
            return this.loadingStatus.get(moduleName);
        }

        // 获取模块配置
        const config = this.moduleConfig.get(moduleName);
        if (!config) {
            throw new Error(`模块配置未找到: ${moduleName}`);
        }

        // 创建加载Promise
        const loadingPromise = this.performModuleLoad(moduleName, config, options);
        this.loadingStatus.set(moduleName, loadingPromise);

        try {
            const module = await loadingPromise;
            this.loadedModules.set(moduleName, module);
            this.loadingStatus.delete(moduleName);
            
            console.log(`✅ [ModuleLoader] 模块加载成功: ${moduleName}`);
            
            // 发布模块加载完成事件
            if (window.mdacEventBus) {
                window.mdacEventBus.emit('module:loaded', { moduleName, module });
            }
            
            return module;
        } catch (error) {
            this.loadingStatus.delete(moduleName);
            console.error(`❌ [ModuleLoader] 模块加载失败: ${moduleName}`, error);
            throw error;
        }
    }

    /**
     * 执行模块加载
     * @param {string} moduleName - 模块名称
     * @param {Object} config - 模块配置
     * @param {Object} options - 加载选项
     */
    async performModuleLoad(moduleName, config, options) {
        console.log(`📥 [ModuleLoader] 开始加载模块: ${moduleName}`);

        // 首先加载依赖模块
        await this.loadDependencies(moduleName);

        // 加载模块脚本
        const scriptUrl = chrome.runtime.getURL(config.path);
        
        let retries = config.retries;
        let lastError;

        while (retries > 0) {
            try {
                await this.loadScript(scriptUrl, config.timeout);
                
                // 验证模块是否正确加载
                const module = await this.validateModule(moduleName, config);
                return module;
                
            } catch (error) {
                lastError = error;
                retries--;
                
                if (retries > 0) {
                    console.warn(`⚠️ [ModuleLoader] 模块加载失败，重试: ${moduleName} (剩余${retries}次)`);
                    await this.delay(1000); // 等待1秒后重试
                }
            }
        }

        throw new Error(`模块加载失败，已重试${config.retries}次: ${moduleName} - ${lastError.message}`);
    }

    /**
     * 加载依赖模块
     * @param {string} moduleName - 模块名称
     */
    async loadDependencies(moduleName) {
        const dependencies = this.dependencies.get(moduleName) || [];
        
        if (dependencies.length === 0) {
            return;
        }

        console.log(`🔗 [ModuleLoader] 加载依赖模块: ${moduleName} -> [${dependencies.join(', ')}]`);

        // 并行加载所有依赖
        await Promise.all(
            dependencies.map(dep => this.loadModule(dep))
        );
    }

    /**
     * 动态加载脚本
     * @param {string} src - 脚本URL
     * @param {number} timeout - 超时时间
     */
    loadScript(src, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.type = 'text/javascript';

            // 设置超时
            const timeoutId = setTimeout(() => {
                script.remove();
                reject(new Error(`脚本加载超时: ${src}`));
            }, timeout);

            script.onload = () => {
                clearTimeout(timeoutId);
                console.log(`📜 [ModuleLoader] 脚本加载成功: ${src}`);
                resolve();
            };

            script.onerror = (error) => {
                clearTimeout(timeoutId);
                script.remove();
                reject(new Error(`脚本加载失败: ${src} - ${error.message}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * 验证模块是否正确加载
     * @param {string} moduleName - 模块名称
     * @param {Object} config - 模块配置
     */
    async validateModule(moduleName, config) {
        // 等待模块初始化
        await this.delay(100);

        // 检查全局变量
        if (config.globalName) {
            const module = window[config.globalName];
            if (!module) {
                throw new Error(`模块全局变量未找到: ${config.globalName}`);
            }
            return module;
        }

        // 检查模块导出
        if (config.validator && typeof config.validator === 'function') {
            const isValid = await config.validator();
            if (!isValid) {
                throw new Error(`模块验证失败: ${moduleName}`);
            }
        }

        return true;
    }

    /**
     * 批量加载模块
     * @param {Array} moduleNames - 模块名称数组
     * @param {Object} options - 加载选项
     */
    async loadModules(moduleNames, options = {}) {
        const { parallel = false, stopOnError = true } = options;

        console.log(`📦 [ModuleLoader] 批量加载模块: [${moduleNames.join(', ')}]`, { parallel, stopOnError });

        if (parallel) {
            // 并行加载
            const results = await Promise.allSettled(
                moduleNames.map(name => this.loadModule(name))
            );

            const failures = results.filter(r => r.status === 'rejected');
            if (failures.length > 0 && stopOnError) {
                throw new Error(`批量加载失败: ${failures.map(f => f.reason.message).join(', ')}`);
            }

            return results.map(r => r.status === 'fulfilled' ? r.value : null);
        } else {
            // 串行加载
            const results = [];
            for (const moduleName of moduleNames) {
                try {
                    const module = await this.loadModule(moduleName);
                    results.push(module);
                } catch (error) {
                    if (stopOnError) {
                        throw error;
                    }
                    results.push(null);
                }
            }
            return results;
        }
    }

    /**
     * 卸载模块
     * @param {string} moduleName - 模块名称
     */
    unloadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            const module = this.loadedModules.get(moduleName);
            
            // 调用模块的清理方法
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }

            this.loadedModules.delete(moduleName);
            console.log(`🗑️ [ModuleLoader] 模块已卸载: ${moduleName}`);

            // 发布模块卸载事件
            if (window.mdacEventBus) {
                window.mdacEventBus.emit('module:unloaded', { moduleName });
            }
        }
    }

    /**
     * 获取加载状态
     */
    getLoadStatus() {
        return {
            loaded: Array.from(this.loadedModules.keys()),
            loading: Array.from(this.loadingStatus.keys()),
            registered: Array.from(this.moduleConfig.keys())
        };
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清理所有模块
     */
    cleanup() {
        // 卸载所有模块
        for (const moduleName of this.loadedModules.keys()) {
            this.unloadModule(moduleName);
        }

        // 清理状态
        this.loadingStatus.clear();
        this.dependencies.clear();
        this.moduleConfig.clear();

        console.log('🧹 [ModuleLoader] 模块加载器已清理');
    }
}

// 创建全局模块加载器实例
window.mdacModuleLoader = new ModuleLoader();

// 导出类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModuleLoader, moduleLoader: window.mdacModuleLoader };
} else {
    window.ModuleLoader = ModuleLoader;
}

console.log('✅ [ModuleLoader] 模块加载器已加载');
