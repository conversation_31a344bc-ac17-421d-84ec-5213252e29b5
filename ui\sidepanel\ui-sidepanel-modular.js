/**
 * MDAC Chrome扩展 - 模块化侧边栏主文件
 * 集成所有模块，提供统一的初始化和管理
 * 创建日期: 2025-01-11
 * 修复日期: 2025-07-12
 * 版本: 2.0.0 (模块化重构版本)
 */

// 防止重复声明的新方法 - 使用简单检查
(function() {
    'use strict';

    // 如果MDACModularSidePanel已存在且功能正常，则跳过
    if (typeof window.MDACModularSidePanel === 'function') {
        console.log('✅ [MDACModularSidePanel] 已存在且正常，跳过重新定义');
        return;
    }

    class MDACModularSidePanel {
    constructor() {
        // 版本信息
        this.version = '2.0.0';
        this.buildDate = '2025-01-11';
        
        // 模块实例
        this.modules = {
            // 核心模块
            eventManager: null,
            stateManager: null,
            moduleLoader: null,
            moduleRegistry: null,
            sidePanelCore: null,
            moduleInitializer: null,
            
            // AI功能模块
            aiService: null,
            textParser: null,
            imageProcessor: null,
            
            // 表单处理模块
            formFiller: null,
            fieldMatcher: null,
            dataValidator: null,
            
            // UI组件模块
            uiRenderer: null,
            modalManager: null,
            progressVisualizer: null,
            
            // 特色功能模块
            autoParseManager: null,
            confidenceEvaluator: null,
            cityViewer: null,
            
            // 数据管理模块
            dataManager: null,
            storageService: null,
            previewManager: null,
            
            // 工具模块
            dateFormatter: null,
            messageHelper: null,
            debugLogger: null,

            // 兼容性适配器
            legacyAdapter: null
        };

        // 初始化状态
        this.initializationState = {
            isInitializing: false,
            isInitialized: false,
            initializationProgress: 0,
            failedModules: [],
            initializationTime: null
        };

        // 配置
        this.config = {
            enableDebugMode: false,
            enablePerformanceMonitoring: true,
            enableErrorReporting: true,
            moduleLoadTimeout: 30000,
            retryFailedModules: true,
            maxRetryAttempts: 3
        };

        console.log('🚀 [MDACModularSidePanel] 模块化侧边栏初始化开始', {
            version: this.version,
            buildDate: this.buildDate
        });

        console.log('🏗️ [MDACModularSidePanel] 模块化侧边栏实例已创建，等待手动初始化');
    }

    /**
     * 等待DOM完全加载
     */
    async waitForDOMReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                // DOM已经加载完成，等待一小段时间确保所有元素都已渲染
                setTimeout(resolve, 100);
            } else {
                // DOM还在加载，等待DOMContentLoaded事件
                document.addEventListener('DOMContentLoaded', () => {
                    setTimeout(resolve, 100);
                }, { once: true });
            }
        });
    }

    /**
     * 初始化模块化侧边栏
     */
    async initialize() {
        if (this.initializationState.isInitializing || this.initializationState.isInitialized) {
            console.warn('⚠️ [MDACModularSidePanel] 已经在初始化或已初始化');
            return;
        }

        try {
            this.initializationState.isInitializing = true;
            this.initializationState.initializationTime = Date.now();

            console.log('🔧 [MDACModularSidePanel] 开始模块化初始化');

            // 检查必需模块是否已加载
            const requiredModules = ['EventBus', 'EventManager', 'StateManager', 'ModuleRegistry', 'SidePanelCore'];
            const missingModules = requiredModules.filter(module => !window[module]);
            
            if (missingModules.length > 0) {
                console.error(`❌ [MDACModularSidePanel] 必需模块未加载: ${missingModules.join(', ')}`);
                console.log('🔍 [MDACModularSidePanel] 可用的全局模块:', Object.keys(window).filter(key => 
                    ['EventBus', 'EventManager', 'StateManager', 'ModuleRegistry', 'SidePanelCore', 'ModuleLoader'].includes(key)
                ));
                
                // 尝试等待模块加载
                console.log('⏳ [MDACModularSidePanel] 等待缺失模块加载...');
                const maxWaitTime = 10000; // 10秒
                const startTime = Date.now();
                
                while (Date.now() - startTime < maxWaitTime) {
                    const stillMissing = requiredModules.filter(module => !window[module]);
                    if (stillMissing.length === 0) {
                        console.log('✅ [MDACModularSidePanel] 所有必需模块现已可用');
                        break;
                    }
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                // 最终检查
                const finalMissing = requiredModules.filter(module => !window[module]);
                if (finalMissing.length > 0) {
                    throw new Error(`必需模块仍然未加载: ${finalMissing.join(', ')}`);
                }
            }
            
            console.log('✅ [MDACModularSidePanel] 所有必需模块已确认加载');

            // 等待DOM完全加载
            await this.waitForDOMReady();
            console.log('✅ [MDACModularSidePanel] DOM已就绪');

            // 阶段1: 初始化核心模块
            await this.initializeCoreModules();
            this.updateProgress(20);

            // 阶段2: 初始化工具模块
            await this.initializeUtilityModules();
            this.updateProgress(40);

            // 阶段3: 初始化数据管理模块
            await this.initializeDataModules();
            this.updateProgress(60);

            // 阶段4: 初始化用户设置
            await this.initializeSettings();
            this.updateProgress(65);

            // 阶段5: 初始化功能模块
            await this.initializeFunctionalModules();
            this.updateProgress(80);

            // 阶段6: 初始化UI模块
            await this.initializeUIModules();
            this.updateProgress(90);

            // 阶段7: 完成初始化
            await this.finalizeInitialization();
            this.updateProgress(100);

            this.initializationState.isInitialized = true;
            this.initializationState.isInitializing = false;

            const initTime = Date.now() - this.initializationState.initializationTime;
            console.log('✅ [MDACModularSidePanel] 模块化初始化完成', {
                initializationTime: initTime,
                totalModules: Object.keys(this.modules).length,
                failedModules: this.initializationState.failedModules.length
            });

            // 发布初始化完成事件
            if (this.modules.eventManager) {
                this.modules.eventManager.emit('sidepanel:initialized', {
                    version: this.version,
                    initializationTime: initTime,
                    modules: Object.keys(this.modules),
                    failedModules: this.initializationState.failedModules
                });
            }

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 初始化失败', error);
            this.initializationState.isInitializing = false;
            this.handleInitializationError(error);
            throw error;
        }
    }

    /**
     * 初始化核心模块
     */
    async initializeCoreModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化核心模块');

        try {
            // 首先确保EventBus可用
            if (!window.mdacEventBus) {
                window.mdacEventBus = new EventBus();
                console.log('🚌 [MDACModularSidePanel] 创建全局事件总线');
            }

            // 模块注册器 - 最先初始化，提供模块配置
            this.modules.moduleRegistry = new ModuleRegistry();
            this.modules.moduleRegistry.initializeModuleDefinitions();
            window.mdacModuleRegistry = this.modules.moduleRegistry;
            console.log('📋 [MDACModularSidePanel] 模块注册器已初始化');

            // 模块加载器 - 第二个初始化
            this.modules.moduleLoader = new ModuleLoader();
            window.mdacModuleLoader = this.modules.moduleLoader;
            console.log('📦 [MDACModularSidePanel] 模块加载器已初始化');

            // 状态管理器 - 第三个初始化
            this.modules.stateManager = new StateManager(window.mdacEventBus);
            console.log('🗂️ [MDACModularSidePanel] 状态管理器已初始化');

            // 事件管理器 - 第四个初始化，传递this作为sidePanelInstance
            this.modules.eventManager = new EventManager(this, window.mdacEventBus);
            await this.modules.eventManager.initialize();
            console.log('🎯 [MDACModularSidePanel] 事件管理器已初始化');

            // 侧边栏核心 - 最后初始化，依赖前面所有模块
            this.modules.sidePanelCore = new SidePanelCore();
            await this.modules.sidePanelCore.initialize();
            console.log('🏗️ [MDACModularSidePanel] 侧边栏核心已初始化');

            // 模块初始化器
            this.modules.moduleInitializer = new ModuleInitializer();
            console.log('🔧 [MDACModularSidePanel] 模块初始化器已创建');

            console.log('✅ [MDACModularSidePanel] 核心模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 核心模块初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化工具模块
     */
    async initializeUtilityModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化工具模块');

        try {
            // 调试日志器
            this.modules.debugLogger = new DebugLogger(window.mdacEventBus);
            
            // 日期格式化器
            this.modules.dateFormatter = new DateFormatter();
            
            // 消息助手
            this.modules.messageHelper = new MessageHelper(window.mdacEventBus);

            console.log('✅ [MDACModularSidePanel] 工具模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 工具模块初始化失败', error);
            this.initializationState.failedModules.push('utilities');
        }
    }

    /**
     * 初始化数据管理模块
     */
    async initializeDataModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化数据管理模块');

        try {
            // 存储服务
            this.modules.storageService = new StorageService(window.mdacEventBus);
            
            // 数据管理器
            this.modules.dataManager = new DataManager(
                window.mdacEventBus,
                this.modules.storageService,
                this.modules.stateManager
            );
            await this.modules.dataManager.initialize();

            // 预览管理器
            this.modules.previewManager = new PreviewManager(
                window.mdacEventBus,
                null, // modalManager将在UI模块中初始化
                this.modules.dataManager
            );

            console.log('✅ [MDACModularSidePanel] 数据管理模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 数据管理模块初始化失败', error);
            this.initializationState.failedModules.push('data');
        }
    }

    /**
     * 初始化功能模块
     */
    async initializeFunctionalModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化功能模块');

        try {
            // AI功能模块
            this.modules.textParser = new TextParser(window.mdacEventBus);
            this.modules.imageProcessor = new ImageProcessor(window.mdacEventBus);
            this.modules.aiService = new AIService(
                window.mdacEventBus,
                this.modules.textParser,
                this.modules.imageProcessor
            );
            await this.modules.aiService.initialize();

            // 表单处理模块
            this.modules.dataValidator = new DataValidator(window.mdacEventBus);
            this.modules.fieldMatcher = new FieldMatcher(window.mdacEventBus);
            this.modules.formFiller = new FormFiller(
                window.mdacEventBus,
                this.modules.fieldMatcher,
                this.modules.dataValidator,
                this.modules.messageHelper
            );
            await this.modules.formFiller.initialize();

            // 特色功能模块
            this.modules.confidenceEvaluator = new ConfidenceEvaluator(
                window.mdacEventBus,
                this.modules.dataValidator
            );

            this.modules.autoParseManager = new AutoParseManager(
                window.mdacEventBus,
                this.modules.stateManager,
                this.modules.aiService
            );

            this.modules.cityViewer = new CityViewer(
                window.mdacEventBus,
                null // modalManager将在UI模块中设置
            );
            await this.modules.cityViewer.initialize();

            console.log('✅ [MDACModularSidePanel] 功能模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 功能模块初始化失败', error);
            this.initializationState.failedModules.push('functional');
        }
    }

    /**
     * 初始化UI模块
     */
    async initializeUIModules() {
        console.log('🔧 [MDACModularSidePanel] 初始化UI模块');

        try {
            // 进度可视化器
            this.modules.progressVisualizer = new ProgressVisualizer(window.mdacEventBus);

            // 模态框管理器
            this.modules.modalManager = new ModalManager(
                window.mdacEventBus,
                this.modules.messageHelper
            );

            // UI渲染器
            this.modules.uiRenderer = new UIRenderer(
                window.mdacEventBus,
                this.modules.stateManager,
                this.modules.modalManager,
                this.modules.progressVisualizer
            );
            await this.modules.uiRenderer.initialize();

            // 更新其他模块的modalManager引用
            if (this.modules.previewManager) {
                this.modules.previewManager.modalManager = this.modules.modalManager;
            }
            if (this.modules.cityViewer) {
                this.modules.cityViewer.modalManager = this.modules.modalManager;
            }

            console.log('✅ [MDACModularSidePanel] UI模块初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] UI模块初始化失败', error);
            this.initializationState.failedModules.push('ui');
        }
    }

    /**
     * 完成初始化
     */
    async finalizeInitialization() {
        console.log('🔧 [MDACModularSidePanel] 完成初始化');

        try {
            // 启动自动解析管理器
            if (this.modules.autoParseManager) {
                this.modules.autoParseManager.start();
            }

            // 设置全局错误处理
            this.setupGlobalErrorHandling();

            // 设置性能监控
            if (this.config.enablePerformanceMonitoring) {
                this.setupPerformanceMonitoring();
            }

            // 注册全局快捷键
            this.setupGlobalShortcuts();

            // 设置模块间通信
            this.setupModuleCommunication();

            // 初始化兼容性适配器
            this.initializeLegacyAdapter();

            // 初始化用户设置
            await this.initializeSettings();

            console.log('✅ [MDACModularSidePanel] 初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 完成初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化用户设置
     * 从Chrome存储中加载用户配置和自动解析设置
     */
    async initializeSettings() {
        console.log('⚙️ [MDACModularSidePanel] 初始化用户设置');

        try {
            // 加载用户设置
            const result = await chrome.storage.sync.get(['mdacSettings']);
            const userSettings = result.mdacSettings || {};
            
            // 存储到状态管理器
            if (this.modules.stateManager) {
                this.modules.stateManager.set('settings.user', userSettings);
            }

            // 加载自动解析设置
            const autoParseResult = await chrome.storage.local.get(['mdac_auto_parse_settings']);
            const autoParseSettings = autoParseResult.mdac_auto_parse_settings || {
                personal: true,
                travel: true,
                delay: 3000
            };

            // 存储自动解析设置
            if (this.modules.stateManager) {
                this.modules.stateManager.set('settings.autoParse', autoParseSettings);
            }

            // 更新UI状态
            this.updateAutoParseUI(autoParseSettings);

            console.log('✅ [MDACModularSidePanel] 用户设置初始化完成', {
                userSettings,
                autoParseSettings
            });

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 设置初始化失败', error);
        }
    }

    /**
     * 更新自动解析UI状态
     * @param {Object} autoParseSettings - 自动解析设置
     */
    updateAutoParseUI(autoParseSettings) {
        try {
            // 修正ID名称（使用下划线而不是空格）
            const personalToggle = document.getElementById('autoParsePersonalEnabled');
            const travelToggle = document.getElementById('autoParseTravel_Enabled');

            if (personalToggle) {
                personalToggle.checked = autoParseSettings.personal;
            }
            if (travelToggle) {
                travelToggle.checked = autoParseSettings.travel;
            }

            console.log('🔄 [MDACModularSidePanel] 自动解析UI状态已更新');

        } catch (error) {
            console.warn('⚠️ [MDACModularSidePanel] 更新自动解析UI失败', error);
        }
    }

    /**
     * 更新初始化进度
     * @param {number} progress - 进度百分比
     */
    updateProgress(progress) {
        this.initializationState.initializationProgress = progress;
        
        if (this.modules.eventManager) {
            this.modules.eventManager.emit('sidepanel:initialization-progress', {
                progress: progress,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error('Global Error', event.error, {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error('Unhandled Promise Rejection', event.reason);
            }
        });
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        if (this.modules.debugLogger) {
            // 监控模块加载性能
            this.modules.debugLogger.startPerformanceMark('module-initialization');
            
            // 定期收集性能指标
            setInterval(() => {
                this.collectPerformanceMetrics();
            }, 60000); // 每分钟收集一次
        }
    }

    /**
     * 收集性能指标
     */
    collectPerformanceMetrics() {
        if (!this.modules.debugLogger) return;

        const metrics = {
            memoryUsage: this.modules.debugLogger.getMemoryUsage(),
            moduleCount: Object.keys(this.modules).length,
            activeModules: Object.values(this.modules).filter(Boolean).length,
            timestamp: Date.now()
        };

        this.modules.debugLogger.logPerformance('System Metrics', metrics);
    }

    /**
     * 设置全局快捷键
     */
    setupGlobalShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+D: 切换调试模式
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                this.toggleDebugMode();
                event.preventDefault();
            }

            // Ctrl+Shift+R: 重新加载模块
            if (event.ctrlKey && event.shiftKey && event.key === 'R') {
                this.reloadModules();
                event.preventDefault();
            }
        });
    }

    /**
     * 设置模块间通信
     */
    setupModuleCommunication() {
        if (!this.modules.eventManager) return;

        // 设置模块状态同步
        this.modules.eventManager.on('module:status-changed', (data) => {
            if (this.modules.stateManager) {
                this.modules.stateManager.set(`modules.${data.moduleName}.status`, data.status);
            }
        });

        // 设置错误报告
        this.modules.eventManager.on('module:error', (data) => {
            if (this.modules.debugLogger) {
                this.modules.debugLogger.error(`Module Error: ${data.moduleName}`, data.error);
            }
        });
    }

    /**
     * 初始化兼容性适配器
     */
    initializeLegacyAdapter() {
        try {
            console.log('🔄 [MDACModularSidePanel] 初始化兼容性适配器');

            // 创建兼容性适配器实例
            this.modules.legacyAdapter = new LegacyAdapter(this);

            // 获取兼容性报告
            const compatibilityReport = this.modules.legacyAdapter.getCompatibilityReport();
            console.log('📊 [MDACModularSidePanel] 兼容性报告', compatibilityReport);

            // 如果兼容性低于80%，发出警告
            if (compatibilityReport.compatibilityRate < 0.8) {
                console.warn('⚠️ [MDACModularSidePanel] 兼容性较低，部分旧功能可能不可用');

                if (this.modules.debugLogger) {
                    this.modules.debugLogger.warn('兼容性较低', {
                        compatibilityRate: compatibilityReport.compatibilityRate,
                        incompatible: compatibilityReport.incompatible
                    });
                }
            }

            console.log('✅ [MDACModularSidePanel] 兼容性适配器初始化完成');

        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 兼容性适配器初始化失败', error);

            // 即使适配器失败，也不应该阻止主系统运行
            this.modules.legacyAdapter = null;
        }
    }

    /**
     * 切换调试模式
     */
    toggleDebugMode() {
        this.config.enableDebugMode = !this.config.enableDebugMode;
        
        if (this.modules.debugLogger) {
            this.modules.debugLogger.updateConfig({
                level: this.config.enableDebugMode ? 'DEBUG' : 'INFO'
            });
        }

        console.log(`🐛 [MDACModularSidePanel] 调试模式: ${this.config.enableDebugMode ? '启用' : '禁用'}`);
    }

    /**
     * 重新加载模块
     */
    async reloadModules() {
        console.log('🔄 [MDACModularSidePanel] 重新加载模块');
        
        try {
            // 这里可以实现模块的热重载逻辑
            if (this.modules.moduleLoader) {
                await this.modules.moduleLoader.reloadModules();
            }
        } catch (error) {
            console.error('❌ [MDACModularSidePanel] 模块重新加载失败', error);
        }
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError(error) {
        console.error('❌ [MDACModularSidePanel] 初始化错误处理', error);

        // 尝试降级模式
        this.enterDegradedMode();
    }

    /**
     * 进入降级模式
     */
    enterDegradedMode() {
        console.warn('⚠️ [MDACModularSidePanel] 进入降级模式');

        // 只保留核心功能
        const essentialModules = ['eventManager', 'stateManager', 'debugLogger'];
        
        Object.keys(this.modules).forEach(moduleName => {
            if (!essentialModules.includes(moduleName)) {
                this.modules[moduleName] = null;
            }
        });

        // 显示降级模式提示
        if (this.modules.messageHelper) {
            this.modules.messageHelper.error(
                '系统运行在降级模式',
                '部分功能可能不可用，请刷新页面重试'
            );
        }
    }

    /**
     * 获取模块状态
     */
    getModuleStatus() {
        const status = {};
        
        Object.entries(this.modules).forEach(([name, module]) => {
            status[name] = {
                loaded: !!module,
                initialized: module && typeof module.initialize === 'function',
                hasError: this.initializationState.failedModules.includes(name)
            };
        });

        return {
            overall: this.initializationState.isInitialized,
            progress: this.initializationState.initializationProgress,
            modules: status,
            failedModules: this.initializationState.failedModules,
            initializationTime: this.initializationState.initializationTime
        };
    }

    /**
     * 获取版本信息
     */
    getVersionInfo() {
        return {
            version: this.version,
            buildDate: this.buildDate,
            moduleCount: Object.keys(this.modules).length,
            activeModules: Object.values(this.modules).filter(Boolean).length
        };
    }

    /**
     * 销毁模块化侧边栏
     */
    destroy() {
        console.log('🗑️ [MDACModularSidePanel] 销毁模块化侧边栏');

        // 按相反顺序销毁模块
        const destroyOrder = [
            'uiRenderer', 'modalManager', 'progressVisualizer',
            'autoParseManager', 'confidenceEvaluator', 'cityViewer',
            'formFiller', 'fieldMatcher', 'dataValidator',
            'aiService', 'textParser', 'imageProcessor',
            'previewManager', 'dataManager', 'storageService',
            'messageHelper', 'dateFormatter', 'debugLogger',
            'moduleInitializer', 'sidePanelCore', 'moduleLoader',
            'moduleRegistry', 'stateManager', 'eventManager'
        ];

        destroyOrder.forEach(moduleName => {
            const module = this.modules[moduleName];
            if (module && typeof module.destroy === 'function') {
                try {
                    module.destroy();
                } catch (error) {
                    console.error(`❌ [MDACModularSidePanel] 销毁模块失败: ${moduleName}`, error);
                }
            }
            this.modules[moduleName] = null;
        });

        // 重置状态
        this.initializationState = {
            isInitializing: false,
            isInitialized: false,
            initializationProgress: 0,
            failedModules: [],
            initializationTime: null
        };
    }
}

// 导出类到全局（供bootstrap检测使用）
window.MDACModularSidePanel = MDACModularSidePanel;

// 全局初始化
let mdacModularSidePanel = null;

// DOM加载完成后等待bootstrap完成再初始化
async function waitForBootstrapAndInitialize() {
    // 检查可用的bootstrap系统 (优先使用enhanced-bootstrap)
    const bootstrap = window.mdacEnhancedBootstrap || window.mdacUltimateBootstrap;
    
    if (bootstrap) {
        const bootstrapName = window.mdacEnhancedBootstrap ? 'Enhanced Bootstrap' : 'Ultimate Bootstrap';
        console.log(`⏳ [MDACModularSidePanel] 等待${bootstrapName}完成模块加载...`);
        
        // 等待所有必需模块加载完成
        const maxWaitTime = 30000; // 30秒超时
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWaitTime) {
            // 检查所有必需模块是否已加载
            const requiredModules = ['EventBus', 'EventManager', 'StateManager', 'ModuleRegistry', 'SidePanelCore'];
            const allLoaded = requiredModules.every(module => window[module]);
            
            if (allLoaded) {
                console.log('✅ [MDACModularSidePanel] 所有必需模块已加载，开始初始化');
                mdacModularSidePanel = new MDACModularSidePanel();
                // 延迟一点再初始化，确保所有模块都已就绪
                setTimeout(() => {
                    if (mdacModularSidePanel && !mdacModularSidePanel.initializationState.isInitialized) {
                        mdacModularSidePanel.initialize();
                    }
                }, 500);
                return;
            }
            
            // 等待100ms再检查
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        console.warn('⚠️ [MDACModularSidePanel] 等待模块加载超时，尝试创建实例');
        mdacModularSidePanel = new MDACModularSidePanel();
    } else {
        console.warn('⚠️ [MDACModularSidePanel] 没有发现Bootstrap系统，直接创建实例');
        mdacModularSidePanel = new MDACModularSidePanel();
    }
}

    // 立即注册到全局对象
    window.MDACModularSidePanel = MDACModularSidePanel;
    console.log('✅ [MDACModularSidePanel] 类已注册到全局对象');

    // DOM加载完成后等待bootstrap完成再初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForBootstrapAndInitialize);
    } else {
        waitForBootstrapAndInitialize();
    }

    // 导出类 - 兼容 ultimate-bootstrap 属性保护系统
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = MDACModularSidePanel;
    }

    console.log('✅ [MDACModularSidePanel] 模块化侧边栏主文件已加载完成');

})();
