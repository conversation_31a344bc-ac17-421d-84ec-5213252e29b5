/**
 * 性能优化配置 - 模块化系统性能调优设置
 * 提供各种性能优化选项和配置
 * 创建日期: 2025-01-11
 */

const PerformanceConfig = {
    // 模块加载优化
    moduleLoading: {
        // 启用懒加载
        enableLazyLoading: true,
        
        // 模块加载超时时间（毫秒）
        loadTimeout: 30000,
        
        // 并发加载模块数量
        concurrentLoads: 3,
        
        // 预加载关键模块
        preloadModules: [
            'EventManager',
            'StateManager',
            'DebugLogger'
        ],
        
        // 延迟加载的模块
        lazyLoadModules: [
            'CityViewer',
            'PreviewManager',
            'ConfidenceEvaluator'
        ],
        
        // 模块加载优先级
        loadPriority: {
            high: ['EventManager', 'StateManager', 'UIRenderer'],
            medium: ['AIService', 'FormFiller', 'DataManager'],
            low: ['CityViewer', 'PreviewManager', 'AutoParseManager']
        }
    },

    // 内存管理优化
    memoryManagement: {
        // 启用内存监控
        enableMonitoring: true,
        
        // 内存警告阈值（字节）
        warningThreshold: 50 * 1024 * 1024, // 50MB
        
        // 内存清理阈值（字节）
        cleanupThreshold: 80 * 1024 * 1024, // 80MB
        
        // 自动垃圾回收间隔（毫秒）
        gcInterval: 300000, // 5分钟
        
        // 缓存大小限制
        cacheLimit: {
            logs: 1000,
            events: 500,
            states: 200,
            data: 100
        },
        
        // 启用对象池
        enableObjectPooling: true,
        
        // 对象池配置
        objectPools: {
            events: { size: 100, type: 'Event' },
            logEntries: { size: 200, type: 'LogEntry' },
            stateChanges: { size: 50, type: 'StateChange' }
        }
    },

    // 事件系统优化
    eventSystem: {
        // 启用事件批处理
        enableBatching: true,
        
        // 批处理间隔（毫秒）
        batchInterval: 16, // ~60fps
        
        // 最大批处理大小
        maxBatchSize: 50,
        
        // 启用事件防抖
        enableDebouncing: true,
        
        // 防抖配置
        debounceConfig: {
            'ui:input-changed': 300,
            'data:changed': 100,
            'state:updated': 50
        },
        
        // 启用事件节流
        enableThrottling: true,
        
        // 节流配置
        throttleConfig: {
            'ui:scroll': 16,
            'ui:resize': 100,
            'performance:metrics': 1000
        },
        
        // 事件优先级
        eventPriority: {
            high: ['error', 'critical', 'user-action'],
            medium: ['data-change', 'state-update', 'ui-update'],
            low: ['debug', 'metrics', 'analytics']
        }
    },

    // DOM操作优化
    domOptimization: {
        // 启用虚拟滚动
        enableVirtualScrolling: true,
        
        // 虚拟滚动配置
        virtualScrolling: {
            itemHeight: 40,
            bufferSize: 10,
            threshold: 100
        },
        
        // 启用DOM批量更新
        enableBatchUpdates: true,
        
        // 批量更新间隔（毫秒）
        batchUpdateInterval: 16,
        
        // 启用DocumentFragment
        useDocumentFragment: true,
        
        // 启用CSS动画硬件加速
        enableHardwareAcceleration: true,
        
        // 延迟DOM操作
        deferDOMOperations: true,
        
        // DOM操作队列大小
        domQueueSize: 100
    },

    // 网络请求优化
    networkOptimization: {
        // 启用请求缓存
        enableCaching: true,
        
        // 缓存过期时间（毫秒）
        cacheExpiry: 300000, // 5分钟
        
        // 启用请求合并
        enableRequestMerging: true,
        
        // 请求合并延迟（毫秒）
        mergeDelay: 50,
        
        // 最大并发请求数
        maxConcurrentRequests: 6,
        
        // 请求超时时间（毫秒）
        requestTimeout: 30000,
        
        // 启用请求重试
        enableRetry: true,
        
        // 重试配置
        retryConfig: {
            maxAttempts: 3,
            backoffMultiplier: 2,
            initialDelay: 1000
        },
        
        // 启用请求压缩
        enableCompression: true
    },

    // 渲染优化
    renderingOptimization: {
        // 启用渲染节流
        enableRenderThrottling: true,
        
        // 渲染帧率限制
        maxFPS: 60,
        
        // 启用增量渲染
        enableIncrementalRendering: true,
        
        // 增量渲染块大小
        incrementalChunkSize: 50,
        
        // 启用离屏渲染
        enableOffscreenRendering: true,
        
        // 启用渲染缓存
        enableRenderCaching: true,
        
        // 渲染缓存大小
        renderCacheSize: 100,
        
        // 启用CSS优化
        enableCSSOptimization: true,
        
        // CSS优化配置
        cssOptimization: {
            minifyCSS: true,
            removeUnusedCSS: true,
            optimizeSelectors: true
        }
    },

    // 数据处理优化
    dataProcessing: {
        // 启用数据流处理
        enableStreaming: true,
        
        // 流处理块大小
        streamChunkSize: 1000,
        
        // 启用数据压缩
        enableCompression: true,
        
        // 压缩算法
        compressionAlgorithm: 'gzip',
        
        // 启用数据缓存
        enableCaching: true,
        
        // 数据缓存配置
        cacheConfig: {
            maxSize: 10 * 1024 * 1024, // 10MB
            ttl: 600000, // 10分钟
            strategy: 'lru' // LRU策略
        },
        
        // 启用数据分页
        enablePagination: true,
        
        // 分页配置
        paginationConfig: {
            pageSize: 50,
            preloadPages: 2,
            maxPages: 100
        },
        
        // 启用数据索引
        enableIndexing: true,
        
        // 索引配置
        indexConfig: {
            fields: ['id', 'timestamp', 'type'],
            strategy: 'btree'
        }
    },

    // 调试和监控优化
    debugOptimization: {
        // 生产环境禁用调试
        disableInProduction: true,
        
        // 调试级别
        debugLevel: 'INFO', // DEBUG, INFO, WARN, ERROR
        
        // 启用性能监控
        enablePerformanceMonitoring: true,
        
        // 性能监控间隔（毫秒）
        monitoringInterval: 5000,
        
        // 启用内存泄漏检测
        enableMemoryLeakDetection: true,
        
        // 内存泄漏检测间隔（毫秒）
        leakDetectionInterval: 30000,
        
        // 启用性能分析
        enableProfiling: false,
        
        // 分析采样率
        profilingSampleRate: 0.1,
        
        // 启用错误追踪
        enableErrorTracking: true,
        
        // 错误上报配置
        errorReporting: {
            maxErrors: 100,
            reportInterval: 60000,
            includeStackTrace: true
        }
    },

    // 启动优化
    startupOptimization: {
        // 启用预加载
        enablePreloading: true,
        
        // 预加载资源
        preloadResources: [
            'critical-css',
            'core-modules',
            'essential-data'
        ],
        
        // 启用代码分割
        enableCodeSplitting: true,
        
        // 代码分割策略
        codeSplittingStrategy: 'route-based',
        
        // 启用懒加载
        enableLazyLoading: true,
        
        // 懒加载阈值
        lazyLoadThreshold: '50px',
        
        // 启用服务工作者
        enableServiceWorker: false,
        
        // 启用应用缓存
        enableAppCache: true,
        
        // 缓存策略
        cacheStrategy: 'cache-first'
    },

    // 用户体验优化
    userExperienceOptimization: {
        // 启用加载指示器
        enableLoadingIndicators: true,
        
        // 加载指示器延迟（毫秒）
        loadingIndicatorDelay: 200,
        
        // 启用骨架屏
        enableSkeletonScreens: true,
        
        // 启用渐进式加载
        enableProgressiveLoading: true,
        
        // 启用预测性预加载
        enablePredictivePreloading: true,
        
        // 预测算法
        predictionAlgorithm: 'user-behavior',
        
        // 启用平滑滚动
        enableSmoothScrolling: true,
        
        // 启用触觉反馈
        enableHapticFeedback: false,
        
        // 启用无障碍优化
        enableAccessibilityOptimization: true
    },

    // 实验性功能
    experimental: {
        // 启用Web Workers
        enableWebWorkers: false,
        
        // Web Worker配置
        webWorkerConfig: {
            maxWorkers: 2,
            taskTypes: ['data-processing', 'image-processing']
        },
        
        // 启用WebAssembly
        enableWebAssembly: false,
        
        // 启用SharedArrayBuffer
        enableSharedArrayBuffer: false,
        
        // 启用OffscreenCanvas
        enableOffscreenCanvas: false,
        
        // 启用Intersection Observer
        enableIntersectionObserver: true,
        
        // 启用Resize Observer
        enableResizeObserver: true,
        
        // 启用Performance Observer
        enablePerformanceObserver: true
    }
};

// 环境特定配置
const EnvironmentConfig = {
    development: {
        debugOptimization: {
            debugLevel: 'DEBUG',
            enableProfiling: true,
            disableInProduction: false
        },
        memoryManagement: {
            warningThreshold: 100 * 1024 * 1024 // 100MB
        }
    },
    
    production: {
        debugOptimization: {
            debugLevel: 'ERROR',
            enableProfiling: false,
            disableInProduction: true
        },
        memoryManagement: {
            warningThreshold: 30 * 1024 * 1024 // 30MB
        },
        renderingOptimization: {
            enableRenderCaching: true,
            enableCSSOptimization: true
        }
    },
    
    testing: {
        debugOptimization: {
            debugLevel: 'INFO',
            enablePerformanceMonitoring: true
        },
        memoryManagement: {
            enableMonitoring: true,
            gcInterval: 10000 // 10秒
        }
    }
};

// 获取当前环境配置
function getCurrentEnvironmentConfig() {
    const environment = process.env.NODE_ENV || 'development';
    const baseConfig = JSON.parse(JSON.stringify(PerformanceConfig));
    const envConfig = EnvironmentConfig[environment] || {};
    
    // 深度合并配置
    return deepMerge(baseConfig, envConfig);
}

// 深度合并对象
function deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = deepMerge(result[key] || {}, source[key]);
        } else {
            result[key] = source[key];
        }
    }
    
    return result;
}

// 性能配置应用器
class PerformanceConfigApplier {
    constructor(config = getCurrentEnvironmentConfig()) {
        this.config = config;
        this.appliedOptimizations = new Set();
    }

    /**
     * 应用所有性能优化
     */
    applyAllOptimizations() {
        console.log('🚀 [PerformanceConfig] 应用性能优化配置');
        
        this.applyMemoryOptimizations();
        this.applyEventOptimizations();
        this.applyDOMOptimizations();
        this.applyRenderingOptimizations();
        this.applyNetworkOptimizations();
        this.applyDebugOptimizations();
        
        console.log('✅ [PerformanceConfig] 性能优化配置应用完成', {
            appliedOptimizations: Array.from(this.appliedOptimizations)
        });
    }

    /**
     * 应用内存优化
     */
    applyMemoryOptimizations() {
        const config = this.config.memoryManagement;
        
        if (config.enableMonitoring) {
            this.setupMemoryMonitoring(config);
            this.appliedOptimizations.add('memory-monitoring');
        }
        
        if (config.enableObjectPooling) {
            this.setupObjectPooling(config.objectPools);
            this.appliedOptimizations.add('object-pooling');
        }
    }

    /**
     * 应用事件优化
     */
    applyEventOptimizations() {
        const config = this.config.eventSystem;
        
        if (config.enableBatching) {
            this.setupEventBatching(config);
            this.appliedOptimizations.add('event-batching');
        }
        
        if (config.enableDebouncing) {
            this.setupEventDebouncing(config.debounceConfig);
            this.appliedOptimizations.add('event-debouncing');
        }
        
        if (config.enableThrottling) {
            this.setupEventThrottling(config.throttleConfig);
            this.appliedOptimizations.add('event-throttling');
        }
    }

    /**
     * 应用DOM优化
     */
    applyDOMOptimizations() {
        const config = this.config.domOptimization;
        
        if (config.enableBatchUpdates) {
            this.setupDOMBatching(config);
            this.appliedOptimizations.add('dom-batching');
        }
        
        if (config.enableHardwareAcceleration) {
            this.enableHardwareAcceleration();
            this.appliedOptimizations.add('hardware-acceleration');
        }
    }

    /**
     * 应用渲染优化
     */
    applyRenderingOptimizations() {
        const config = this.config.renderingOptimization;
        
        if (config.enableRenderThrottling) {
            this.setupRenderThrottling(config);
            this.appliedOptimizations.add('render-throttling');
        }
        
        if (config.enableRenderCaching) {
            this.setupRenderCaching(config);
            this.appliedOptimizations.add('render-caching');
        }
    }

    /**
     * 应用网络优化
     */
    applyNetworkOptimizations() {
        const config = this.config.networkOptimization;
        
        if (config.enableCaching) {
            this.setupNetworkCaching(config);
            this.appliedOptimizations.add('network-caching');
        }
        
        if (config.enableRequestMerging) {
            this.setupRequestMerging(config);
            this.appliedOptimizations.add('request-merging');
        }
    }

    /**
     * 应用调试优化
     */
    applyDebugOptimizations() {
        const config = this.config.debugOptimization;
        
        if (config.enablePerformanceMonitoring) {
            this.setupPerformanceMonitoring(config);
            this.appliedOptimizations.add('performance-monitoring');
        }
        
        if (config.enableMemoryLeakDetection) {
            this.setupMemoryLeakDetection(config);
            this.appliedOptimizations.add('memory-leak-detection');
        }
    }

    // 具体的优化实现方法（简化版本）
    setupMemoryMonitoring(config) {
        if (window.performance && window.performance.memory) {
            setInterval(() => {
                const memory = window.performance.memory;
                if (memory.usedJSHeapSize > config.warningThreshold) {
                    console.warn('⚠️ [PerformanceConfig] 内存使用超过警告阈值');
                }
            }, config.gcInterval);
        }
    }

    setupObjectPooling(pools) {
        // 对象池实现
        window.mdacObjectPools = {};
        Object.entries(pools).forEach(([name, config]) => {
            window.mdacObjectPools[name] = {
                pool: [],
                size: config.size,
                type: config.type
            };
        });
    }

    setupEventBatching(config) {
        // 事件批处理实现
        window.mdacEventBatcher = {
            queue: [],
            interval: config.batchInterval,
            maxSize: config.maxBatchSize
        };
    }

    setupEventDebouncing(debounceConfig) {
        // 事件防抖实现
        window.mdacDebouncers = new Map();
        Object.entries(debounceConfig).forEach(([event, delay]) => {
            window.mdacDebouncers.set(event, delay);
        });
    }

    setupEventThrottling(throttleConfig) {
        // 事件节流实现
        window.mdacThrottlers = new Map();
        Object.entries(throttleConfig).forEach(([event, delay]) => {
            window.mdacThrottlers.set(event, delay);
        });
    }

    setupDOMBatching(config) {
        // DOM批量更新实现
        window.mdacDOMBatcher = {
            queue: [],
            interval: config.batchUpdateInterval
        };
    }

    enableHardwareAcceleration() {
        // 启用硬件加速
        const style = document.createElement('style');
        style.textContent = `
            .mdac-accelerated {
                transform: translateZ(0);
                will-change: transform;
            }
        `;
        document.head.appendChild(style);
    }

    setupRenderThrottling(config) {
        // 渲染节流实现
        window.mdacRenderThrottler = {
            maxFPS: config.maxFPS,
            lastRender: 0
        };
    }

    setupRenderCaching(config) {
        // 渲染缓存实现
        window.mdacRenderCache = new Map();
    }

    setupNetworkCaching(config) {
        // 网络缓存实现
        window.mdacNetworkCache = new Map();
    }

    setupRequestMerging(config) {
        // 请求合并实现
        window.mdacRequestMerger = {
            queue: new Map(),
            delay: config.mergeDelay
        };
    }

    setupPerformanceMonitoring(config) {
        // 性能监控实现
        setInterval(() => {
            if (window.performance) {
                const metrics = {
                    memory: window.performance.memory,
                    timing: window.performance.timing,
                    navigation: window.performance.navigation
                };
                console.log('📊 [PerformanceConfig] 性能指标', metrics);
            }
        }, config.monitoringInterval);
    }

    setupMemoryLeakDetection(config) {
        // 内存泄漏检测实现
        let lastMemoryUsage = 0;
        setInterval(() => {
            if (window.performance && window.performance.memory) {
                const currentUsage = window.performance.memory.usedJSHeapSize;
                if (currentUsage > lastMemoryUsage * 1.5) {
                    console.warn('⚠️ [PerformanceConfig] 检测到可能的内存泄漏');
                }
                lastMemoryUsage = currentUsage;
            }
        }, config.leakDetectionInterval);
    }
}

// 导出配置和应用器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PerformanceConfig,
        EnvironmentConfig,
        PerformanceConfigApplier,
        getCurrentEnvironmentConfig
    };
} else {
    window.PerformanceConfig = PerformanceConfig;
    window.PerformanceConfigApplier = PerformanceConfigApplier;
    window.getCurrentEnvironmentConfig = getCurrentEnvironmentConfig;
}

console.log('✅ [PerformanceConfig] 性能优化配置已加载');
