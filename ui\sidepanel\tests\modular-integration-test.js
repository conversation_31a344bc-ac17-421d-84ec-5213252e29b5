/**
 * 模块化集成测试 - 测试所有模块的集成和功能
 * 验证模块化重构后的系统完整性
 * 创建日期: 2025-01-11
 */

class ModularIntegrationTest {
    constructor() {
        this.testResults = [];
        this.testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            startTime: null,
            endTime: null
        };

        this.testSuites = [
            'coreModules',
            'utilityModules', 
            'dataModules',
            'functionalModules',
            'uiModules',
            'integration',
            'performance'
        ];

        console.log('🧪 [ModularIntegrationTest] 模块化集成测试初始化');
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        try {
            console.log('🚀 [ModularIntegrationTest] 开始运行所有测试');
            this.testStats.startTime = Date.now();

            // 等待模块化侧边栏初始化完成
            await this.waitForInitialization();

            // 运行各个测试套件
            for (const suiteName of this.testSuites) {
                await this.runTestSuite(suiteName);
            }

            this.testStats.endTime = Date.now();
            this.generateTestReport();

        } catch (error) {
            console.error('❌ [ModularIntegrationTest] 测试运行失败', error);
            this.testStats.endTime = Date.now();
            this.generateTestReport();
        }
    }

    /**
     * 等待初始化完成
     */
    async waitForInitialization() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('初始化超时'));
            }, 30000);

            const checkInitialization = () => {
                if (window.mdacModularSidePanel && 
                    window.mdacModularSidePanel.initializationState.isInitialized) {
                    clearTimeout(timeout);
                    resolve();
                } else {
                    setTimeout(checkInitialization, 100);
                }
            };

            checkInitialization();
        });
    }

    /**
     * 运行测试套件
     * @param {string} suiteName - 测试套件名称
     */
    async runTestSuite(suiteName) {
        console.log(`🧪 [ModularIntegrationTest] 运行测试套件: ${suiteName}`);

        const suiteStartTime = Date.now();
        const suiteResults = [];

        try {
            switch (suiteName) {
                case 'coreModules':
                    await this.testCoreModules(suiteResults);
                    break;
                case 'utilityModules':
                    await this.testUtilityModules(suiteResults);
                    break;
                case 'dataModules':
                    await this.testDataModules(suiteResults);
                    break;
                case 'functionalModules':
                    await this.testFunctionalModules(suiteResults);
                    break;
                case 'uiModules':
                    await this.testUIModules(suiteResults);
                    break;
                case 'integration':
                    await this.testIntegration(suiteResults);
                    break;
                case 'performance':
                    await this.testPerformance(suiteResults);
                    break;
            }

            const suiteEndTime = Date.now();
            const suiteDuration = suiteEndTime - suiteStartTime;

            console.log(`✅ [ModularIntegrationTest] 测试套件完成: ${suiteName} (${suiteDuration}ms)`);

        } catch (error) {
            console.error(`❌ [ModularIntegrationTest] 测试套件失败: ${suiteName}`, error);
            suiteResults.push({
                name: `${suiteName}_suite_error`,
                passed: false,
                error: error.message,
                duration: Date.now() - suiteStartTime
            });
        }

        this.testResults.push(...suiteResults);
        this.updateStats(suiteResults);
    }

    /**
     * 测试核心模块
     * @param {Array} results - 结果数组
     */
    async testCoreModules(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试事件管理器
        await this.runTest('EventManager存在性', () => {
            return !!sidePanel.modules.eventManager;
        }, results);

        await this.runTest('EventManager功能', () => {
            const eventManager = sidePanel.modules.eventManager;
            let eventReceived = false;
            
            eventManager.on('test-event', () => {
                eventReceived = true;
            });
            
            eventManager.emit('test-event');
            return eventReceived;
        }, results);

        // 测试状态管理器
        await this.runTest('StateManager存在性', () => {
            return !!sidePanel.modules.stateManager;
        }, results);

        await this.runTest('StateManager功能', () => {
            const stateManager = sidePanel.modules.stateManager;
            stateManager.set('test.value', 'test-data');
            return stateManager.get('test.value') === 'test-data';
        }, results);

        // 测试模块注册器
        await this.runTest('ModuleRegistry存在性', () => {
            return !!sidePanel.modules.moduleRegistry;
        }, results);

        // 测试模块加载器
        await this.runTest('ModuleLoader存在性', () => {
            return !!sidePanel.modules.moduleLoader;
        }, results);

        // 测试侧边栏核心
        await this.runTest('SidePanelCore存在性', () => {
            return !!sidePanel.modules.sidePanelCore;
        }, results);
    }

    /**
     * 测试工具模块
     * @param {Array} results - 结果数组
     */
    async testUtilityModules(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试调试日志器
        await this.runTest('DebugLogger存在性', () => {
            return !!sidePanel.modules.debugLogger;
        }, results);

        await this.runTest('DebugLogger功能', () => {
            const logger = sidePanel.modules.debugLogger;
            const initialLogCount = logger.logs.length;
            logger.info('测试日志');
            return logger.logs.length > initialLogCount;
        }, results);

        // 测试日期格式化器
        await this.runTest('DateFormatter存在性', () => {
            return !!sidePanel.modules.dateFormatter;
        }, results);

        await this.runTest('DateFormatter功能', () => {
            const formatter = sidePanel.modules.dateFormatter;
            const testDate = new Date('2025-01-11');
            const formatted = formatter.formatDate(testDate, 'mdac');
            return formatted === '11/01/2025';
        }, results);

        // 测试消息助手
        await this.runTest('MessageHelper存在性', () => {
            return !!sidePanel.modules.messageHelper;
        }, results);
    }

    /**
     * 测试数据管理模块
     * @param {Array} results - 结果数组
     */
    async testDataModules(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试存储服务
        await this.runTest('StorageService存在性', () => {
            return !!sidePanel.modules.storageService;
        }, results);

        // 测试数据管理器
        await this.runTest('DataManager存在性', () => {
            return !!sidePanel.modules.dataManager;
        }, results);

        await this.runTest('DataManager功能', () => {
            const dataManager = sidePanel.modules.dataManager;
            dataManager.setData('test.field', 'test-value');
            return dataManager.getData('test.field') === 'test-value';
        }, results);

        // 测试预览管理器
        await this.runTest('PreviewManager存在性', () => {
            return !!sidePanel.modules.previewManager;
        }, results);
    }

    /**
     * 测试功能模块
     * @param {Array} results - 结果数组
     */
    async testFunctionalModules(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试AI服务
        await this.runTest('AIService存在性', () => {
            return !!sidePanel.modules.aiService;
        }, results);

        // 测试文本解析器
        await this.runTest('TextParser存在性', () => {
            return !!sidePanel.modules.textParser;
        }, results);

        // 测试图片处理器
        await this.runTest('ImageProcessor存在性', () => {
            return !!sidePanel.modules.imageProcessor;
        }, results);

        // 测试表单填充器
        await this.runTest('FormFiller存在性', () => {
            return !!sidePanel.modules.formFiller;
        }, results);

        // 测试字段匹配器
        await this.runTest('FieldMatcher存在性', () => {
            return !!sidePanel.modules.fieldMatcher;
        }, results);

        // 测试数据验证器
        await this.runTest('DataValidator存在性', () => {
            return !!sidePanel.modules.dataValidator;
        }, results);

        // 测试置信度评估器
        await this.runTest('ConfidenceEvaluator存在性', () => {
            return !!sidePanel.modules.confidenceEvaluator;
        }, results);

        // 测试自动解析管理器
        await this.runTest('AutoParseManager存在性', () => {
            return !!sidePanel.modules.autoParseManager;
        }, results);

        // 测试城市查看器
        await this.runTest('CityViewer存在性', () => {
            return !!sidePanel.modules.cityViewer;
        }, results);
    }

    /**
     * 测试UI模块
     * @param {Array} results - 结果数组
     */
    async testUIModules(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试UI渲染器
        await this.runTest('UIRenderer存在性', () => {
            return !!sidePanel.modules.uiRenderer;
        }, results);

        // 测试模态框管理器
        await this.runTest('ModalManager存在性', () => {
            return !!sidePanel.modules.modalManager;
        }, results);

        // 测试进度可视化器
        await this.runTest('ProgressVisualizer存在性', () => {
            return !!sidePanel.modules.progressVisualizer;
        }, results);
    }

    /**
     * 测试集成功能
     * @param {Array} results - 结果数组
     */
    async testIntegration(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试模块间通信
        await this.runTest('模块间事件通信', () => {
            let eventReceived = false;
            const eventManager = sidePanel.modules.eventManager;
            
            eventManager.on('integration-test', () => {
                eventReceived = true;
            });
            
            eventManager.emit('integration-test');
            return eventReceived;
        }, results);

        // 测试数据流
        await this.runTest('数据流测试', () => {
            const dataManager = sidePanel.modules.dataManager;
            const stateManager = sidePanel.modules.stateManager;
            
            dataManager.setData('integration.test', 'flow-test');
            
            // 检查状态管理器是否同步
            return stateManager.get('data.integration.test') === 'flow-test';
        }, results);

        // 测试错误处理
        await this.runTest('错误处理测试', () => {
            try {
                // 触发一个预期的错误
                throw new Error('测试错误');
            } catch (error) {
                // 检查错误是否被正确记录
                const logger = sidePanel.modules.debugLogger;
                return logger.stats.errorLogs > 0;
            }
        }, results);
    }

    /**
     * 测试性能
     * @param {Array} results - 结果数组
     */
    async testPerformance(results) {
        const sidePanel = window.mdacModularSidePanel;

        // 测试初始化时间
        await this.runTest('初始化性能', () => {
            const initTime = Date.now() - sidePanel.initializationState.initializationTime;
            return initTime < 10000; // 应该在10秒内完成
        }, results);

        // 测试内存使用
        await this.runTest('内存使用', () => {
            if (window.performance && window.performance.memory) {
                const memoryUsage = window.performance.memory.usedJSHeapSize;
                return memoryUsage < 50 * 1024 * 1024; // 应该小于50MB
            }
            return true; // 如果无法检测内存，跳过测试
        }, results);

        // 测试模块加载数量
        await this.runTest('模块加载完整性', () => {
            const moduleCount = Object.values(sidePanel.modules).filter(Boolean).length;
            const expectedModules = Object.keys(sidePanel.modules).length;
            return moduleCount >= expectedModules * 0.8; // 至少80%的模块应该加载成功
        }, results);
    }

    /**
     * 运行单个测试
     * @param {string} name - 测试名称
     * @param {Function} testFn - 测试函数
     * @param {Array} results - 结果数组
     */
    async runTest(name, testFn, results) {
        const startTime = Date.now();
        
        try {
            const result = await testFn();
            const endTime = Date.now();
            
            results.push({
                name: name,
                passed: !!result,
                duration: endTime - startTime,
                timestamp: startTime
            });
            
            console.log(`${result ? '✅' : '❌'} [Test] ${name} (${endTime - startTime}ms)`);
            
        } catch (error) {
            const endTime = Date.now();
            
            results.push({
                name: name,
                passed: false,
                error: error.message,
                duration: endTime - startTime,
                timestamp: startTime
            });
            
            console.error(`❌ [Test] ${name} - Error: ${error.message}`);
        }
    }

    /**
     * 更新统计信息
     * @param {Array} suiteResults - 套件结果
     */
    updateStats(suiteResults) {
        suiteResults.forEach(result => {
            this.testStats.total++;
            if (result.passed) {
                this.testStats.passed++;
            } else {
                this.testStats.failed++;
            }
        });
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        const duration = this.testStats.endTime - this.testStats.startTime;
        const successRate = (this.testStats.passed / this.testStats.total * 100).toFixed(2);

        const report = {
            summary: {
                total: this.testStats.total,
                passed: this.testStats.passed,
                failed: this.testStats.failed,
                successRate: successRate + '%',
                duration: duration + 'ms'
            },
            details: this.testResults,
            timestamp: new Date().toISOString()
        };

        console.log('📊 [ModularIntegrationTest] 测试报告', report);

        // 保存报告到localStorage
        try {
            localStorage.setItem('mdac_test_report', JSON.stringify(report));
        } catch (error) {
            console.warn('⚠️ [ModularIntegrationTest] 无法保存测试报告', error);
        }

        // 显示摘要
        this.displayTestSummary(report.summary);

        return report;
    }

    /**
     * 显示测试摘要
     * @param {Object} summary - 摘要信息
     */
    displayTestSummary(summary) {
        const summaryElement = document.createElement('div');
        summaryElement.id = 'test-summary';
        summaryElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: monospace;
            font-size: 12px;
            max-width: 300px;
        `;

        summaryElement.innerHTML = `
            <h3 style="margin: 0 0 12px 0; color: #007bff;">🧪 测试报告</h3>
            <div><strong>总计:</strong> ${summary.total}</div>
            <div style="color: green;"><strong>通过:</strong> ${summary.passed}</div>
            <div style="color: red;"><strong>失败:</strong> ${summary.failed}</div>
            <div><strong>成功率:</strong> ${summary.successRate}</div>
            <div><strong>耗时:</strong> ${summary.duration}</div>
            <button onclick="this.parentElement.remove()" style="margin-top: 12px; padding: 4px 8px; border: 1px solid #ccc; background: white; cursor: pointer;">关闭</button>
        `;

        document.body.appendChild(summaryElement);

        // 5秒后自动移除
        setTimeout(() => {
            if (summaryElement.parentElement) {
                summaryElement.remove();
            }
        }, 10000);
    }

    /**
     * 获取测试结果
     */
    getTestResults() {
        return {
            stats: this.testStats,
            results: this.testResults
        };
    }
}

// 自动运行测试（如果在测试环境中）
if (window.location.search.includes('run-tests=true')) {
    document.addEventListener('DOMContentLoaded', async () => {
        // 等待一段时间确保所有模块加载完成
        setTimeout(async () => {
            const test = new ModularIntegrationTest();
            await test.runAllTests();
        }, 2000);
    });
}

// 导出到全局
window.ModularIntegrationTest = ModularIntegrationTest;

console.log('✅ [ModularIntegrationTest] 模块化集成测试已加载');
