/**
 * 事件总线 - 模块间通信核心
 * 提供发布-订阅模式的事件通信机制
 * 创建日期: 2025-01-11
 */

// 防止重复声明 - 修复版本
if (typeof window.EventBus !== 'undefined') {
    console.warn('⚠️ [EventBus] 类已存在，跳过重复声明');
} else {

class EventBus {
    constructor() {
        // 事件监听器映射表
        this.events = new Map();
        // 一次性事件监听器
        this.onceEvents = new Map();
        // 调试模式
        this.debug = false;
        
        console.log('🚌 [EventBus] 事件总线已初始化');
    }

    /**
     * 订阅事件
     * @param {string} event - 事件名称
     * @param {Function} handler - 事件处理函数
     * @param {Object} context - 执行上下文
     */
    on(event, handler, context = null) {
        if (typeof handler !== 'function') {
            throw new Error('事件处理器必须是函数');
        }

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listener = { handler, context };
        this.events.get(event).push(listener);

        if (this.debug) {
            console.log(`📝 [EventBus] 订阅事件: ${event}`, { handler: handler.name, context });
        }

        // 返回取消订阅函数
        return () => this.off(event, handler);
    }

    /**
     * 订阅一次性事件
     * @param {string} event - 事件名称
     * @param {Function} handler - 事件处理函数
     * @param {Object} context - 执行上下文
     */
    once(event, handler, context = null) {
        const onceHandler = (...args) => {
            this.off(event, onceHandler);
            handler.apply(context, args);
        };

        return this.on(event, onceHandler, context);
    }

    /**
     * 取消订阅事件
     * @param {string} event - 事件名称
     * @param {Function} handler - 事件处理函数
     */
    off(event, handler) {
        if (!this.events.has(event)) {
            return false;
        }

        const listeners = this.events.get(event);
        const index = listeners.findIndex(listener => listener.handler === handler);

        if (index !== -1) {
            listeners.splice(index, 1);
            
            if (this.debug) {
                console.log(`🗑️ [EventBus] 取消订阅事件: ${event}`, { handler: handler.name });
            }

            // 如果没有监听器了，删除事件
            if (listeners.length === 0) {
                this.events.delete(event);
            }
            
            return true;
        }

        return false;
    }

    /**
     * 发布事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     * @param {Object} options - 发布选项
     */
    emit(event, data = null, options = {}) {
        const { async = false, timeout = 5000 } = options;

        if (this.debug) {
            console.log(`📢 [EventBus] 发布事件: ${event}`, { data, async, timeout });
        }

        if (!this.events.has(event)) {
            if (this.debug) {
                console.log(`⚠️ [EventBus] 没有监听器订阅事件: ${event}`);
            }
            return Promise.resolve([]);
        }

        const listeners = [...this.events.get(event)]; // 复制数组避免修改原数组
        const results = [];

        if (async) {
            // 异步执行
            return Promise.all(
                listeners.map(listener => {
                    return Promise.race([
                        this.executeHandler(listener, event, data),
                        this.createTimeout(timeout, event, listener.handler.name)
                    ]);
                })
            );
        } else {
            // 同步执行
            for (const listener of listeners) {
                try {
                    const result = this.executeHandler(listener, event, data);
                    results.push(result);
                } catch (error) {
                    console.error(`❌ [EventBus] 事件处理器执行失败: ${event}`, error);
                    results.push(error);
                }
            }
            return results;
        }
    }

    /**
     * 执行事件处理器
     * @param {Object} listener - 监听器对象
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    executeHandler(listener, event, data) {
        try {
            const { handler, context } = listener;
            
            if (context) {
                return handler.call(context, data, event);
            } else {
                return handler(data, event);
            }
        } catch (error) {
            console.error(`❌ [EventBus] 事件处理器执行异常: ${event}`, error);
            throw error;
        }
    }

    /**
     * 创建超时Promise
     * @param {number} timeout - 超时时间
     * @param {string} event - 事件名称
     * @param {string} handlerName - 处理器名称
     */
    createTimeout(timeout, event, handlerName) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`事件处理器超时: ${event} (${handlerName})`));
            }, timeout);
        });
    }

    /**
     * 清除所有事件监听器
     */
    clear() {
        const eventCount = this.events.size;
        this.events.clear();
        this.onceEvents.clear();
        
        console.log(`🧹 [EventBus] 已清除 ${eventCount} 个事件的所有监听器`);
    }

    /**
     * 获取事件统计信息
     */
    getStats() {
        const stats = {
            totalEvents: this.events.size,
            totalListeners: 0,
            events: {}
        };

        for (const [event, listeners] of this.events) {
            stats.events[event] = listeners.length;
            stats.totalListeners += listeners.length;
        }

        return stats;
    }

    /**
     * 启用/禁用调试模式
     * @param {boolean} enabled - 是否启用调试
     */
    setDebug(enabled) {
        this.debug = enabled;
        console.log(`🐛 [EventBus] 调试模式: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 检查事件是否有监听器
     * @param {string} event - 事件名称
     */
    hasListeners(event) {
        return this.events.has(event) && this.events.get(event).length > 0;
    }

    /**
     * 获取事件的监听器数量
     * @param {string} event - 事件名称
     */
    getListenerCount(event) {
        return this.events.has(event) ? this.events.get(event).length : 0;
    }
}

} // 结束重复声明保护

// 安全的全局导出，兼容属性保护
try {
    if (typeof window.EventBus === 'undefined' && typeof EventBus !== 'undefined') {
        window.EventBus = EventBus;
        console.log('✅ [EventBus] 类已注册到全局对象');
    }
} catch (error) {
    // 如果属性被保护，直接设置
    if (typeof EventBus !== 'undefined') {
        window.EventBus = EventBus;
        console.log('✅ [EventBus] 类已强制注册到全局对象');
    }
}

// 创建全局事件总线实例 - 确保总是创建
if (!window.mdacEventBus && typeof window.EventBus !== 'undefined') {
    window.mdacEventBus = new window.EventBus();
    console.log('🚌 [EventBus] 全局事件总线实例已创建');
}

// 导出类和实例 - 兼容 ultimate-bootstrap 属性保护系统
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EventBus: window.EventBus, eventBus: window.mdacEventBus };
}

console.log('✅ [EventBus] 事件总线模块已加载');
