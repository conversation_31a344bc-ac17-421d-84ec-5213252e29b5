/**
 * 模块初始化器 - 负责按正确顺序初始化所有核心模块
 * 确保模块依赖关系正确加载和初始化
 * 创建日期: 2025-01-11
 */

class ModuleInitializer {
    constructor() {
        this.initializationStatus = {
            started: false,
            completed: false,
            failed: false,
            error: null
        };
        
        this.loadedModules = new Set();
        this.initializationSteps = [];
        
        console.log('🚀 [ModuleInitializer] 模块初始化器已创建');
    }

    /**
     * 初始化所有核心模块
     */
    async initializeCore() {
        if (this.initializationStatus.started) {
            console.warn('⚠️ [ModuleInitializer] 初始化已经开始');
            return;
        }

        this.initializationStatus.started = true;
        console.log('🚀 [ModuleInitializer] 开始初始化核心模块...');

        try {
            // 步骤1: 验证基础环境
            await this.validateEnvironment();
            
            // 步骤2: 加载核心基础模块
            await this.loadCoreFoundation();
            
            // 步骤3: 加载工具模块
            await this.loadUtilityModules();
            
            // 步骤4: 初始化管理器
            await this.initializeManagers();
            
            // 步骤5: 验证初始化结果
            await this.validateInitialization();

            this.initializationStatus.completed = true;
            console.log('✅ [ModuleInitializer] 核心模块初始化完成');

            // 发布初始化完成事件
            if (window.mdacEventBus) {
                window.mdacEventBus.emit('core:initialization-complete', {
                    timestamp: Date.now(),
                    loadedModules: Array.from(this.loadedModules),
                    steps: this.initializationSteps
                });
            }

        } catch (error) {
            this.initializationStatus.failed = true;
            this.initializationStatus.error = error;
            
            console.error('❌ [ModuleInitializer] 核心模块初始化失败', error);
            
            // 发布初始化失败事件
            if (window.mdacEventBus) {
                window.mdacEventBus.emit('core:initialization-failed', {
                    error: error.message,
                    stack: error.stack,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 验证基础环境
     */
    async validateEnvironment() {
        this.addStep('验证基础环境');
        
        // 检查Chrome扩展API
        if (typeof chrome === 'undefined') {
            throw new Error('Chrome扩展API不可用');
        }

        // 检查必要的DOM元素
        if (!document.body) {
            throw new Error('DOM未准备就绪');
        }

        // 检查基础全局对象
        const requiredGlobals = ['window', 'document', 'console'];
        for (const global of requiredGlobals) {
            if (typeof window[global] === 'undefined') {
                throw new Error(`全局对象不可用: ${global}`);
            }
        }

        console.log('✅ [ModuleInitializer] 基础环境验证通过');
    }

    /**
     * 加载核心基础模块
     */
    async loadCoreFoundation() {
        this.addStep('加载核心基础模块');
        
        // 确保EventBus已加载
        if (!window.mdacEventBus) {
            throw new Error('EventBus未加载');
        }
        this.markModuleLoaded('EventBus');

        // 确保ModuleLoader已加载
        if (!window.mdacModuleLoader) {
            throw new Error('ModuleLoader未加载');
        }
        this.markModuleLoaded('ModuleLoader');

        // 确保ModuleRegistry已加载
        if (!window.mdacModuleRegistry) {
            throw new Error('ModuleRegistry未加载');
        }
        this.markModuleLoaded('ModuleRegistry');

        console.log('✅ [ModuleInitializer] 核心基础模块加载完成');
    }

    /**
     * 加载工具模块
     */
    async loadUtilityModules() {
        this.addStep('加载工具模块');
        
        // 检查工具类是否已加载
        const utilityModules = [
            { name: 'DateFormatter', class: 'DateFormatter' },
            { name: 'MessageHelper', class: 'MessageHelper' },
            { name: 'DebugLogger', class: 'DebugLogger' }
        ];

        for (const module of utilityModules) {
            if (window[module.class]) {
                this.markModuleLoaded(module.name);
                console.log(`✅ [ModuleInitializer] ${module.name} 已加载`);
            } else {
                console.warn(`⚠️ [ModuleInitializer] ${module.name} 未加载，将在后续阶段加载`);
            }
        }

        console.log('✅ [ModuleInitializer] 工具模块检查完成');
    }

    /**
     * 初始化管理器
     */
    async initializeManagers() {
        this.addStep('初始化管理器');
        
        // 检查管理器类是否已加载
        const managerModules = [
            { name: 'StateManager', class: 'StateManager' },
            { name: 'EventManager', class: 'EventManager' }
        ];

        for (const module of managerModules) {
            if (window[module.class]) {
                this.markModuleLoaded(module.name);
                console.log(`✅ [ModuleInitializer] ${module.name} 类已加载`);
            } else {
                throw new Error(`管理器类未加载: ${module.name}`);
            }
        }

        // 检查SidePanelCore
        if (window.SidePanelCore) {
            this.markModuleLoaded('SidePanelCore');
            console.log('✅ [ModuleInitializer] SidePanelCore 类已加载');
        } else {
            throw new Error('SidePanelCore类未加载');
        }

        console.log('✅ [ModuleInitializer] 管理器初始化完成');
    }

    /**
     * 验证初始化结果
     */
    async validateInitialization() {
        this.addStep('验证初始化结果');
        
        // 验证事件总线功能
        try {
            window.mdacEventBus.emit('test:initialization', { test: true });
            console.log('✅ [ModuleInitializer] 事件总线功能正常');
        } catch (error) {
            throw new Error(`事件总线功能异常: ${error.message}`);
        }

        // 验证模块注册表
        try {
            const validation = window.mdacModuleRegistry.validate();
            if (!validation.valid) {
                throw new Error(`模块注册表验证失败: ${validation.errors.join(', ')}`);
            }
            console.log('✅ [ModuleInitializer] 模块注册表验证通过');
        } catch (error) {
            throw new Error(`模块注册表验证异常: ${error.message}`);
        }

        // 验证必需模块数量
        const requiredModules = ['EventBus', 'ModuleLoader', 'ModuleRegistry', 'StateManager', 'EventManager', 'SidePanelCore'];
        const loadedCount = requiredModules.filter(module => this.loadedModules.has(module)).length;
        
        if (loadedCount < requiredModules.length) {
            const missing = requiredModules.filter(module => !this.loadedModules.has(module));
            throw new Error(`缺少必需模块: ${missing.join(', ')}`);
        }

        console.log(`✅ [ModuleInitializer] 已加载 ${loadedCount}/${requiredModules.length} 个必需模块`);
    }

    /**
     * 添加初始化步骤
     * @param {string} stepName - 步骤名称
     */
    addStep(stepName) {
        const step = {
            name: stepName,
            timestamp: Date.now(),
            completed: false
        };
        
        this.initializationSteps.push(step);
        console.log(`🔄 [ModuleInitializer] 执行步骤: ${stepName}`);
        
        return step;
    }

    /**
     * 标记步骤完成
     * @param {Object} step - 步骤对象
     */
    completeStep(step) {
        step.completed = true;
        step.completedAt = Date.now();
        step.duration = step.completedAt - step.timestamp;
    }

    /**
     * 标记模块已加载
     * @param {string} moduleName - 模块名称
     */
    markModuleLoaded(moduleName) {
        this.loadedModules.add(moduleName);
    }

    /**
     * 获取初始化状态
     */
    getStatus() {
        return {
            ...this.initializationStatus,
            loadedModules: Array.from(this.loadedModules),
            steps: this.initializationSteps,
            totalSteps: this.initializationSteps.length,
            completedSteps: this.initializationSteps.filter(step => step.completed).length
        };
    }

    /**
     * 获取初始化报告
     */
    getInitializationReport() {
        const status = this.getStatus();
        const totalDuration = this.initializationSteps.reduce((sum, step) => {
            return sum + (step.duration || 0);
        }, 0);

        return {
            status: status.completed ? 'SUCCESS' : status.failed ? 'FAILED' : 'IN_PROGRESS',
            totalDuration,
            loadedModules: status.loadedModules,
            steps: this.initializationSteps.map(step => ({
                name: step.name,
                duration: step.duration || 0,
                completed: step.completed
            })),
            error: status.error,
            timestamp: Date.now()
        };
    }

    /**
     * 重置初始化状态
     */
    reset() {
        this.initializationStatus = {
            started: false,
            completed: false,
            failed: false,
            error: null
        };
        
        this.loadedModules.clear();
        this.initializationSteps = [];
        
        console.log('🔄 [ModuleInitializer] 初始化状态已重置');
    }

    /**
     * 创建核心实例
     * @param {Object} options - 初始化选项
     */
    async createCoreInstance(options = {}) {
        if (!this.initializationStatus.completed) {
            throw new Error('核心模块未完成初始化');
        }

        console.log('🏗️ [ModuleInitializer] 创建SidePanelCore实例...');
        
        try {
            const sidePanelCore = new SidePanelCore();
            await sidePanelCore.initialize(options);
            
            console.log('✅ [ModuleInitializer] SidePanelCore实例创建成功');
            return sidePanelCore;
            
        } catch (error) {
            console.error('❌ [ModuleInitializer] SidePanelCore实例创建失败', error);
            throw error;
        }
    }
}

// 创建全局初始化器实例
window.mdacModuleInitializer = new ModuleInitializer();

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModuleInitializer, moduleInitializer: window.mdacModuleInitializer };
} else {
    window.ModuleInitializer = ModuleInitializer;
}

console.log('✅ [ModuleInitializer] 模块初始化器已加载');
