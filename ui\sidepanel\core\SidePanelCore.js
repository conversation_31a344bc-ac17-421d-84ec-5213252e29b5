/**
 * 侧边栏核心类 - 重构后的主控制器
 * 负责协调所有模块，提供统一的API接口
 * 创建日期: 2025-01-11
 */

// 防止重复声明 - 修复版本
if (typeof window.SidePanelCore !== 'undefined') {
    console.warn('⚠️ [SidePanelCore] 类已存在，跳过重复声明');
} else {

class SidePanelCore {
    constructor() {
        // 核心组件
        this.eventBus = window.mdacEventBus;
        this.moduleLoader = window.mdacModuleLoader;
        this.moduleRegistry = window.mdacModuleRegistry;
        
        // 管理器实例
        this.stateManager = null;
        this.eventManager = null;
        
        // 服务实例
        this.aiService = null;
        this.formFiller = null;
        this.uiRenderer = null;
        this.dataManager = null;
        
        // 功能模块实例
        this.autoParseManager = null;
        this.cityViewer = null;
        this.confidenceEvaluator = null;
        
        // 初始化状态
        this.isInitialized = false;
        this.isDestroyed = false;
        
        // 配置
        this.config = {
            autoInitialize: true,
            enableDebug: false,
            moduleTimeout: 10000
        };

        console.log('🏗️ [SidePanelCore] 侧边栏核心已创建');
    }

    /**
     * 初始化侧边栏
     * @param {Object} options - 初始化选项
     */
    async initialize(options = {}) {
        if (this.isInitialized) {
            console.warn('⚠️ [SidePanelCore] 侧边栏已经初始化');
            return;
        }

        try {
            console.log('🚀 [SidePanelCore] 开始初始化侧边栏...');
            
            // 合并配置
            this.config = { ...this.config, ...options };
            
            // 设置调试模式
            if (this.config.enableDebug && this.eventBus) {
                this.eventBus.setDebug(true);
            }

            // 阶段1: 加载核心模块
            await this.loadCoreModules();
            
            // 阶段2: 初始化管理器
            await this.initializeManagers();
            
            // 阶段3: 加载服务模块
            await this.loadServiceModules();
            
            // 阶段4: 加载功能模块
            await this.loadFeatureModules();
            
            // 阶段5: 初始化UI
            await this.initializeUI();
            
            // 阶段6: 启动事件系统
            await this.startEventSystem();

            this.isInitialized = true;
            console.log('✅ [SidePanelCore] 侧边栏初始化完成');

            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('sidePanel:initialized', {
                    timestamp: Date.now(),
                    config: this.config
                });
            }

        } catch (error) {
            console.error('❌ [SidePanelCore] 侧边栏初始化失败', error);
            
            // 发布初始化失败事件
            if (this.eventBus) {
                this.eventBus.emit('sidePanel:initialization-failed', {
                    error: error.message,
                    stack: error.stack,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 加载核心模块
     */
    async loadCoreModules() {
        console.log('📦 [SidePanelCore] 加载核心模块...');

        // 确保模块注册表已初始化
        if (this.moduleRegistry && !this.moduleRegistry.isInitialized) {
            this.moduleRegistry.initializeModuleDefinitions();
        }

        // 同步模块配置到模块加载器
        if (this.moduleRegistry && this.moduleLoader) {
            const allModules = this.moduleRegistry.getAllModules();
            for (const module of allModules) {
                this.moduleLoader.registerModule(module.name, module);
            }
            console.log('✅ [SidePanelCore] 模块配置已同步到加载器');
        }

        const coreModules = [
            'StateManager',
            'EventManager'
        ];

        // 由于模块已通过HTML加载，这里只需验证类是否可用
        for (const moduleName of coreModules) {
            const moduleConfig = this.moduleRegistry.getModule(moduleName);
            if (moduleConfig && window[moduleConfig.globalName]) {
                console.log(`✅ [SidePanelCore] 模块已加载: ${moduleName}`);
            } else {
                throw new Error(`模块未加载或配置未找到: ${moduleName}`);
            }
        }
    }

    /**
     * 初始化管理器
     */
    async initializeManagers() {
        console.log('🛠️ [SidePanelCore] 初始化管理器...');
        
        // 初始化状态管理器
        this.stateManager = new StateManager(this.eventBus);
        
        // 初始化事件管理器
        this.eventManager = new EventManager(this, this.eventBus, this.stateManager);
        await this.eventManager.initialize();
        
        console.log('✅ [SidePanelCore] 管理器初始化完成');
    }

    /**
     * 加载服务模块
     */
    async loadServiceModules() {
        console.log('🔧 [SidePanelCore] 加载服务模块...');
        
        const serviceModules = [
            'AIService',
            'FormFiller',
            'UIRenderer',
            'DataManager'
        ];

        // 并行加载服务模块
        await this.moduleLoader.loadModules(serviceModules, { parallel: true });
        
        // 初始化服务实例
        this.aiService = new AIService(this.eventBus, this.stateManager);
        this.formFiller = new FormFiller(this.eventBus, this.stateManager);
        this.uiRenderer = new UIRenderer(this.eventBus, this.stateManager);
        this.dataManager = new DataManager(this.eventBus, this.stateManager);
    }

    /**
     * 加载功能模块
     */
    async loadFeatureModules() {
        console.log('🎯 [SidePanelCore] 加载功能模块...');
        
        const featureModules = [
            'AutoParseManager',
            'CityViewer',
            'ConfidenceEvaluator'
        ];

        // 并行加载功能模块
        await this.moduleLoader.loadModules(featureModules, { parallel: true });
        
        // 初始化功能实例
        this.autoParseManager = new AutoParseManager(this.eventBus, this.stateManager, this.aiService);
        this.cityViewer = new CityViewer(this.eventBus, this.stateManager, this.uiRenderer);
        this.confidenceEvaluator = new ConfidenceEvaluator(this.eventBus, this.stateManager);
    }

    /**
     * 初始化UI
     */
    async initializeUI() {
        console.log('🎨 [SidePanelCore] 初始化UI...');
        
        if (this.uiRenderer) {
            await this.uiRenderer.initialize();
        }
        
        // 设置初始状态
        if (this.stateManager) {
            this.stateManager.set('ui.isVisible', true);
            this.stateManager.set('ui.currentTab', 'input');
        }
    }

    /**
     * 启动事件系统
     */
    async startEventSystem() {
        console.log('🎯 [SidePanelCore] 启动事件系统...');
        
        // 注册核心事件处理器
        this.registerCoreEventHandlers();
        
        // 启动自动解析管理器
        if (this.autoParseManager) {
            this.autoParseManager.start();
        }
    }

    /**
     * 注册核心事件处理器
     */
    registerCoreEventHandlers() {
        if (!this.eventBus) return;

        // 处理模块加载事件
        this.eventBus.on('module:loaded', (data) => {
            console.log(`📦 [SidePanelCore] 模块已加载: ${data.moduleName}`);
        });

        // 处理错误事件
        this.eventBus.on('error:occurred', (data) => {
            this.handleError(data);
        });

        // 处理状态变更事件
        this.eventBus.on('state:changed', (data) => {
            this.handleStateChange(data);
        });
    }

    // ==================== 公共API方法 ====================

    /**
     * 解析内容
     * @param {string} text - 文本内容
     * @param {File} image - 图片文件
     */
    async parseContent(text = null, image = null) {
        try {
            console.log('🤖 [SidePanelCore] 开始解析内容');
            
            if (!this.aiService) {
                throw new Error('AI服务未初始化');
            }

            // 获取输入内容
            const inputText = text || this.stateManager?.get('data.inputText') || '';
            const inputImage = image || this.stateManager?.get('data.inputImage');

            if (!inputText && !inputImage) {
                throw new Error('请输入文本或上传图片');
            }

            // 发布解析开始事件
            this.eventBus?.emit('ai:processing-start', {
                text: inputText,
                hasImage: !!inputImage
            });

            // 执行解析
            const result = await this.aiService.parseContent(inputText, inputImage);

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('data.parsedData', result.data);
                this.stateManager.set('data.confidence', result.confidence);
            }

            // 发布解析完成事件
            this.eventBus?.emit('ai:processing-complete', {
                result,
                timestamp: Date.now()
            });

            return result;

        } catch (error) {
            console.error('❌ [SidePanelCore] 内容解析失败', error);
            
            // 发布解析错误事件
            this.eventBus?.emit('ai:processing-error', {
                error: error.message,
                timestamp: Date.now()
            });
            
            throw error;
        }
    }

    /**
     * 填充表单
     * @param {Object} data - 要填充的数据
     */
    async fillForm(data = null) {
        try {
            console.log('📝 [SidePanelCore] 开始填充表单');
            
            if (!this.formFiller) {
                throw new Error('表单填充器未初始化');
            }

            // 获取填充数据
            const fillData = data || this.stateManager?.get('data.parsedData');
            
            if (!fillData) {
                throw new Error('没有可填充的数据，请先解析内容');
            }

            // 发布填充开始事件
            this.eventBus?.emit('form:fill-start', {
                data: fillData
            });

            // 执行填充
            const result = await this.formFiller.fillForm(fillData);

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('form.lastFillResult', result);
            }

            // 发布填充完成事件
            this.eventBus?.emit('form:fill-complete', {
                result,
                timestamp: Date.now()
            });

            return result;

        } catch (error) {
            console.error('❌ [SidePanelCore] 表单填充失败', error);
            
            // 发布填充错误事件
            this.eventBus?.emit('form:fill-error', {
                error: error.message,
                timestamp: Date.now()
            });
            
            throw error;
        }
    }

    /**
     * 清除所有数据
     */
    clearAll() {
        console.log('🧹 [SidePanelCore] 清除所有数据');
        
        if (this.stateManager) {
            this.stateManager.batchUpdate({
                'data.inputText': '',
                'data.inputImage': null,
                'data.parsedData': null,
                'data.confidence': {},
                'data.validationErrors': [],
                'form.fillProgress': 0,
                'form.lastFillResult': null,
                'errors.lastError': null
            });
        }

        // 清除UI
        if (this.uiRenderer) {
            this.uiRenderer.clearAll();
        }

        // 发布清除事件
        this.eventBus?.emit('data:cleared', {
            timestamp: Date.now()
        });
    }

    /**
     * 处理图片上传
     * @param {File} file - 图片文件
     */
    async handleImageUpload(file) {
        try {
            console.log('🖼️ [SidePanelCore] 处理图片上传');
            
            if (!file || !file.type.startsWith('image/')) {
                throw new Error('请选择有效的图片文件');
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('data.inputImage', file);
                this.stateManager.set('ui.isLoading', true);
                this.stateManager.set('ui.loadingMessage', '正在处理图片...');
            }

            // 如果启用了自动解析，则自动解析图片
            if (this.stateManager?.get('settings.autoParseEnabled')) {
                await this.parseContent(null, file);
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('ui.isLoading', false);
            }

        } catch (error) {
            console.error('❌ [SidePanelCore] 图片上传处理失败', error);
            
            if (this.stateManager) {
                this.stateManager.set('ui.isLoading', false);
                this.stateManager.set('errors.lastError', error.message);
            }
            
            throw error;
        }
    }

    /**
     * 处理Chrome消息
     * @param {Object} message - 消息对象
     * @param {Object} sender - 发送者信息
     * @param {Function} sendResponse - 响应函数
     */
    handleMessage(message, sender, sendResponse) {
        console.log('📨 [SidePanelCore] 处理Chrome消息', message);
        
        try {
            switch (message.action) {
                case 'parseContent':
                    this.parseContent(message.text, message.image)
                        .then(result => sendResponse({ success: true, result }))
                        .catch(error => sendResponse({ success: false, error: error.message }));
                    return true; // 异步响应
                    
                case 'fillForm':
                    this.fillForm(message.data)
                        .then(result => sendResponse({ success: true, result }))
                        .catch(error => sendResponse({ success: false, error: error.message }));
                    return true; // 异步响应
                    
                case 'getState':
                    sendResponse({ 
                        success: true, 
                        state: this.stateManager?.getState() 
                    });
                    break;
                    
                case 'clearAll':
                    this.clearAll();
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ success: false, error: '未知的操作' });
            }
        } catch (error) {
            console.error('❌ [SidePanelCore] 消息处理失败', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // ==================== 事件处理方法 ====================

    /**
     * 处理错误
     * @param {Object} errorData - 错误数据
     */
    handleError(errorData) {
        console.error('❌ [SidePanelCore] 处理错误', errorData);
        
        // 更新错误状态
        if (this.stateManager) {
            this.stateManager.set('errors.lastError', errorData);
        }

        // 显示错误消息
        if (this.uiRenderer) {
            this.uiRenderer.showError(errorData.error || errorData.message);
        }
    }

    /**
     * 处理状态变更
     * @param {Object} changeData - 状态变更数据
     */
    handleStateChange(changeData) {
        // 这里可以添加状态变更的通用处理逻辑
        // 例如：持久化某些状态、触发UI更新等
    }

    // ==================== 工具方法 ====================

    /**
     * 获取当前状态
     */
    getState() {
        return this.stateManager?.getState();
    }

    /**
     * 获取模块加载状态
     */
    getModuleStatus() {
        return this.moduleLoader?.getLoadStatus();
    }

    /**
     * 检查是否已初始化
     */
    isReady() {
        return this.isInitialized && !this.isDestroyed;
    }

    /**
     * 销毁侧边栏
     */
    destroy() {
        if (this.isDestroyed) return;

        console.log('🗑️ [SidePanelCore] 销毁侧边栏');

        // 停止自动解析
        if (this.autoParseManager) {
            this.autoParseManager.stop();
        }

        // 销毁管理器
        if (this.eventManager) {
            this.eventManager.destroy();
        }

        if (this.stateManager) {
            this.stateManager.destroy();
        }

        // 清理服务
        [this.aiService, this.formFiller, this.uiRenderer, this.dataManager].forEach(service => {
            if (service && typeof service.destroy === 'function') {
                service.destroy();
            }
        });

        // 清理功能模块
        [this.autoParseManager, this.cityViewer, this.confidenceEvaluator].forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });

        this.isDestroyed = true;
        console.log('✅ [SidePanelCore] 侧边栏已销毁');
    }
}

} // 结束重复声明保护

// 导出类 - 兼容 ultimate-bootstrap 属性保护系统
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidePanelCore;
}

// 安全的全局导出，兼容属性保护
try {
    if (typeof window.SidePanelCore === 'undefined') {
        window.SidePanelCore = SidePanelCore;
    }
} catch (error) {
    // 如果属性被保护，直接设置
    window.SidePanelCore = SidePanelCore;
}

console.log('✅ [SidePanelCore] 侧边栏核心模块已加载');
