/**
 * 进度可视化器 - 进度条和状态显示组件
 * 负责显示各种操作的进度和状态信息
 * 创建日期: 2025-01-11
 */

class ProgressVisualizer {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 进度容器
        this.progressContainer = null;
        
        // 活跃的进度条
        this.activeProgress = new Map();
        
        // 配置
        this.config = {
            animationDuration: 300,
            autoHide: true,
            autoHideDelay: 3000,
            showPercentage: true,
            showETA: true,
            position: 'top-right'
        };

        // 进度条模板
        this.progressTemplates = {
            linear: this.createLinearTemplate.bind(this),
            circular: this.createCircularTemplate.bind(this),
            step: this.createStepTemplate.bind(this),
            mini: this.createMiniTemplate.bind(this)
        };

        console.log('📊 [ProgressVisualizer] 进度可视化器已初始化');
        this.initializeContainer();
        this.initializeEventListeners();
    }

    /**
     * 初始化进度容器
     */
    initializeContainer() {
        // 检查是否已存在容器
        this.progressContainer = document.getElementById('mdac-progress-container');
        
        if (!this.progressContainer) {
            this.progressContainer = document.createElement('div');
            this.progressContainer.id = 'mdac-progress-container';
            this.progressContainer.className = `progress-container position-${this.config.position}`;
            
            // 添加样式
            this.progressContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                pointer-events: none;
            `;
            
            document.body.appendChild(this.progressContainer);
        }

        // 注入CSS样式
        this.injectStyles();
    }

    /**
     * 注入CSS样式
     */
    injectStyles() {
        const styleId = 'mdac-progress-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .progress-container {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            
            .progress-item {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 16px;
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                border-left: 4px solid #007bff;
            }
            
            .progress-item.show {
                transform: translateX(0);
                opacity: 1;
            }
            
            .progress-item.hide {
                transform: translateX(100%);
                opacity: 0;
            }
            
            .progress-item.success {
                border-left-color: #28a745;
            }
            
            .progress-item.error {
                border-left-color: #dc3545;
            }
            
            .progress-item.warning {
                border-left-color: #ffc107;
            }
            
            .progress-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 12px;
            }
            
            .progress-title {
                font-weight: 600;
                color: #333;
                font-size: 14px;
                margin: 0;
            }
            
            .progress-close {
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: #999;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .progress-close:hover {
                color: #333;
            }
            
            .progress-bar-container {
                background: #f0f0f0;
                border-radius: 4px;
                height: 8px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .progress-bar {
                background: #007bff;
                height: 100%;
                width: 0%;
                transition: width 0.3s ease;
                border-radius: 4px;
            }
            
            .progress-bar.success {
                background: #28a745;
            }
            
            .progress-bar.error {
                background: #dc3545;
            }
            
            .progress-bar.warning {
                background: #ffc107;
            }
            
            .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                color: #666;
            }
            
            .progress-percentage {
                font-weight: 600;
            }
            
            .progress-eta {
                font-style: italic;
            }
            
            .progress-message {
                margin-top: 8px;
                font-size: 13px;
                color: #555;
                line-height: 1.4;
            }
            
            .progress-steps {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 12px;
            }
            
            .progress-step {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
                color: #999;
                position: relative;
            }
            
            .progress-step.active {
                background: #007bff;
                color: white;
            }
            
            .progress-step.completed {
                background: #28a745;
                color: white;
            }
            
            .progress-step.error {
                background: #dc3545;
                color: white;
            }
            
            .progress-step:not(:last-child)::after {
                content: '';
                position: absolute;
                left: 100%;
                top: 50%;
                transform: translateY(-50%);
                width: 8px;
                height: 2px;
                background: #ddd;
            }
            
            .progress-step.completed:not(:last-child)::after {
                background: #28a745;
            }
            
            .progress-circular {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .progress-circle {
                width: 40px;
                height: 40px;
                position: relative;
            }
            
            .progress-circle svg {
                width: 100%;
                height: 100%;
                transform: rotate(-90deg);
            }
            
            .progress-circle-bg {
                fill: none;
                stroke: #f0f0f0;
                stroke-width: 4;
            }
            
            .progress-circle-fill {
                fill: none;
                stroke: #007bff;
                stroke-width: 4;
                stroke-linecap: round;
                transition: stroke-dasharray 0.3s ease;
            }
            
            .progress-mini {
                padding: 8px 12px;
                font-size: 12px;
            }
            
            .progress-mini .progress-bar-container {
                height: 4px;
                margin-bottom: 4px;
            }
            
            .progress-mini .progress-header {
                margin-bottom: 6px;
            }
            
            .progress-loading {
                position: relative;
                overflow: hidden;
            }
            
            .progress-loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                animation: loading-shimmer 1.5s infinite;
            }
            
            @keyframes loading-shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听进度事件
        this.eventBus.on('progress:start', (data) => {
            this.startProgress(data.id, data.options);
        });

        this.eventBus.on('progress:update', (data) => {
            this.updateProgress(data.id, data.progress, data.message);
        });

        this.eventBus.on('progress:complete', (data) => {
            this.completeProgress(data.id, data.message);
        });

        this.eventBus.on('progress:error', (data) => {
            this.errorProgress(data.id, data.message);
        });

        this.eventBus.on('progress:hide', (data) => {
            this.hideProgress(data.id);
        });

        // 监听特定模块的进度事件
        this.eventBus.on('ai:processing-start', () => {
            this.startProgress('ai-processing', {
                title: 'AI处理中',
                message: '正在解析内容...',
                type: 'linear'
            });
        });

        this.eventBus.on('ai:processing-complete', () => {
            this.completeProgress('ai-processing', 'AI处理完成');
        });

        this.eventBus.on('form:fill-start', () => {
            this.startProgress('form-filling', {
                title: '表单填充',
                message: '正在填充表单字段...',
                type: 'linear'
            });
        });

        this.eventBus.on('form:fill-progress', (data) => {
            this.updateProgress('form-filling', data.progress, `已填充 ${data.completed}/${data.total} 个字段`);
        });

        this.eventBus.on('form:fill-complete', () => {
            this.completeProgress('form-filling', '表单填充完成');
        });
    }

    /**
     * 开始进度显示
     * @param {string} id - 进度ID
     * @param {Object} options - 选项
     */
    startProgress(id, options = {}) {
        try {
            console.log(`📊 [ProgressVisualizer] 开始进度: ${id}`, options);

            const {
                title = '处理中',
                message = '',
                type = 'linear',
                steps = null,
                closable = true,
                autoHide = this.config.autoHide
            } = options;

            // 如果已存在，先移除
            if (this.activeProgress.has(id)) {
                this.hideProgress(id);
            }

            // 创建进度元素
            const template = this.progressTemplates[type];
            if (!template) {
                throw new Error(`未知的进度类型: ${type}`);
            }

            const progressElement = template(options);
            progressElement.dataset.progressId = id;

            // 添加到容器
            this.progressContainer.appendChild(progressElement);

            // 创建进度对象
            const progress = {
                id: id,
                element: progressElement,
                type: type,
                options: options,
                progress: 0,
                status: 'active',
                startTime: Date.now(),
                autoHide: autoHide,
                steps: steps
            };

            // 注册进度
            this.activeProgress.set(id, progress);

            // 显示动画
            requestAnimationFrame(() => {
                progressElement.classList.add('show');
            });

            // 发布开始事件
            if (this.eventBus) {
                this.eventBus.emit('progress:started', {
                    id: id,
                    type: type,
                    timestamp: Date.now()
                });
            }

            return progress;

        } catch (error) {
            console.error('❌ [ProgressVisualizer] 进度开始失败', error);
            throw error;
        }
    }

    /**
     * 更新进度
     * @param {string} id - 进度ID
     * @param {number} progress - 进度百分比 (0-100)
     * @param {string} message - 消息
     */
    updateProgress(id, progress, message = null) {
        const progressObj = this.activeProgress.get(id);
        if (!progressObj) {
            console.warn(`⚠️ [ProgressVisualizer] 进度不存在: ${id}`);
            return;
        }

        console.log(`📊 [ProgressVisualizer] 更新进度: ${id} -> ${progress}%`);

        // 更新进度值
        progressObj.progress = Math.max(0, Math.min(100, progress));

        // 更新进度条
        const progressBar = progressObj.element.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progressObj.progress}%`;
        }

        // 更新百分比显示
        const percentageElement = progressObj.element.querySelector('.progress-percentage');
        if (percentageElement && this.config.showPercentage) {
            percentageElement.textContent = `${Math.round(progressObj.progress)}%`;
        }

        // 更新消息
        if (message) {
            const messageElement = progressObj.element.querySelector('.progress-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }

        // 更新ETA
        if (this.config.showETA && progressObj.progress > 0) {
            this.updateETA(progressObj);
        }

        // 更新圆形进度条
        if (progressObj.type === 'circular') {
            this.updateCircularProgress(progressObj);
        }

        // 发布更新事件
        if (this.eventBus) {
            this.eventBus.emit('progress:updated', {
                id: id,
                progress: progressObj.progress,
                message: message,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 完成进度
     * @param {string} id - 进度ID
     * @param {string} message - 完成消息
     */
    completeProgress(id, message = '完成') {
        const progressObj = this.activeProgress.get(id);
        if (!progressObj) return;

        console.log(`✅ [ProgressVisualizer] 进度完成: ${id}`);

        // 更新状态
        progressObj.status = 'completed';
        progressObj.progress = 100;

        // 更新样式
        progressObj.element.classList.add('success');
        
        // 更新进度条
        const progressBar = progressObj.element.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.classList.add('success');
        }

        // 更新消息
        const messageElement = progressObj.element.querySelector('.progress-message');
        if (messageElement) {
            messageElement.textContent = message;
        }

        // 更新百分比
        const percentageElement = progressObj.element.querySelector('.progress-percentage');
        if (percentageElement) {
            percentageElement.textContent = '100%';
        }

        // 自动隐藏
        if (progressObj.autoHide) {
            setTimeout(() => {
                this.hideProgress(id);
            }, this.config.autoHideDelay);
        }

        // 发布完成事件
        if (this.eventBus) {
            this.eventBus.emit('progress:completed', {
                id: id,
                message: message,
                duration: Date.now() - progressObj.startTime,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 进度错误
     * @param {string} id - 进度ID
     * @param {string} message - 错误消息
     */
    errorProgress(id, message = '处理失败') {
        const progressObj = this.activeProgress.get(id);
        if (!progressObj) return;

        console.log(`❌ [ProgressVisualizer] 进度错误: ${id}`);

        // 更新状态
        progressObj.status = 'error';

        // 更新样式
        progressObj.element.classList.add('error');
        
        // 更新进度条
        const progressBar = progressObj.element.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.classList.add('error');
        }

        // 更新消息
        const messageElement = progressObj.element.querySelector('.progress-message');
        if (messageElement) {
            messageElement.textContent = message;
        }

        // 自动隐藏
        if (progressObj.autoHide) {
            setTimeout(() => {
                this.hideProgress(id);
            }, this.config.autoHideDelay * 2); // 错误时显示更长时间
        }

        // 发布错误事件
        if (this.eventBus) {
            this.eventBus.emit('progress:errored', {
                id: id,
                message: message,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 隐藏进度
     * @param {string} id - 进度ID
     */
    hideProgress(id) {
        const progressObj = this.activeProgress.get(id);
        if (!progressObj) return;

        console.log(`📊 [ProgressVisualizer] 隐藏进度: ${id}`);

        // 隐藏动画
        progressObj.element.classList.add('hide');

        // 延迟移除元素
        setTimeout(() => {
            if (progressObj.element.parentElement) {
                progressObj.element.remove();
            }
            this.activeProgress.delete(id);
        }, this.config.animationDuration);

        // 发布隐藏事件
        if (this.eventBus) {
            this.eventBus.emit('progress:hidden', {
                id: id,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 创建线性进度条模板
     * @param {Object} options - 选项
     */
    createLinearTemplate(options) {
        const {
            title = '处理中',
            message = '',
            closable = true
        } = options;

        const element = document.createElement('div');
        element.className = 'progress-item';

        element.innerHTML = `
            <div class="progress-header">
                <h4 class="progress-title">${title}</h4>
                ${closable ? '<button class="progress-close" data-action="close">×</button>' : ''}
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar"></div>
            </div>
            <div class="progress-info">
                <span class="progress-percentage">0%</span>
                <span class="progress-eta"></span>
            </div>
            ${message ? `<div class="progress-message">${message}</div>` : ''}
        `;

        this.bindProgressEvents(element);
        return element;
    }

    /**
     * 创建圆形进度条模板
     * @param {Object} options - 选项
     */
    createCircularTemplate(options) {
        const {
            title = '处理中',
            message = '',
            closable = true,
            size = 40
        } = options;

        const element = document.createElement('div');
        element.className = 'progress-item';

        const radius = (size - 8) / 2;
        const circumference = 2 * Math.PI * radius;

        element.innerHTML = `
            <div class="progress-header">
                <h4 class="progress-title">${title}</h4>
                ${closable ? '<button class="progress-close" data-action="close">×</button>' : ''}
            </div>
            <div class="progress-circular">
                <div class="progress-circle" style="width: ${size}px; height: ${size}px;">
                    <svg>
                        <circle class="progress-circle-bg" cx="${size/2}" cy="${size/2}" r="${radius}"></circle>
                        <circle class="progress-circle-fill" cx="${size/2}" cy="${size/2}" r="${radius}" 
                                stroke-dasharray="${circumference}" stroke-dashoffset="${circumference}"></circle>
                    </svg>
                </div>
                <div>
                    <div class="progress-percentage">0%</div>
                    ${message ? `<div class="progress-message">${message}</div>` : ''}
                </div>
            </div>
        `;

        this.bindProgressEvents(element);
        return element;
    }

    /**
     * 创建步骤进度条模板
     * @param {Object} options - 选项
     */
    createStepTemplate(options) {
        const {
            title = '处理中',
            steps = ['步骤1', '步骤2', '步骤3'],
            closable = true
        } = options;

        const element = document.createElement('div');
        element.className = 'progress-item';

        const stepsHtml = steps.map((step, index) => 
            `<div class="progress-step" data-step="${index}">${index + 1}</div>`
        ).join('');

        element.innerHTML = `
            <div class="progress-header">
                <h4 class="progress-title">${title}</h4>
                ${closable ? '<button class="progress-close" data-action="close">×</button>' : ''}
            </div>
            <div class="progress-steps">
                ${stepsHtml}
            </div>
            <div class="progress-message"></div>
        `;

        this.bindProgressEvents(element);
        return element;
    }

    /**
     * 创建迷你进度条模板
     * @param {Object} options - 选项
     */
    createMiniTemplate(options) {
        const {
            title = '处理中',
            closable = false
        } = options;

        const element = document.createElement('div');
        element.className = 'progress-item progress-mini';

        element.innerHTML = `
            <div class="progress-header">
                <h4 class="progress-title">${title}</h4>
                ${closable ? '<button class="progress-close" data-action="close">×</button>' : ''}
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar"></div>
            </div>
        `;

        this.bindProgressEvents(element);
        return element;
    }

    /**
     * 绑定进度事件
     * @param {Element} element - 进度元素
     */
    bindProgressEvents(element) {
        element.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'close') {
                const progressId = element.dataset.progressId;
                this.hideProgress(progressId);
            }
        });
    }

    /**
     * 更新ETA
     * @param {Object} progressObj - 进度对象
     */
    updateETA(progressObj) {
        const elapsed = Date.now() - progressObj.startTime;
        const rate = progressObj.progress / elapsed;
        const remaining = (100 - progressObj.progress) / rate;

        if (remaining > 0 && remaining < Infinity) {
            const etaElement = progressObj.element.querySelector('.progress-eta');
            if (etaElement) {
                const seconds = Math.round(remaining / 1000);
                if (seconds < 60) {
                    etaElement.textContent = `剩余 ${seconds}秒`;
                } else {
                    const minutes = Math.round(seconds / 60);
                    etaElement.textContent = `剩余 ${minutes}分钟`;
                }
            }
        }
    }

    /**
     * 更新圆形进度条
     * @param {Object} progressObj - 进度对象
     */
    updateCircularProgress(progressObj) {
        const circle = progressObj.element.querySelector('.progress-circle-fill');
        if (!circle) return;

        const radius = parseFloat(circle.getAttribute('r'));
        const circumference = 2 * Math.PI * radius;
        const offset = circumference - (progressObj.progress / 100) * circumference;

        circle.style.strokeDashoffset = offset;
    }

    /**
     * 获取活跃进度数量
     */
    getActiveProgressCount() {
        return this.activeProgress.size;
    }

    /**
     * 获取进度信息
     * @param {string} id - 进度ID
     */
    getProgressInfo(id) {
        const progressObj = this.activeProgress.get(id);
        if (!progressObj) return null;

        return {
            id: progressObj.id,
            type: progressObj.type,
            progress: progressObj.progress,
            status: progressObj.status,
            startTime: progressObj.startTime,
            duration: Date.now() - progressObj.startTime
        };
    }

    /**
     * 隐藏所有进度
     */
    hideAllProgress() {
        console.log('📊 [ProgressVisualizer] 隐藏所有进度');
        
        const progressIds = Array.from(this.activeProgress.keys());
        progressIds.forEach(id => {
            this.hideProgress(id);
        });
    }

    /**
     * 销毁进度可视化器
     */
    destroy() {
        // 隐藏所有进度
        this.hideAllProgress();
        
        // 移除容器
        if (this.progressContainer && this.progressContainer.parentElement) {
            this.progressContainer.remove();
        }

        // 移除样式
        const styles = document.getElementById('mdac-progress-styles');
        if (styles) {
            styles.remove();
        }

        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('progress:start');
            this.eventBus.off('progress:update');
            this.eventBus.off('progress:complete');
            this.eventBus.off('progress:error');
            this.eventBus.off('progress:hide');
        }

        console.log('🗑️ [ProgressVisualizer] 进度可视化器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressVisualizer;
} else {
    window.ProgressVisualizer = ProgressVisualizer;
}

console.log('✅ [ProgressVisualizer] 进度可视化器模块已加载');
