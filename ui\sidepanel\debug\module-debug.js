/**
 * 调试脚本 - 检查模块加载状态
 */

(function() {
    console.log('🔍 [Debug] 开始检查模块状态...');
    
    // 检查需要的模块
    const requiredModules = ['EventBus', 'DebugLogger', 'StateManager', 'EventManager', 'SidePanelCore'];
    const moduleStatus = {};
    
    requiredModules.forEach(moduleName => {
        const exists = typeof window[moduleName] !== 'undefined';
        const isFunction = typeof window[moduleName] === 'function';
        
        moduleStatus[moduleName] = {
            exists,
            isFunction,
            type: typeof window[moduleName],
            value: exists ? window[moduleName] : 'undefined'
        };
        
        console.log(`${exists ? '✅' : '❌'} [Debug] ${moduleName}: ${exists ? (isFunction ? 'CLASS' : typeof window[moduleName]) : 'MISSING'}`);
    });
    
    // 检查全局事件总线
    const eventBusExists = typeof window.mdacEventBus !== 'undefined';
    console.log(`${eventBusExists ? '✅' : '❌'} [Debug] window.mdacEventBus: ${eventBusExists ? 'EXISTS' : 'MISSING'}`);
    
    // 检查已加载的脚本
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    console.log('📜 [Debug] 已加载的脚本:');
    scripts.forEach(script => {
        if (script.src.includes('sidepanel')) {
            console.log(`  - ${script.src}`);
        }
    });
    
    // 存储状态到全局对象
    window.mdacDebugInfo = {
        timestamp: new Date().toISOString(),
        moduleStatus,
        eventBusExists,
        loadedScripts: scripts.map(s => s.src).filter(src => src.includes('sidepanel'))
    };
    
    console.log('🔍 [Debug] 调试信息已保存到 window.mdacDebugInfo');
    
    // 如果EventBus存在但实例不存在，尝试创建
    if (window.EventBus && !window.mdacEventBus) {
        console.log('🔧 [Debug] 尝试创建EventBus实例...');
        try {
            window.mdacEventBus = new window.EventBus();
            console.log('✅ [Debug] EventBus实例创建成功');
        } catch (error) {
            console.error('❌ [Debug] EventBus实例创建失败:', error);
        }
    }
    
})();
