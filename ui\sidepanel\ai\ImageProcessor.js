/**
 * 图片处理器 - 图片上传、处理和OCR功能
 * 负责图片文件的处理、格式转换和文本提取
 * 创建日期: 2025-01-11
 */

class ImageProcessor {
    constructor(eventBus = window.mdacEventBus, aiService = null) {
        this.eventBus = eventBus;
        this.aiService = aiService;
        
        // 配置
        this.config = {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            supportedFormats: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
            maxWidth: 2048,
            maxHeight: 2048,
            quality: 0.8,
            enableOCR: true
        };

        // 处理状态
        this.processingQueue = [];
        this.processedImages = new Map();
        this.processingHistory = [];

        console.log('🖼️ [ImageProcessor] 图片处理器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听图片处理请求
        this.eventBus.on('image:process-request', (data) => {
            this.handleImageProcessRequest(data);
        });

        // 监听配置更新
        this.eventBus.on('image:config-updated', (config) => {
            this.updateConfig(config);
        });
    }

    /**
     * 处理图片文件
     * @param {File} file - 图片文件
     * @param {Object} options - 处理选项
     */
    async processImage(file, options = {}) {
        try {
            console.log('🖼️ [ImageProcessor] 开始处理图片', {
                fileName: file.name,
                fileSize: file.size,
                fileType: file.type,
                options
            });

            // 验证文件
            this.validateImageFile(file);

            // 发布处理开始事件
            if (this.eventBus) {
                this.eventBus.emit('image:processing-start', {
                    fileName: file.name,
                    fileSize: file.size,
                    timestamp: Date.now()
                });
            }

            const processingId = this.generateProcessingId();
            const startTime = Date.now();

            // 添加到处理队列
            this.processingQueue.push({
                id: processingId,
                file: file,
                startTime: startTime,
                status: 'processing'
            });

            // 读取文件
            const imageData = await this.readImageFile(file);
            
            // 处理图片
            const processedImage = await this.processImageData(imageData, options);
            
            // OCR文本提取
            let extractedText = null;
            if (this.config.enableOCR && options.enableOCR !== false) {
                extractedText = await this.extractTextFromImage(processedImage, options);
            }

            const processingTime = Date.now() - startTime;
            
            const result = {
                success: true,
                processingId: processingId,
                originalFile: {
                    name: file.name,
                    size: file.size,
                    type: file.type
                },
                processedImage: processedImage,
                extractedText: extractedText,
                processingTime: processingTime,
                timestamp: Date.now()
            };

            // 缓存结果
            this.processedImages.set(processingId, result);
            
            // 更新处理队列
            this.updateProcessingQueue(processingId, 'completed');
            
            // 记录处理历史
            this.recordProcessingHistory(result);

            console.log('✅ [ImageProcessor] 图片处理完成', {
                processingId,
                processingTime,
                hasText: !!extractedText
            });

            // 发布处理完成事件
            if (this.eventBus) {
                this.eventBus.emit('image:processing-complete', result);
            }

            return result;

        } catch (error) {
            console.error('❌ [ImageProcessor] 图片处理失败', error);

            const errorResult = {
                success: false,
                error: error.message,
                fileName: file.name,
                timestamp: Date.now()
            };

            // 发布处理错误事件
            if (this.eventBus) {
                this.eventBus.emit('image:processing-error', errorResult);
            }

            throw error;
        }
    }

    /**
     * 验证图片文件
     * @param {File} file - 图片文件
     */
    validateImageFile(file) {
        if (!file) {
            throw new Error('未提供文件');
        }

        if (!file.type || !this.config.supportedFormats.includes(file.type)) {
            throw new Error(`不支持的文件格式: ${file.type}。支持的格式: ${this.config.supportedFormats.join(', ')}`);
        }

        if (file.size > this.config.maxFileSize) {
            const maxSizeMB = this.config.maxFileSize / (1024 * 1024);
            throw new Error(`文件大小超限: ${(file.size / (1024 * 1024)).toFixed(2)}MB > ${maxSizeMB}MB`);
        }

        console.log('✅ [ImageProcessor] 文件验证通过');
    }

    /**
     * 读取图片文件
     * @param {File} file - 图片文件
     */
    readImageFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (event) => {
                resolve({
                    dataUrl: event.target.result,
                    arrayBuffer: null // 如果需要可以添加
                });
            };
            
            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };
            
            reader.readAsDataURL(file);
        });
    }

    /**
     * 处理图片数据
     * @param {Object} imageData - 图片数据
     * @param {Object} options - 处理选项
     */
    async processImageData(imageData, options = {}) {
        const {
            resize = true,
            compress = true,
            format = 'jpeg'
        } = options;

        try {
            // 创建图片元素
            const img = await this.createImageElement(imageData.dataUrl);
            
            // 计算新尺寸
            const newDimensions = this.calculateNewDimensions(img.width, img.height);
            
            // 创建canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = newDimensions.width;
            canvas.height = newDimensions.height;
            
            // 绘制图片
            ctx.drawImage(img, 0, 0, newDimensions.width, newDimensions.height);
            
            // 转换格式和压缩
            const outputFormat = `image/${format}`;
            const quality = compress ? this.config.quality : 1.0;
            const processedDataUrl = canvas.toDataURL(outputFormat, quality);
            
            // 计算压缩后的大小
            const originalSize = imageData.dataUrl.length;
            const processedSize = processedDataUrl.length;
            const compressionRatio = (1 - processedSize / originalSize) * 100;

            console.log('🔧 [ImageProcessor] 图片处理完成', {
                originalDimensions: { width: img.width, height: img.height },
                newDimensions: newDimensions,
                compressionRatio: compressionRatio.toFixed(2) + '%'
            });

            return {
                dataUrl: processedDataUrl,
                dimensions: newDimensions,
                format: outputFormat,
                quality: quality,
                originalSize: originalSize,
                processedSize: processedSize,
                compressionRatio: compressionRatio
            };

        } catch (error) {
            console.error('❌ [ImageProcessor] 图片数据处理失败', error);
            throw new Error(`图片处理失败: ${error.message}`);
        }
    }

    /**
     * 创建图片元素
     * @param {string} dataUrl - 图片数据URL
     */
    createImageElement(dataUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error('图片加载失败'));
            
            img.src = dataUrl;
        });
    }

    /**
     * 计算新尺寸
     * @param {number} originalWidth - 原始宽度
     * @param {number} originalHeight - 原始高度
     */
    calculateNewDimensions(originalWidth, originalHeight) {
        let newWidth = originalWidth;
        let newHeight = originalHeight;

        // 如果超过最大尺寸，按比例缩放
        if (originalWidth > this.config.maxWidth || originalHeight > this.config.maxHeight) {
            const widthRatio = this.config.maxWidth / originalWidth;
            const heightRatio = this.config.maxHeight / originalHeight;
            const ratio = Math.min(widthRatio, heightRatio);

            newWidth = Math.round(originalWidth * ratio);
            newHeight = Math.round(originalHeight * ratio);
        }

        return { width: newWidth, height: newHeight };
    }

    /**
     * 从图片提取文本 (OCR)
     * @param {Object} processedImage - 处理后的图片
     * @param {Object} options - OCR选项
     */
    async extractTextFromImage(processedImage, options = {}) {
        try {
            console.log('🔍 [ImageProcessor] 开始OCR文本提取');

            if (!this.aiService) {
                console.warn('⚠️ [ImageProcessor] AI服务未可用，跳过OCR');
                return null;
            }

            // 构建OCR提示词
            const ocrPrompt = this.buildOCRPrompt(options);
            
            // 将图片转换为File对象（模拟）
            const imageBlob = this.dataURLToBlob(processedImage.dataUrl);
            const imageFile = new File([imageBlob], 'image.jpg', { type: 'image/jpeg' });

            // 调用AI服务进行OCR
            const aiResponse = await this.aiService.makeAPIRequest(ocrPrompt, {
                includeImage: true,
                image: imageFile,
                skipCache: true
            });

            if (aiResponse && aiResponse.success) {
                const extractedText = this.parseOCRResponse(aiResponse.data);
                
                console.log('✅ [ImageProcessor] OCR文本提取完成', {
                    textLength: extractedText.length,
                    preview: extractedText.substring(0, 100) + '...'
                });

                return extractedText;
            } else {
                throw new Error('OCR处理失败');
            }

        } catch (error) {
            console.error('❌ [ImageProcessor] OCR文本提取失败', error);
            return null; // OCR失败不影响图片处理
        }
    }

    /**
     * 构建OCR提示词
     * @param {Object} options - OCR选项
     */
    buildOCRPrompt(options = {}) {
        const { language = 'zh-CN', extractionType = 'all' } = options;

        let prompt = '请识别图片中的所有文本内容，并按原始布局返回。\n\n';
        
        prompt += '要求：\n';
        prompt += '1. 保持原始的文本布局和格式\n';
        prompt += '2. 识别中文、英文和数字\n';
        prompt += '3. 保留标点符号和特殊字符\n';
        prompt += '4. 如果是表格，请保持表格结构\n';
        prompt += '5. 如果是表单，请标明字段名称和值\n\n';
        
        if (extractionType === 'structured') {
            prompt += '请特别关注以下信息类型：\n';
            prompt += '- 个人信息（姓名、性别、出生日期等）\n';
            prompt += '- 证件信息（护照号、身份证号等）\n';
            prompt += '- 联系信息（电话、邮箱、地址等）\n';
            prompt += '- 日期和时间信息\n';
            prompt += '- 数字和编号信息\n\n';
        }
        
        prompt += '请直接返回识别的文本内容，不需要额外说明。';

        return prompt;
    }

    /**
     * 解析OCR响应
     * @param {string} response - AI响应
     */
    parseOCRResponse(response) {
        if (!response || typeof response !== 'string') {
            return '';
        }

        // 清理响应文本
        let cleanedText = response.trim();
        
        // 移除可能的AI回复前缀
        const prefixes = [
            '识别结果：',
            '文本内容：',
            '图片中的文本：',
            'OCR结果：'
        ];
        
        for (const prefix of prefixes) {
            if (cleanedText.startsWith(prefix)) {
                cleanedText = cleanedText.substring(prefix.length).trim();
                break;
            }
        }

        return cleanedText;
    }

    /**
     * DataURL转Blob
     * @param {string} dataURL - 数据URL
     */
    dataURLToBlob(dataURL) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new Blob([u8arr], { type: mime });
    }

    /**
     * 批量处理图片
     * @param {Array} files - 文件数组
     * @param {Object} options - 处理选项
     */
    async batchProcessImages(files, options = {}) {
        if (!Array.isArray(files)) {
            throw new Error('输入必须是文件数组');
        }

        console.log(`🖼️ [ImageProcessor] 开始批量处理 ${files.length} 个图片`);

        const results = [];
        const { parallel = false, maxConcurrent = 3 } = options;

        if (parallel) {
            // 并行处理（限制并发数）
            const chunks = this.chunkArray(files, maxConcurrent);
            
            for (const chunk of chunks) {
                const chunkResults = await Promise.allSettled(
                    chunk.map(file => this.processImage(file, options))
                );
                
                results.push(...chunkResults.map((result, index) => ({
                    file: chunk[index],
                    success: result.status === 'fulfilled',
                    result: result.status === 'fulfilled' ? result.value : null,
                    error: result.status === 'rejected' ? result.reason.message : null
                })));
            }
        } else {
            // 串行处理
            for (const file of files) {
                try {
                    const result = await this.processImage(file, options);
                    results.push({
                        file: file,
                        success: true,
                        result: result,
                        error: null
                    });
                } catch (error) {
                    results.push({
                        file: file,
                        success: false,
                        result: null,
                        error: error.message
                    });
                }
            }
        }

        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        const batchResult = {
            total: files.length,
            successful: successful.length,
            failed: failed.length,
            results: results,
            successRate: successful.length / files.length,
            timestamp: Date.now()
        };

        console.log(`✅ [ImageProcessor] 批量处理完成: ${successful.length}/${files.length} 成功`);

        return batchResult;
    }

    /**
     * 数组分块
     * @param {Array} array - 数组
     * @param {number} size - 块大小
     */
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    /**
     * 获取处理历史
     * @param {number} limit - 限制数量
     */
    getProcessingHistory(limit = 50) {
        return this.processingHistory.slice(-limit);
    }

    /**
     * 获取处理统计
     */
    getProcessingStats() {
        const history = this.processingHistory;
        
        return {
            totalProcessed: history.length,
            averageProcessingTime: history.reduce((sum, item) => sum + item.processingTime, 0) / history.length || 0,
            successRate: history.filter(item => item.success).length / history.length || 0,
            supportedFormats: this.config.supportedFormats,
            maxFileSize: this.config.maxFileSize,
            cacheSize: this.processedImages.size
        };
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ [ImageProcessor] 配置已更新', this.config);
    }

    /**
     * 更新处理队列
     * @param {string} processingId - 处理ID
     * @param {string} status - 状态
     */
    updateProcessingQueue(processingId, status) {
        const index = this.processingQueue.findIndex(item => item.id === processingId);
        if (index !== -1) {
            this.processingQueue[index].status = status;
            this.processingQueue[index].endTime = Date.now();
            
            // 如果完成，从队列中移除
            if (status === 'completed' || status === 'failed') {
                this.processingQueue.splice(index, 1);
            }
        }
    }

    /**
     * 记录处理历史
     * @param {Object} result - 处理结果
     */
    recordProcessingHistory(result) {
        this.processingHistory.push({
            processingId: result.processingId,
            fileName: result.originalFile.name,
            fileSize: result.originalFile.size,
            processingTime: result.processingTime,
            success: result.success,
            hasText: !!result.extractedText,
            timestamp: result.timestamp
        });

        // 限制历史记录大小
        if (this.processingHistory.length > 200) {
            this.processingHistory.shift();
        }
    }

    /**
     * 生成处理ID
     */
    generateProcessingId() {
        return 'img_proc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 处理图片处理请求事件
     * @param {Object} data - 请求数据
     */
    async handleImageProcessRequest(data) {
        try {
            const { file, options, callback } = data;
            const result = await this.processImage(file, options);
            
            if (callback && typeof callback === 'function') {
                callback(null, result);
            }
        } catch (error) {
            if (data.callback && typeof data.callback === 'function') {
                data.callback(error, null);
            }
        }
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.processedImages.clear();
        console.log('🧹 [ImageProcessor] 缓存已清除');
        
        if (this.eventBus) {
            this.eventBus.emit('image:cache-cleared', { timestamp: Date.now() });
        }
    }

    /**
     * 销毁图片处理器
     */
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理处理队列
        this.processingQueue = [];
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('image:process-request');
            this.eventBus.off('image:config-updated');
        }

        console.log('🗑️ [ImageProcessor] 图片处理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageProcessor;
} else {
    window.ImageProcessor = ImageProcessor;
}

console.log('✅ [ImageProcessor] 图片处理器模块已加载');
