/**
 * 文本解析器 - 智能文本解析和数据提取
 * 负责从各种文本格式中提取结构化数据
 * 创建日期: 2025-01-11
 */

class TextParser {
    constructor(eventBus = window.mdacEventBus, dateFormatter = null) {
        this.eventBus = eventBus;
        this.dateFormatter = dateFormatter || (window.DateFormatter ? new DateFormatter() : null);
        
        // 解析规则配置
        this.parseRules = {
            // 个人信息解析规则
            personalInfo: {
                name: [
                    /姓名[：:]\s*([^\n\r,，]+)/i,
                    /名字[：:]\s*([^\n\r,，]+)/i,
                    /Name[：:]\s*([^\n\r,，]+)/i,
                    /全名[：:]\s*([^\n\r,，]+)/i
                ],
                gender: [
                    /性别[：:]\s*(男|女|Male|Female|M|F)/i,
                    /Gender[：:]\s*(男|女|Male|Female|M|F)/i
                ],
                birthDate: [
                    /出生日期[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i,
                    /生日[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i,
                    /Birth Date[：:]\s*([0-9]{1,2}[\/\-\.]{1,2}[0-9]{1,2}[\/\-\.]{0,2}[0-9]{2,4})/i,
                    /DOB[：:]\s*([0-9]{1,2}[\/\-\.]{1,2}[0-9]{1,2}[\/\-\.]{0,2}[0-9]{2,4})/i
                ],
                nationality: [
                    /国籍[：:]\s*([^\n\r,，]+)/i,
                    /Nationality[：:]\s*([^\n\r,，]+)/i,
                    /国家[：:]\s*([^\n\r,，]+)/i
                ],
                passportNumber: [
                    /护照号[码]?[：:]\s*([A-Z0-9]+)/i,
                    /Passport\s*Number[：:]\s*([A-Z0-9]+)/i,
                    /Passport[：:]\s*([A-Z0-9]+)/i
                ]
            },
            
            // 联系信息解析规则
            contactInfo: {
                phone: [
                    /电话[号码]?[：:]\s*([+]?[0-9\-\s\(\)]+)/i,
                    /手机[号码]?[：:]\s*([+]?[0-9\-\s\(\)]+)/i,
                    /Phone[：:]\s*([+]?[0-9\-\s\(\)]+)/i,
                    /Mobile[：:]\s*([+]?[0-9\-\s\(\)]+)/i,
                    /Tel[：:]\s*([+]?[0-9\-\s\(\)]+)/i
                ],
                email: [
                    /邮箱[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                    /Email[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
                    /电子邮件[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i
                ],
                address: [
                    /地址[：:]\s*([^\n\r]+)/i,
                    /Address[：:]\s*([^\n\r]+)/i,
                    /住址[：:]\s*([^\n\r]+)/i,
                    /居住地[：:]\s*([^\n\r]+)/i
                ]
            },
            
            // 旅行信息解析规则
            travelInfo: {
                destination: [
                    /目的地[：:]\s*([^\n\r,，]+)/i,
                    /Destination[：:]\s*([^\n\r,，]+)/i,
                    /前往[：:]\s*([^\n\r,，]+)/i,
                    /到达[：:]\s*([^\n\r,，]+)/i
                ],
                entryDate: [
                    /入境日期[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i,
                    /Entry Date[：:]\s*([0-9]{1,2}[\/\-\.]{1,2}[0-9]{1,2}[\/\-\.]{0,2}[0-9]{2,4})/i,
                    /到达日期[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i
                ],
                exitDate: [
                    /离境日期[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i,
                    /Exit Date[：:]\s*([0-9]{1,2}[\/\-\.]{1,2}[0-9]{1,2}[\/\-\.]{0,2}[0-9]{2,4})/i,
                    /离开日期[：:]\s*([0-9]{1,2}[\/\-\.年月日]{1,2}[0-9]{1,2}[\/\-\.年月日]{0,2}[0-9]{2,4})/i
                ],
                accommodation: [
                    /住宿地址[：:]\s*([^\n\r]+)/i,
                    /酒店地址[：:]\s*([^\n\r]+)/i,
                    /Accommodation[：:]\s*([^\n\r]+)/i,
                    /Hotel[：:]\s*([^\n\r]+)/i
                ]
            },
            
            // 交通信息解析规则
            transportInfo: {
                flightNumber: [
                    /航班号[：:]\s*([A-Z]{2}[0-9]+)/i,
                    /Flight[：:]\s*([A-Z]{2}[0-9]+)/i,
                    /班机[：:]\s*([A-Z]{2}[0-9]+)/i
                ],
                vehicleNumber: [
                    /车牌号[：:]\s*([A-Z0-9]+)/i,
                    /Vehicle[：:]\s*([A-Z0-9]+)/i,
                    /车号[：:]\s*([A-Z0-9]+)/i,
                    /License Plate[：:]\s*([A-Z0-9]+)/i
                ]
            }
        };

        // 数据清理规则
        this.cleaningRules = {
            phone: (value) => value.replace(/[^\d+\-\(\)\s]/g, '').trim(),
            email: (value) => value.toLowerCase().trim(),
            name: (value) => value.replace(/[^\u4e00-\u9fa5a-zA-Z\s]/g, '').trim(),
            passportNumber: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, ''),
            flightNumber: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, ''),
            vehicleNumber: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, '')
        };

        console.log('📝 [TextParser] 文本解析器已初始化');
    }

    /**
     * 解析文本内容
     * @param {string} text - 输入文本
     * @param {Object} options - 解析选项
     */
    parseText(text, options = {}) {
        try {
            console.log('📝 [TextParser] 开始解析文本', { 
                textLength: text.length,
                options 
            });

            if (!text || typeof text !== 'string') {
                throw new Error('无效的文本输入');
            }

            // 预处理文本
            const preprocessedText = this.preprocessText(text);
            
            // 执行解析
            const extractedData = this.extractData(preprocessedText, options);
            
            // 后处理数据
            const cleanedData = this.postprocessData(extractedData);
            
            // 验证数据
            const validationResult = this.validateExtractedData(cleanedData);
            
            const result = {
                success: true,
                data: cleanedData,
                validation: validationResult,
                originalText: text,
                preprocessedText: preprocessedText,
                timestamp: Date.now()
            };

            console.log('✅ [TextParser] 文本解析完成', result);

            // 发布解析完成事件
            if (this.eventBus) {
                this.eventBus.emit('textParser:parse-complete', result);
            }

            return result;

        } catch (error) {
            console.error('❌ [TextParser] 文本解析失败', error);
            
            const errorResult = {
                success: false,
                error: error.message,
                originalText: text,
                timestamp: Date.now()
            };

            // 发布解析错误事件
            if (this.eventBus) {
                this.eventBus.emit('textParser:parse-error', errorResult);
            }

            return errorResult;
        }
    }

    /**
     * 预处理文本
     * @param {string} text - 原始文本
     */
    preprocessText(text) {
        // 统一换行符
        let processed = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        
        // 移除多余的空白字符
        processed = processed.replace(/\s+/g, ' ');
        
        // 统一冒号格式
        processed = processed.replace(/：/g, ':');
        
        // 移除特殊字符但保留基本标点
        processed = processed.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s:：,，.。\-\/\(\)@+]/g, ' ');
        
        // 清理多余空格
        processed = processed.replace(/\s+/g, ' ').trim();
        
        return processed;
    }

    /**
     * 提取数据
     * @param {string} text - 预处理后的文本
     * @param {Object} options - 解析选项
     */
    extractData(text, options = {}) {
        const extractedData = {
            personalInfo: {},
            contactInfo: {},
            travelInfo: {},
            transportInfo: {}
        };

        // 遍历所有解析规则
        Object.entries(this.parseRules).forEach(([category, fields]) => {
            Object.entries(fields).forEach(([fieldName, patterns]) => {
                const value = this.extractField(text, patterns, fieldName);
                if (value) {
                    extractedData[category][fieldName] = value;
                }
            });
        });

        // 特殊处理：自动检测交通方式
        if (extractedData.transportInfo.flightNumber) {
            extractedData.transportInfo.modeOfTravel = 'AIR';
        } else if (extractedData.transportInfo.vehicleNumber) {
            extractedData.transportInfo.modeOfTravel = 'LAND';
        }

        return extractedData;
    }

    /**
     * 提取单个字段
     * @param {string} text - 文本
     * @param {Array} patterns - 正则表达式模式数组
     * @param {string} fieldName - 字段名称
     */
    extractField(text, patterns, fieldName) {
        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
                let value = match[1].trim();
                
                // 应用清理规则
                if (this.cleaningRules[fieldName]) {
                    value = this.cleaningRules[fieldName](value);
                }
                
                // 特殊处理日期字段
                if (fieldName.includes('Date') || fieldName === 'birthDate') {
                    value = this.formatDate(value);
                }
                
                return value;
            }
        }
        return null;
    }

    /**
     * 格式化日期
     * @param {string} dateStr - 日期字符串
     */
    formatDate(dateStr) {
        if (!this.dateFormatter) {
            return dateStr; // 如果没有日期格式化器，返回原始值
        }

        try {
            const result = this.dateFormatter.formatForMDAC(dateStr);
            return result.isValid ? result.formatted : dateStr;
        } catch (error) {
            console.warn('⚠️ [TextParser] 日期格式化失败', error);
            return dateStr;
        }
    }

    /**
     * 后处理数据
     * @param {Object} data - 提取的数据
     */
    postprocessData(data) {
        const processed = JSON.parse(JSON.stringify(data)); // 深拷贝

        // 处理性别标准化
        if (processed.personalInfo.gender) {
            const gender = processed.personalInfo.gender.toLowerCase();
            if (gender.includes('男') || gender.includes('male') || gender === 'm') {
                processed.personalInfo.gender = '男';
            } else if (gender.includes('女') || gender.includes('female') || gender === 'f') {
                processed.personalInfo.gender = '女';
            }
        }

        // 处理国籍标准化
        if (processed.personalInfo.nationality) {
            const nationality = processed.personalInfo.nationality.toLowerCase();
            const nationalityMap = {
                'china': '中国',
                'chinese': '中国',
                '中华人民共和国': '中国',
                'malaysia': '马来西亚',
                'malaysian': '马来西亚',
                'singapore': '新加坡',
                'singaporean': '新加坡'
            };
            
            processed.personalInfo.nationality = nationalityMap[nationality] || processed.personalInfo.nationality;
        }

        // 处理电话号码格式
        if (processed.contactInfo.phone) {
            let phone = processed.contactInfo.phone;
            // 移除括号和多余空格
            phone = phone.replace(/[\(\)\s]/g, '');
            // 确保国际格式
            if (phone.startsWith('0')) {
                phone = '+60' + phone.substring(1); // 马来西亚号码
            } else if (!phone.startsWith('+')) {
                phone = '+' + phone;
            }
            processed.contactInfo.phone = phone;
        }

        return processed;
    }

    /**
     * 验证提取的数据
     * @param {Object} data - 提取的数据
     */
    validateExtractedData(data) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            fieldValidation: {}
        };

        // 验证个人信息
        if (data.personalInfo) {
            // 验证护照号码格式
            if (data.personalInfo.passportNumber) {
                const passportPattern = /^[A-Z][0-9]{8}$/; // 简化的护照号码格式
                if (!passportPattern.test(data.personalInfo.passportNumber)) {
                    validation.warnings.push('护照号码格式可能不正确');
                    validation.fieldValidation.passportNumber = 'warning';
                }
            }

            // 验证出生日期
            if (data.personalInfo.birthDate) {
                const birthYear = parseInt(data.personalInfo.birthDate.split('/')[2]);
                const currentYear = new Date().getFullYear();
                if (birthYear > currentYear || birthYear < 1900) {
                    validation.errors.push('出生日期不合理');
                    validation.fieldValidation.birthDate = 'error';
                    validation.isValid = false;
                }
            }
        }

        // 验证联系信息
        if (data.contactInfo) {
            // 验证邮箱格式
            if (data.contactInfo.email) {
                const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!emailPattern.test(data.contactInfo.email)) {
                    validation.errors.push('邮箱格式不正确');
                    validation.fieldValidation.email = 'error';
                    validation.isValid = false;
                }
            }

            // 验证电话号码
            if (data.contactInfo.phone) {
                const phonePattern = /^\+?[0-9\-\s\(\)]{8,15}$/;
                if (!phonePattern.test(data.contactInfo.phone)) {
                    validation.warnings.push('电话号码格式可能不正确');
                    validation.fieldValidation.phone = 'warning';
                }
            }
        }

        // 验证旅行信息
        if (data.travelInfo) {
            // 验证日期逻辑
            if (data.travelInfo.entryDate && data.travelInfo.exitDate) {
                const entryDate = new Date(data.travelInfo.entryDate.split('/').reverse().join('-'));
                const exitDate = new Date(data.travelInfo.exitDate.split('/').reverse().join('-'));
                
                if (exitDate <= entryDate) {
                    validation.errors.push('离境日期不能早于或等于入境日期');
                    validation.fieldValidation.exitDate = 'error';
                    validation.isValid = false;
                }
            }
        }

        // 验证交通信息
        if (data.transportInfo) {
            // 验证航班号格式
            if (data.transportInfo.flightNumber) {
                const flightPattern = /^[A-Z]{2}[0-9]{1,4}$/;
                if (!flightPattern.test(data.transportInfo.flightNumber)) {
                    validation.warnings.push('航班号格式可能不正确');
                    validation.fieldValidation.flightNumber = 'warning';
                }
            }

            // 验证车牌号格式（马来西亚）
            if (data.transportInfo.vehicleNumber) {
                const malaysianPlatePatterns = [
                    /^[A-Z]{1,3}[0-9]{1,4}[A-Z]?$/, // 一般格式
                    /^[A-Z]{2}[0-9]{4}[A-Z]$/, // 新格式
                ];
                
                const isValidPlate = malaysianPlatePatterns.some(pattern => 
                    pattern.test(data.transportInfo.vehicleNumber)
                );
                
                if (!isValidPlate) {
                    validation.warnings.push('车牌号格式可能不正确');
                    validation.fieldValidation.vehicleNumber = 'warning';
                }
            }
        }

        return validation;
    }

    /**
     * 批量解析多个文本
     * @param {Array} texts - 文本数组
     * @param {Object} options - 解析选项
     */
    batchParseTexts(texts, options = {}) {
        if (!Array.isArray(texts)) {
            throw new Error('输入必须是文本数组');
        }

        console.log(`📝 [TextParser] 开始批量解析 ${texts.length} 个文本`);

        const results = texts.map((text, index) => {
            try {
                const result = this.parseText(text, options);
                return {
                    index,
                    success: true,
                    result
                };
            } catch (error) {
                return {
                    index,
                    success: false,
                    error: error.message,
                    text: text.substring(0, 100) + '...'
                };
            }
        });

        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        const batchResult = {
            total: texts.length,
            successful: successful.length,
            failed: failed.length,
            results: results,
            successRate: successful.length / texts.length,
            timestamp: Date.now()
        };

        console.log(`✅ [TextParser] 批量解析完成: ${successful.length}/${texts.length} 成功`);

        return batchResult;
    }

    /**
     * 合并多个解析结果
     * @param {Array} parseResults - 解析结果数组
     */
    mergeParseResults(parseResults) {
        if (!Array.isArray(parseResults) || parseResults.length === 0) {
            return null;
        }

        const merged = {
            personalInfo: {},
            contactInfo: {},
            travelInfo: {},
            transportInfo: {}
        };

        // 合并所有结果，后面的结果覆盖前面的
        parseResults.forEach(result => {
            if (result.success && result.data) {
                Object.keys(merged).forEach(category => {
                    if (result.data[category]) {
                        merged[category] = { ...merged[category], ...result.data[category] };
                    }
                });
            }
        });

        return {
            success: true,
            data: merged,
            sourceCount: parseResults.length,
            timestamp: Date.now()
        };
    }

    /**
     * 获取解析统计信息
     */
    getParseStats() {
        return {
            supportedFields: Object.keys(this.parseRules).reduce((total, category) => {
                return total + Object.keys(this.parseRules[category]).length;
            }, 0),
            categories: Object.keys(this.parseRules),
            cleaningRules: Object.keys(this.cleaningRules),
            hasDateFormatter: !!this.dateFormatter
        };
    }

    /**
     * 添加自定义解析规则
     * @param {string} category - 类别
     * @param {string} fieldName - 字段名
     * @param {Array} patterns - 正则表达式模式
     */
    addParseRule(category, fieldName, patterns) {
        if (!this.parseRules[category]) {
            this.parseRules[category] = {};
        }
        
        this.parseRules[category][fieldName] = patterns;
        
        console.log(`📝 [TextParser] 添加解析规则: ${category}.${fieldName}`);
    }

    /**
     * 添加自定义清理规则
     * @param {string} fieldName - 字段名
     * @param {Function} cleaningFunction - 清理函数
     */
    addCleaningRule(fieldName, cleaningFunction) {
        this.cleaningRules[fieldName] = cleaningFunction;
        
        console.log(`🧹 [TextParser] 添加清理规则: ${fieldName}`);
    }

    /**
     * 销毁文本解析器
     */
    destroy() {
        // 清理事件监听器
        if (this.eventBus) {
            // 移除相关事件监听器
        }

        console.log('🗑️ [TextParser] 文本解析器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TextParser;
} else {
    window.TextParser = TextParser;
}

console.log('✅ [TextParser] 文本解析器模块已加载');
