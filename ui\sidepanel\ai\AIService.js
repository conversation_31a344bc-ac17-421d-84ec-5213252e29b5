/**
 * AI服务 - Gemini API集成和AI功能管理
 * 负责与Gemini AI的通信、配置管理和错误处理
 * 创建日期: 2025-01-11
 */

class AIService {
    constructor(eventBus = window.mdacEventBus, stateManager = null) {
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        
        // AI配置
        this.config = {
            apiKey: null,
            model: 'gemini-pro',
            maxRetries: 3,
            timeout: 30000,
            rateLimit: {
                requestsPerMinute: 60,
                requestsPerHour: 1000
            }
        };

        // 请求状态管理
        this.requestQueue = [];
        this.activeRequests = new Map();
        this.requestHistory = [];
        this.rateLimitTracker = {
            minuteRequests: [],
            hourRequests: []
        };

        // 缓存系统
        this.responseCache = new Map();
        this.cacheConfig = {
            enabled: true,
            maxSize: 100,
            ttl: 300000 // 5分钟
        };

        console.log('🤖 [AIService] AI服务已初始化');
        this.initializeService();
    }

    /**
     * 初始化AI服务
     */
    async initializeService() {
        try {
            // 加载配置
            await this.loadConfiguration();
            
            // 初始化事件监听器
            this.initializeEventListeners();
            
            // 验证API连接
            await this.validateConnection();
            
            console.log('✅ [AIService] AI服务初始化完成');
            
            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('ai:service-initialized', {
                    timestamp: Date.now(),
                    config: this.config
                });
            }
            
        } catch (error) {
            console.error('❌ [AIService] AI服务初始化失败', error);
            
            if (this.eventBus) {
                this.eventBus.emit('ai:service-initialization-failed', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        }
    }

    /**
     * 加载配置
     */
    async loadConfiguration() {
        try {
            // 从存储加载配置
            const result = await chrome.storage.sync.get(['mdacAIConfig']);
            if (result.mdacAIConfig) {
                this.config = { ...this.config, ...result.mdacAIConfig };
            }

            // 从环境变量或配置文件加载API密钥
            if (!this.config.apiKey) {
                // 尝试从background script获取API密钥
                const response = await chrome.runtime.sendMessage({
                    action: 'getAIConfig'
                });
                
                if (response && response.success && response.config) {
                    this.config = { ...this.config, ...response.config };
                }
            }

            console.log('📋 [AIService] 配置已加载', {
                model: this.config.model,
                hasApiKey: !!this.config.apiKey,
                rateLimit: this.config.rateLimit
            });

        } catch (error) {
            console.error('❌ [AIService] 配置加载失败', error);
            throw new Error(`AI配置加载失败: ${error.message}`);
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听AI请求事件
        this.eventBus.on('ai:request', (data) => {
            this.handleAIRequest(data);
        });

        // 监听配置更新事件
        this.eventBus.on('ai:config-updated', (config) => {
            this.updateConfiguration(config);
        });

        // 监听缓存清理事件
        this.eventBus.on('ai:clear-cache', () => {
            this.clearCache();
        });
    }

    /**
     * 验证API连接
     */
    async validateConnection() {
        if (!this.config.apiKey) {
            throw new Error('API密钥未配置');
        }

        try {
            // 发送测试请求
            const testPrompt = "测试连接";
            const response = await this.makeAPIRequest(testPrompt, {
                skipCache: true,
                timeout: 10000
            });

            if (response && response.success) {
                console.log('✅ [AIService] API连接验证成功');
                return true;
            } else {
                throw new Error('API响应无效');
            }

        } catch (error) {
            console.error('❌ [AIService] API连接验证失败', error);
            throw new Error(`API连接失败: ${error.message}`);
        }
    }

    /**
     * 解析内容 - 主要的AI处理方法
     * @param {string} text - 文本内容
     * @param {File} image - 图片文件
     * @param {Object} options - 解析选项
     */
    async parseContent(text = '', image = null, options = {}) {
        try {
            console.log('🤖 [AIService] 开始解析内容', { 
                hasText: !!text, 
                hasImage: !!image,
                options 
            });

            // 发布处理开始事件
            if (this.eventBus) {
                this.eventBus.emit('ai:processing-start', {
                    text: text.substring(0, 100) + '...',
                    hasImage: !!image,
                    timestamp: Date.now()
                });
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('ai.isProcessing', true);
            }

            // 构建提示词
            const prompt = this.buildParsePrompt(text, image, options);
            
            // 发送AI请求
            const response = await this.makeAPIRequest(prompt, {
                includeImage: !!image,
                image: image,
                ...options
            });

            if (!response || !response.success) {
                throw new Error(response?.error || 'AI解析失败');
            }

            // 解析AI响应
            const parsedData = this.parseAIResponse(response.data);
            
            // 评估置信度
            const confidence = this.evaluateConfidence(parsedData, text, image);

            const result = {
                data: parsedData,
                confidence: confidence,
                rawResponse: response.data,
                timestamp: Date.now(),
                processingTime: response.processingTime
            };

            console.log('✅ [AIService] 内容解析完成', result);

            // 发布处理完成事件
            if (this.eventBus) {
                this.eventBus.emit('ai:processing-complete', {
                    result,
                    timestamp: Date.now()
                });
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'ai.isProcessing': false,
                    'ai.lastResponse': result,
                    'data.parsedData': parsedData,
                    'data.confidence': confidence
                });
            }

            return result;

        } catch (error) {
            console.error('❌ [AIService] 内容解析失败', error);

            // 发布处理错误事件
            if (this.eventBus) {
                this.eventBus.emit('ai:processing-error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.batchUpdate({
                    'ai.isProcessing': false,
                    'errors.lastError': error.message
                });
            }

            throw error;
        }
    }

    /**
     * 构建解析提示词
     * @param {string} text - 文本内容
     * @param {File} image - 图片文件
     * @param {Object} options - 选项
     */
    buildParsePrompt(text, image, options = {}) {
        const { 
            context = 'MDAC表单填充',
            language = 'zh-CN',
            extractionType = 'personal_info'
        } = options;

        let prompt = `请从以下内容中提取${context}所需的信息，并以JSON格式返回：\n\n`;

        if (text) {
            prompt += `文本内容：\n${text}\n\n`;
        }

        if (image) {
            prompt += `图片内容：请分析图片中的文本和信息\n\n`;
        }

        prompt += `请提取以下字段（如果存在）：
- 个人信息：姓名、性别、出生日期、国籍、护照号码
- 联系信息：电话、邮箱、地址
- 旅行信息：目的地、入境日期、离境日期、住宿地址
- 交通信息：航班号、车牌号、交通方式

返回格式：
{
  "personalInfo": {
    "name": "姓名",
    "gender": "性别",
    "birthDate": "出生日期",
    "nationality": "国籍",
    "passportNumber": "护照号码"
  },
  "contactInfo": {
    "phone": "电话",
    "email": "邮箱",
    "address": "地址"
  },
  "travelInfo": {
    "destination": "目的地",
    "entryDate": "入境日期",
    "exitDate": "离境日期",
    "accommodation": "住宿地址"
  },
  "transportInfo": {
    "flightNumber": "航班号",
    "vehicleNumber": "车牌号",
    "modeOfTravel": "交通方式"
  }
}

注意：
1. 只提取确实存在的信息，不要编造
2. 日期格式使用 DD/MM/YYYY
3. 如果信息不确定，请在字段名后加上 "_uncertain"
4. 保持原始语言，不要翻译专有名词`;

        return prompt;
    }

    /**
     * 发送API请求
     * @param {string} prompt - 提示词
     * @param {Object} options - 请求选项
     */
    async makeAPIRequest(prompt, options = {}) {
        const {
            includeImage = false,
            image = null,
            timeout = this.config.timeout,
            skipCache = false,
            retries = this.config.maxRetries
        } = options;

        // 检查速率限制
        if (!this.checkRateLimit()) {
            throw new Error('API请求频率超限，请稍后再试');
        }

        // 检查缓存
        if (!skipCache && this.cacheConfig.enabled) {
            const cacheKey = this.generateCacheKey(prompt, includeImage);
            const cachedResponse = this.getFromCache(cacheKey);
            if (cachedResponse) {
                console.log('📋 [AIService] 使用缓存响应');
                return cachedResponse;
            }
        }

        const requestId = this.generateRequestId();
        let lastError;

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                console.log(`🔄 [AIService] 发送API请求 (尝试 ${attempt}/${retries})`);

                const startTime = Date.now();
                
                // 记录请求
                this.recordRequest(requestId, prompt);

                // 发送请求到background script
                const response = await chrome.runtime.sendMessage({
                    action: 'callGeminiAI',
                    prompt: prompt,
                    context: 'MDAC表单填充',
                    includeImage: includeImage,
                    image: image,
                    requestId: requestId,
                    timeout: timeout
                });

                const processingTime = Date.now() - startTime;

                if (response && response.success) {
                    console.log(`✅ [AIService] API请求成功 (${processingTime}ms)`);
                    
                    const result = {
                        success: true,
                        data: response.data,
                        processingTime: processingTime,
                        requestId: requestId,
                        attempt: attempt
                    };

                    // 缓存响应
                    if (this.cacheConfig.enabled && !skipCache) {
                        const cacheKey = this.generateCacheKey(prompt, includeImage);
                        this.saveToCache(cacheKey, result);
                    }

                    // 更新请求统计
                    this.updateRequestStats(requestId, true, processingTime);

                    return result;
                } else {
                    throw new Error(response?.error || 'API请求失败');
                }

            } catch (error) {
                lastError = error;
                console.warn(`⚠️ [AIService] API请求失败 (尝试 ${attempt}/${retries}):`, error.message);

                // 更新请求统计
                this.updateRequestStats(requestId, false, 0, error.message);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < retries) {
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // 指数退避
                    console.log(`⏳ [AIService] ${delay}ms后重试...`);
                    await this.delay(delay);
                }
            }
        }

        throw new Error(`API请求失败，已重试${retries}次: ${lastError.message}`);
    }

    /**
     * 解析AI响应
     * @param {string} responseText - AI响应文本
     */
    parseAIResponse(responseText) {
        try {
            // 尝试直接解析JSON
            if (responseText.trim().startsWith('{')) {
                return JSON.parse(responseText);
            }

            // 提取JSON部分
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                return JSON.parse(jsonMatch[0]);
            }

            // 如果无法解析为JSON，返回原始文本
            console.warn('⚠️ [AIService] 无法解析为JSON，返回原始响应');
            return {
                rawText: responseText,
                parseError: true
            };

        } catch (error) {
            console.error('❌ [AIService] 响应解析失败', error);
            return {
                rawText: responseText,
                parseError: true,
                error: error.message
            };
        }
    }

    /**
     * 评估置信度
     * @param {Object} parsedData - 解析的数据
     * @param {string} originalText - 原始文本
     * @param {File} originalImage - 原始图片
     */
    evaluateConfidence(parsedData, originalText, originalImage) {
        const confidence = {
            overall: 0,
            fields: {},
            factors: []
        };

        if (!parsedData || parsedData.parseError) {
            confidence.overall = 0;
            confidence.factors.push('解析失败');
            return confidence;
        }

        let totalFields = 0;
        let filledFields = 0;
        let uncertainFields = 0;

        // 遍历所有字段评估置信度
        const evaluateSection = (section, sectionName) => {
            if (!section || typeof section !== 'object') return;

            Object.entries(section).forEach(([key, value]) => {
                totalFields++;
                
                if (value && value !== '') {
                    filledFields++;
                    
                    // 检查不确定标记
                    if (key.endsWith('_uncertain')) {
                        uncertainFields++;
                        confidence.fields[key] = 0.5;
                    } else {
                        confidence.fields[key] = 0.8;
                    }
                } else {
                    confidence.fields[key] = 0;
                }
            });
        };

        // 评估各个部分
        evaluateSection(parsedData.personalInfo, 'personal');
        evaluateSection(parsedData.contactInfo, 'contact');
        evaluateSection(parsedData.travelInfo, 'travel');
        evaluateSection(parsedData.transportInfo, 'transport');

        // 计算总体置信度
        if (totalFields > 0) {
            const fillRate = filledFields / totalFields;
            const uncertaintyPenalty = uncertainFields / totalFields * 0.3;
            confidence.overall = Math.max(0, fillRate - uncertaintyPenalty);
        }

        // 添加影响因素
        if (filledFields === 0) {
            confidence.factors.push('未提取到任何信息');
        } else if (fillRate < 0.3) {
            confidence.factors.push('提取信息较少');
        } else if (fillRate > 0.7) {
            confidence.factors.push('提取信息丰富');
        }

        if (uncertainFields > 0) {
            confidence.factors.push(`${uncertainFields}个字段不确定`);
        }

        if (originalImage) {
            confidence.factors.push('包含图片信息');
            confidence.overall += 0.1; // 图片信息加分
        }

        // 限制置信度范围
        confidence.overall = Math.min(1, Math.max(0, confidence.overall));

        return confidence;
    }

    /**
     * 检查速率限制
     */
    checkRateLimit() {
        const now = Date.now();
        
        // 清理过期的请求记录
        this.rateLimitTracker.minuteRequests = this.rateLimitTracker.minuteRequests
            .filter(time => now - time < 60000);
        this.rateLimitTracker.hourRequests = this.rateLimitTracker.hourRequests
            .filter(time => now - time < 3600000);

        // 检查限制
        if (this.rateLimitTracker.minuteRequests.length >= this.config.rateLimit.requestsPerMinute) {
            return false;
        }
        
        if (this.rateLimitTracker.hourRequests.length >= this.config.rateLimit.requestsPerHour) {
            return false;
        }

        return true;
    }

    /**
     * 记录请求
     * @param {string} requestId - 请求ID
     * @param {string} prompt - 提示词
     */
    recordRequest(requestId, prompt) {
        const now = Date.now();
        
        // 记录到速率限制追踪器
        this.rateLimitTracker.minuteRequests.push(now);
        this.rateLimitTracker.hourRequests.push(now);

        // 记录到活跃请求
        this.activeRequests.set(requestId, {
            startTime: now,
            prompt: prompt.substring(0, 100) + '...'
        });

        // 更新状态
        if (this.stateManager) {
            this.stateManager.set('ai.apiUsage.requestCount', 
                this.stateManager.get('ai.apiUsage.requestCount') + 1);
            this.stateManager.set('ai.apiUsage.lastRequestTime', now);
        }
    }

    /**
     * 更新请求统计
     * @param {string} requestId - 请求ID
     * @param {boolean} success - 是否成功
     * @param {number} processingTime - 处理时间
     * @param {string} error - 错误信息
     */
    updateRequestStats(requestId, success, processingTime, error = null) {
        const request = this.activeRequests.get(requestId);
        if (!request) return;

        const requestRecord = {
            ...request,
            requestId,
            success,
            processingTime,
            error,
            endTime: Date.now()
        };

        // 移除活跃请求
        this.activeRequests.delete(requestId);

        // 添加到历史记录
        this.requestHistory.push(requestRecord);

        // 限制历史记录大小
        if (this.requestHistory.length > 100) {
            this.requestHistory.shift();
        }
    }

    /**
     * 生成缓存键
     * @param {string} prompt - 提示词
     * @param {boolean} includeImage - 是否包含图片
     */
    generateCacheKey(prompt, includeImage) {
        const content = prompt + (includeImage ? '_with_image' : '');
        return 'ai_cache_' + this.hashString(content);
    }

    /**
     * 字符串哈希
     * @param {string} str - 字符串
     */
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    /**
     * 从缓存获取
     * @param {string} key - 缓存键
     */
    getFromCache(key) {
        const cached = this.responseCache.get(key);
        if (!cached) return null;

        // 检查TTL
        if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
            this.responseCache.delete(key);
            return null;
        }

        return cached.data;
    }

    /**
     * 保存到缓存
     * @param {string} key - 缓存键
     * @param {*} data - 数据
     */
    saveToCache(key, data) {
        // 检查缓存大小限制
        if (this.responseCache.size >= this.cacheConfig.maxSize) {
            // 删除最旧的缓存项
            const firstKey = this.responseCache.keys().next().value;
            this.responseCache.delete(firstKey);
        }

        this.responseCache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.responseCache.clear();
        console.log('🧹 [AIService] 缓存已清除');
        
        if (this.eventBus) {
            this.eventBus.emit('ai:cache-cleared', { timestamp: Date.now() });
        }
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfiguration(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // 保存到存储
        chrome.storage.sync.set({ 'mdacAIConfig': this.config });
        
        console.log('⚙️ [AIService] 配置已更新', this.config);
    }

    /**
     * 获取服务状态
     */
    getStatus() {
        return {
            isInitialized: !!this.config.apiKey,
            activeRequests: this.activeRequests.size,
            cacheSize: this.responseCache.size,
            requestHistory: this.requestHistory.length,
            rateLimit: {
                minuteRequests: this.rateLimitTracker.minuteRequests.length,
                hourRequests: this.rateLimitTracker.hourRequests.length,
                limits: this.config.rateLimit
            },
            config: {
                model: this.config.model,
                hasApiKey: !!this.config.apiKey,
                cacheEnabled: this.cacheConfig.enabled
            }
        };
    }

    /**
     * 生成请求ID
     */
    generateRequestId() {
        return 'ai_req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 处理AI请求事件
     * @param {Object} data - 请求数据
     */
    async handleAIRequest(data) {
        try {
            const { text, image, options, callback } = data;
            const result = await this.parseContent(text, image, options);
            
            if (callback && typeof callback === 'function') {
                callback(null, result);
            }
        } catch (error) {
            if (data.callback && typeof data.callback === 'function') {
                data.callback(error, null);
            }
        }
    }

    /**
     * 销毁AI服务
     */
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理活跃请求
        this.activeRequests.clear();
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('ai:request');
            this.eventBus.off('ai:config-updated');
            this.eventBus.off('ai:clear-cache');
        }

        console.log('🗑️ [AIService] AI服务已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIService;
} else {
    window.AIService = AIService;
}

console.log('✅ [AIService] AI服务模块已加载');
