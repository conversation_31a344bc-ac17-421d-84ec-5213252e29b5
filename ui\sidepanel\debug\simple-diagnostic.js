/**
 * 简单诊断工具 - 替代复杂的system-validator
 * 只检查核心功能是否可用
 */

(function() {
    'use strict';

    console.log('🔍 [SimpleDiagnostic] 开始简单诊断...');

    // 等待一秒后进行诊断
    setTimeout(() => {
        const results = [];

        // 检查EventBus
        if (typeof window.EventBus === 'function' && typeof window.mdacEventBus === 'object') {
            results.push('✅ EventBus: 正常');
        } else {
            results.push('❌ EventBus: 缺失');
        }

        // 检查存根类
        const stubClasses = ['DebugLogger', 'StateManager', 'EventManager', 'SidePanelCore'];
        stubClasses.forEach(className => {
            if (typeof window[className] === 'function') {
                results.push(`✅ ${className}: 正常`);
            } else {
                results.push(`❌ ${className}: 缺失`);
            }
        });

        // 检查侧边栏实例
        if (window.mdacModularSidePanel) {
            results.push('✅ SidePanel实例: 正常');
        } else {
            results.push('❌ SidePanel实例: 缺失');
        }

        // 输出诊断结果
        console.log('📊 [SimpleDiagnostic] 诊断结果:');
        results.forEach(result => console.log(result));

        // 检查是否有任何失败
        const failures = results.filter(r => r.startsWith('❌'));
        if (failures.length === 0) {
            console.log('🎉 [SimpleDiagnostic] 所有核心功能正常！');
        } else {
            console.warn(`⚠️ [SimpleDiagnostic] 发现 ${failures.length} 个问题`);
        }

    }, 1000);

})();
