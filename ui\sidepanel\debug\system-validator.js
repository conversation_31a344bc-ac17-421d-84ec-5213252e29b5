/**
 * 系统修复验证脚本
 * 验证模块加载问题是否已解决
 */

console.log('🔧 [SystemValidator] 开始系统修复验证...');

// 延迟执行，等待模块加载完成
setTimeout(() => {
    const results = {
        timestamp: new Date().toISOString(),
        tests: [],
        passed: 0,
        failed: 0,
        overall: 'UNKNOWN'
    };

    function addTest(name, condition, message) {
        const passed = !!condition;
        results.tests.push({
            name,
            passed,
            message: message || (passed ? '✅ 通过' : '❌ 失败')
        });
        
        if (passed) {
            results.passed++;
            console.log(`✅ [Test] ${name}: ${message || '通过'}`);
        } else {
            results.failed++;
            console.error(`❌ [Test] ${name}: ${message || '失败'}`);
        }
    }

    // 测试核心模块
    addTest('EventBus类存在', typeof window.EventBus === 'function');
    addTest('EventBus实例存在', typeof window.mdacEventBus === 'object' && window.mdacEventBus !== null);
    addTest('DebugLogger类存在', typeof window.DebugLogger === 'function');
    addTest('StateManager类存在', typeof window.StateManager === 'function');
    addTest('EventManager类存在', typeof window.EventManager === 'function');
    addTest('SidePanelCore类存在', typeof window.SidePanelCore === 'function');

    // 测试实例
    addTest('StateManager实例存在', typeof window.mdacStateManager === 'object' && window.mdacStateManager !== null);
    addTest('SidePanelCore实例存在', typeof window.mdacSidePanelCore === 'object' && window.mdacSidePanelCore !== null);

    // 测试功能
    if (window.mdacEventBus) {
        try {
            window.mdacEventBus.emit('test:validation', { test: true });
            addTest('EventBus功能正常', true, 'emit方法执行成功');
        } catch (error) {
            addTest('EventBus功能正常', false, `emit方法失败: ${error.message}`);
        }
    }

    // 检查页面元素
    const hasUI = document.querySelector('.sidepanel-container') !== null;
    addTest('UI容器存在', hasUI);

    // 计算总体结果
    const total = results.passed + results.failed;
    const successRate = total > 0 ? (results.passed / total * 100).toFixed(1) : 0;
    
    if (results.failed === 0) {
        results.overall = 'PASSED';
    } else if (results.passed >= results.failed) {
        results.overall = 'PARTIAL';
    } else {
        results.overall = 'FAILED';
    }

    // 输出结果
    console.log(`📊 [SystemValidator] 验证完成 - ${results.overall}`);
    console.log(`📊 成功率: ${successRate}% (${results.passed}/${total})`);
    
    if (results.overall === 'PASSED') {
        console.log('🎉 [SystemValidator] 系统修复成功，所有测试通过！');
        showNotification('✅ 系统修复成功！', 'success');
    } else if (results.overall === 'PARTIAL') {
        console.warn('⚠️ [SystemValidator] 系统部分修复，存在一些问题');
        showNotification('⚠️ 系统部分修复', 'warning');
    } else {
        console.error('❌ [SystemValidator] 系统修复失败，需要进一步排查');
        showNotification('❌ 系统修复失败', 'error');
    }

    // 存储验证结果
    window.mdacValidationResults = results;

    // 生成详细报告
    console.group('📋 详细测试报告');
    results.tests.forEach(test => {
        if (test.passed) {
            console.log(`✅ ${test.name}: ${test.message}`);
        } else {
            console.error(`❌ ${test.name}: ${test.message}`);
        }
    });
    console.groupEnd();

}, 2000); // 等待2秒让所有模块加载完成

function showNotification(message, type = 'info') {
    const colors = {
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545',
        info: '#17a2b8'
    };

    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${colors[type]};
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        z-index: 10001;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        font-size: 16px;
        font-weight: 500;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        animation: slideDown 0.3s ease-out;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    notification.textContent = message;
    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideDown 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }, 5000);
}

console.log('✅ [SystemValidator] 验证脚本已加载，等待模块初始化完成...');
