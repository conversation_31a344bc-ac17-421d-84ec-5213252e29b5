/**
 * 自动解析管理器 - 智能自动解析功能管理
 * 负责监控用户输入并自动触发解析功能
 * 创建日期: 2025-01-11
 */

class AutoParseManager {
    constructor(eventBus = window.mdacEventBus, stateManager = null, aiService = null) {
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        this.aiService = aiService;
        
        // 自动解析配置
        this.config = {
            enabled: false,
            debounceDelay: 500,
            minTextLength: 10,
            maxTextLength: 10000,
            autoFillAfterParse: false,
            showCountdown: true,
            countdownDuration: 3000
        };

        // 状态管理
        this.isActive = false;
        this.debounceTimer = null;
        this.countdownTimer = null;
        this.lastInputTime = 0;
        this.lastInputContent = '';
        
        // 监听器
        this.inputListeners = new Map();
        this.imageListeners = new Map();
        
        // 统计信息
        this.stats = {
            totalAutoParses: 0,
            successfulParses: 0,
            failedParses: 0,
            averageParseTime: 0,
            lastParseTime: null
        };

        console.log('🤖 [AutoParseManager] 自动解析管理器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听配置变更
        this.eventBus.on('autoParse:config-changed', (config) => {
            this.updateConfig(config);
        });

        // 监听启用/禁用
        this.eventBus.on('autoParse:toggle', (enabled) => {
            this.toggle(enabled);
        });

        // 监听输入变化
        this.eventBus.on('ui:input-changed', (data) => {
            this.handleInputChange(data);
        });

        // 监听图片上传
        this.eventBus.on('ui:image-uploaded', (data) => {
            this.handleImageUpload(data);
        });

        // 监听解析结果
        this.eventBus.on('ai:processing-complete', (data) => {
            this.handleParseComplete(data);
        });

        this.eventBus.on('ai:processing-error', (data) => {
            this.handleParseError(data);
        });
    }

    /**
     * 启动自动解析管理器
     */
    start() {
        if (this.isActive) {
            console.warn('⚠️ [AutoParseManager] 自动解析管理器已经启动');
            return;
        }

        console.log('🚀 [AutoParseManager] 启动自动解析管理器');
        
        this.isActive = true;
        
        // 设置DOM监听器
        this.setupDOMListeners();
        
        // 从状态管理器获取配置
        this.loadConfigFromState();

        // 发布启动事件
        if (this.eventBus) {
            this.eventBus.emit('autoParse:started', {
                timestamp: Date.now(),
                config: this.config
            });
        }
    }

    /**
     * 停止自动解析管理器
     */
    stop() {
        if (!this.isActive) return;

        console.log('🛑 [AutoParseManager] 停止自动解析管理器');
        
        this.isActive = false;
        
        // 清理定时器
        this.clearTimers();
        
        // 移除DOM监听器
        this.removeDOMListeners();

        // 发布停止事件
        if (this.eventBus) {
            this.eventBus.emit('autoParse:stopped', {
                timestamp: Date.now()
            });
        }
    }

    /**
     * 切换自动解析状态
     * @param {boolean} enabled - 是否启用
     */
    toggle(enabled = null) {
        const newState = enabled !== null ? enabled : !this.config.enabled;
        
        console.log(`🔄 [AutoParseManager] 切换自动解析: ${newState ? '启用' : '禁用'}`);
        
        this.config.enabled = newState;
        
        // 更新状态
        if (this.stateManager) {
            this.stateManager.set('autoParse.enabled', newState);
        }

        // 发布状态变更事件
        if (this.eventBus) {
            this.eventBus.emit('autoParse:toggled', {
                enabled: newState,
                timestamp: Date.now()
            });
        }

        return newState;
    }

    /**
     * 设置DOM监听器
     */
    setupDOMListeners() {
        // 监听文本输入
        const textInput = document.getElementById('input-text');
        if (textInput) {
            const textListener = (e) => this.handleTextInput(e);
            textInput.addEventListener('input', textListener);
            textInput.addEventListener('paste', textListener);
            this.inputListeners.set('text', { element: textInput, listener: textListener });
        }

        // 监听图片上传
        const imageInput = document.getElementById('image-upload');
        if (imageInput) {
            const imageListener = (e) => this.handleImageInput(e);
            imageInput.addEventListener('change', imageListener);
            this.imageListeners.set('image', { element: imageInput, listener: imageListener });
        }
    }

    /**
     * 移除DOM监听器
     */
    removeDOMListeners() {
        // 移除文本输入监听器
        for (const [key, { element, listener }] of this.inputListeners) {
            element.removeEventListener('input', listener);
            element.removeEventListener('paste', listener);
        }
        this.inputListeners.clear();

        // 移除图片输入监听器
        for (const [key, { element, listener }] of this.imageListeners) {
            element.removeEventListener('change', listener);
        }
        this.imageListeners.clear();
    }

    /**
     * 处理文本输入
     * @param {Event} event - 输入事件
     */
    handleTextInput(event) {
        if (!this.config.enabled || !this.isActive) return;

        const text = event.target.value.trim();
        const currentTime = Date.now();

        // 更新最后输入时间
        this.lastInputTime = currentTime;

        // 检查输入长度
        if (text.length < this.config.minTextLength) {
            this.clearTimers();
            return;
        }

        if (text.length > this.config.maxTextLength) {
            console.warn('⚠️ [AutoParseManager] 输入文本过长，跳过自动解析');
            return;
        }

        // 检查内容是否有变化
        if (text === this.lastInputContent) {
            return;
        }

        this.lastInputContent = text;

        // 清除之前的定时器
        this.clearTimers();

        // 设置防抖定时器
        this.debounceTimer = setTimeout(() => {
            this.triggerAutoParse('text', text);
        }, this.config.debounceDelay);

        console.log(`⏳ [AutoParseManager] 文本输入检测，${this.config.debounceDelay}ms后自动解析`);
    }

    /**
     * 处理图片输入
     * @param {Event} event - 输入事件
     */
    handleImageInput(event) {
        if (!this.config.enabled || !this.isActive) return;

        const file = event.target.files[0];
        if (!file || !file.type.startsWith('image/')) return;

        console.log('📷 [AutoParseManager] 图片上传检测，准备自动解析');

        // 图片上传立即触发解析（无防抖）
        this.triggerAutoParse('image', null, file);
    }

    /**
     * 处理输入变化事件
     * @param {Object} data - 输入数据
     */
    handleInputChange(data) {
        if (!this.config.enabled || !this.isActive) return;

        const { type, content, file } = data;

        if (type === 'text') {
            this.handleTextInput({ target: { value: content } });
        } else if (type === 'image') {
            this.handleImageUpload({ file });
        }
    }

    /**
     * 处理图片上传事件
     * @param {Object} data - 图片数据
     */
    handleImageUpload(data) {
        if (!this.config.enabled || !this.isActive) return;

        const { file } = data;
        if (file) {
            this.triggerAutoParse('image', null, file);
        }
    }

    /**
     * 触发自动解析
     * @param {string} type - 输入类型
     * @param {string} text - 文本内容
     * @param {File} image - 图片文件
     */
    async triggerAutoParse(type, text = null, image = null) {
        try {
            console.log(`🤖 [AutoParseManager] 触发自动解析: ${type}`);

            // 显示倒计时（如果启用）
            if (this.config.showCountdown && this.config.countdownDuration > 0) {
                await this.showCountdown();
            }

            // 检查是否仍然需要解析（用户可能在倒计时期间修改了内容）
            if (type === 'text') {
                const currentText = document.getElementById('input-text')?.value.trim() || '';
                if (currentText !== text) {
                    console.log('📝 [AutoParseManager] 文本内容已变更，取消自动解析');
                    return;
                }
            }

            // 更新统计
            this.stats.totalAutoParses++;
            this.stats.lastParseTime = Date.now();

            // 发布自动解析开始事件
            if (this.eventBus) {
                this.eventBus.emit('autoParse:triggered', {
                    type: type,
                    hasText: !!text,
                    hasImage: !!image,
                    timestamp: Date.now()
                });
            }

            // 执行解析
            if (this.aiService) {
                const startTime = Date.now();
                const result = await this.aiService.parseContent(text, image);
                const parseTime = Date.now() - startTime;

                // 更新平均解析时间
                this.updateAverageParseTime(parseTime);

                // 如果启用了自动填充
                if (this.config.autoFillAfterParse && result.success) {
                    this.triggerAutoFill(result.data);
                }

            } else {
                // 通过事件总线触发解析
                if (this.eventBus) {
                    this.eventBus.emit('ai:request', {
                        text: text,
                        image: image,
                        source: 'autoParse'
                    });
                }
            }

        } catch (error) {
            console.error('❌ [AutoParseManager] 自动解析失败', error);
            this.stats.failedParses++;

            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('autoParse:error', {
                    error: error.message,
                    type: type,
                    timestamp: Date.now()
                });
            }
        }
    }

    /**
     * 显示倒计时
     */
    async showCountdown() {
        return new Promise((resolve) => {
            let remaining = Math.ceil(this.config.countdownDuration / 1000);
            
            console.log(`⏰ [AutoParseManager] 自动解析倒计时: ${remaining}秒`);

            // 发布倒计时开始事件
            if (this.eventBus) {
                this.eventBus.emit('autoParse:countdown-start', {
                    duration: remaining,
                    timestamp: Date.now()
                });
            }

            const countdownInterval = setInterval(() => {
                remaining--;
                
                // 发布倒计时更新事件
                if (this.eventBus) {
                    this.eventBus.emit('autoParse:countdown-update', {
                        remaining: remaining,
                        timestamp: Date.now()
                    });
                }

                if (remaining <= 0) {
                    clearInterval(countdownInterval);
                    
                    // 发布倒计时结束事件
                    if (this.eventBus) {
                        this.eventBus.emit('autoParse:countdown-end', {
                            timestamp: Date.now()
                        });
                    }
                    
                    resolve();
                }
            }, 1000);

            // 保存定时器引用以便取消
            this.countdownTimer = countdownInterval;
        });
    }

    /**
     * 触发自动填充
     * @param {Object} data - 解析的数据
     */
    triggerAutoFill(data) {
        console.log('📝 [AutoParseManager] 触发自动填充');

        if (this.eventBus) {
            this.eventBus.emit('form:fill-request', {
                formData: data,
                source: 'autoParse',
                options: {
                    skipValidation: false,
                    showProgress: true
                }
            });
        }
    }

    /**
     * 处理解析完成
     * @param {Object} data - 解析结果
     */
    handleParseComplete(data) {
        if (data.source === 'autoParse') {
            this.stats.successfulParses++;
            console.log('✅ [AutoParseManager] 自动解析完成');
        }
    }

    /**
     * 处理解析错误
     * @param {Object} data - 错误数据
     */
    handleParseError(data) {
        if (data.source === 'autoParse') {
            this.stats.failedParses++;
            console.log('❌ [AutoParseManager] 自动解析失败');
        }
    }

    /**
     * 更新平均解析时间
     * @param {number} parseTime - 解析时间
     */
    updateAverageParseTime(parseTime) {
        const totalParses = this.stats.successfulParses + this.stats.failedParses;
        if (totalParses > 0) {
            this.stats.averageParseTime = (this.stats.averageParseTime * (totalParses - 1) + parseTime) / totalParses;
        }
    }

    /**
     * 清除定时器
     */
    clearTimers() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        console.log('⚙️ [AutoParseManager] 配置已更新', this.config);

        // 保存到状态管理器
        if (this.stateManager) {
            this.stateManager.set('autoParse.config', this.config);
        }
    }

    /**
     * 从状态管理器加载配置
     */
    loadConfigFromState() {
        if (!this.stateManager) return;

        const savedConfig = this.stateManager.get('autoParse.config');
        if (savedConfig) {
            this.config = { ...this.config, ...savedConfig };
        }

        const enabled = this.stateManager.get('autoParse.enabled');
        if (enabled !== undefined) {
            this.config.enabled = enabled;
        }
    }

    /**
     * 取消当前的自动解析
     */
    cancelAutoParse() {
        console.log('🚫 [AutoParseManager] 取消自动解析');
        
        this.clearTimers();

        // 发布取消事件
        if (this.eventBus) {
            this.eventBus.emit('autoParse:cancelled', {
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        const successRate = this.stats.totalAutoParses > 0 ? 
            this.stats.successfulParses / this.stats.totalAutoParses : 0;

        return {
            ...this.stats,
            successRate: successRate,
            isActive: this.isActive,
            isEnabled: this.config.enabled,
            lastInputTime: this.lastInputTime,
            hasActiveTimer: !!(this.debounceTimer || this.countdownTimer)
        };
    }

    /**
     * 获取配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            totalAutoParses: 0,
            successfulParses: 0,
            failedParses: 0,
            averageParseTime: 0,
            lastParseTime: null
        };

        console.log('🔄 [AutoParseManager] 统计信息已重置');

        // 发布重置事件
        if (this.eventBus) {
            this.eventBus.emit('autoParse:stats-reset', {
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取状态信息
     */
    getStatus() {
        return {
            isActive: this.isActive,
            isEnabled: this.config.enabled,
            hasActiveTimer: !!(this.debounceTimer || this.countdownTimer),
            lastInputTime: this.lastInputTime,
            config: this.config,
            stats: this.getStats()
        };
    }

    /**
     * 销毁自动解析管理器
     */
    destroy() {
        console.log('🗑️ [AutoParseManager] 销毁自动解析管理器');

        // 停止管理器
        this.stop();

        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('autoParse:config-changed');
            this.eventBus.off('autoParse:toggle');
            this.eventBus.off('ui:input-changed');
            this.eventBus.off('ui:image-uploaded');
            this.eventBus.off('ai:processing-complete');
            this.eventBus.off('ai:processing-error');
        }

        // 重置状态
        this.isActive = false;
        this.lastInputContent = '';
        this.lastInputTime = 0;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AutoParseManager;
} else {
    window.AutoParseManager = AutoParseManager;
}

console.log('✅ [AutoParseManager] 自动解析管理器模块已加载');
