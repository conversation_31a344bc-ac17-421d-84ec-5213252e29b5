/**
 * UI渲染器 - 统一的UI渲染和显示管理
 * 负责渲染侧边栏界面、管理UI状态和用户交互
 * 创建日期: 2025-01-11
 */

class UIRenderer {
    constructor(eventBus = window.mdacEventBus, stateManager = null, modalManager = null, progressVisualizer = null) {
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        this.modalManager = modalManager;
        this.progressVisualizer = progressVisualizer;

        // UI容器
        this.container = null;

        // UI组件
        this.components = new Map();

        // 渲染配置
        this.config = {
            theme: 'light',
            animations: true,
            autoSave: true,
            debugMode: false
        };

        // UI模板
        this.templates = {
            main: this.createMainTemplate.bind(this),
            header: this.createHeaderTemplate.bind(this),
            tabs: this.createTabsTemplate.bind(this),
            inputSection: this.createInputSectionTemplate.bind(this),
            resultSection: this.createResultSectionTemplate.bind(this),
            settingsSection: this.createSettingsSectionTemplate.bind(this),
            debugSection: this.createDebugSectionTemplate.bind(this)
        };

        console.log('🎨 [UIRenderer] UI渲染器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化UI渲染器
     */
    async initialize() {
        try {
            console.log('🎨 [UIRenderer] 开始初始化UI');

            // 查找或创建容器
            this.container = document.getElementById('mdac-sidepanel') || document.body;

            // 渲染主界面
            await this.renderMainInterface();

            // 初始化组件
            await this.initializeComponents();

            // 绑定事件
            this.bindEvents();

            // 应用主题
            this.applyTheme();

            // 恢复状态
            await this.restoreState();

            console.log('✅ [UIRenderer] UI初始化完成');

            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('ui:initialized', {
                    timestamp: Date.now()
                });
            }

        } catch (error) {
            console.error('❌ [UIRenderer] UI初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听状态变化
        this.eventBus.on('state:changed', (data) => {
            this.handleStateChange(data);
        });

        // 监听UI更新请求
        this.eventBus.on('ui:update', (data) => {
            this.updateUI(data);
        });

        // 监听主题变更
        this.eventBus.on('ui:theme-changed', (theme) => {
            this.changeTheme(theme);
        });

        // 监听数据显示请求
        this.eventBus.on('ui:display-data', (data) => {
            this.displayParsedData(data);
        });
    }

    /**
     * 渲染主界面
     */
    async renderMainInterface() {
        console.log('🎨 [UIRenderer] 渲染主界面');

        const mainHTML = this.templates.main();
        this.container.innerHTML = mainHTML;

        // 注入样式
        this.injectStyles();
    }

    /**
     * 创建主模板
     */
    createMainTemplate() {
        return `
            <div class="mdac-sidepanel">
                <div class="mdac-header">
                    ${this.templates.header()}
                </div>

                <div class="mdac-tabs">
                    ${this.templates.tabs()}
                </div>

                <div class="mdac-content">
                    <div class="tab-content active" data-tab="input">
                        ${this.templates.inputSection()}
                    </div>

                    <div class="tab-content" data-tab="result">
                        ${this.templates.resultSection()}
                    </div>

                    <div class="tab-content" data-tab="settings">
                        ${this.templates.settingsSection()}
                    </div>

                    <div class="tab-content" data-tab="debug">
                        ${this.templates.debugSection()}
                    </div>
                </div>

                <div class="mdac-footer">
                    <div class="status-bar">
                        <span class="status-text">就绪</span>
                        <span class="version-info">v1.0.0</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建头部模板
     */
    createHeaderTemplate() {
        return `
            <div class="header-content">
                <div class="logo-section">
                    <h2 class="app-title">MDAC助手</h2>
                    <span class="app-subtitle">智能表单填充</span>
                </div>

                <div class="header-actions">
                    <button class="mdac-button mdac-button-primary" id="quick-access-mdac" title="快速访问MDAC网站">
                        🌐 MDAC
                    </button>

                    <button class="mdac-button mdac-button-secondary" id="toggle-debug" title="切换调试模式">
                        🐛
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 创建标签页模板
     */
    createTabsTemplate() {
        return `
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="input">
                    📝 输入
                </button>
                <button class="tab-button" data-tab="result">
                    📊 结果
                </button>
                <button class="tab-button" data-tab="settings">
                    ⚙️ 设置
                </button>
                <button class="tab-button" data-tab="debug" style="display: none;">
                    🐛 调试
                </button>
            </div>
        `;
    }

    /**
     * 创建输入区域模板
     */
    createInputSectionTemplate() {
        return `
            <div class="input-section">
                <div class="input-group">
                    <label for="input-text" class="input-label">文本内容</label>
                    <textarea
                        id="input-text"
                        class="input-textarea"
                        placeholder="请输入或粘贴需要解析的文本内容..."
                        rows="6"
                    ></textarea>
                </div>

                <div class="input-group">
                    <label for="image-upload" class="input-label">图片上传</label>
                    <div class="image-upload-area" id="image-upload-area">
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                        <div class="upload-placeholder">
                            <div class="upload-icon">📷</div>
                            <div class="upload-text">点击或拖拽上传图片</div>
                            <div class="upload-hint">支持 JPG、PNG、WebP 格式</div>
                        </div>
                        <div class="image-preview" style="display: none;">
                            <img id="preview-image" alt="预览图片">
                            <button class="remove-image" id="remove-image">×</button>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="mdac-button mdac-button-primary" id="parse-button">
                        🤖 解析内容
                    </button>
                    <button class="mdac-button mdac-button-secondary" id="clear-button">
                        🧹 清除
                    </button>
                </div>

                <div class="auto-parse-section">
                    <label class="checkbox-label">
                        <input type="checkbox" id="auto-parse-toggle">
                        <span class="checkmark"></span>
                        自动解析（输入后自动处理）
                    </label>
                </div>
            </div>
        `;
    }

    /**
     * 创建结果区域模板
     */
    createResultSectionTemplate() {
        return `
            <div class="result-section">
                <div class="result-header">
                    <h3>解析结果</h3>
                    <div class="result-actions">
                        <button class="mdac-button mdac-button-primary" id="fill-form-button" disabled>
                            📝 填充表单
                        </button>
                        <button class="mdac-button mdac-button-secondary" id="export-data-button" disabled>
                            💾 导出数据
                        </button>
                    </div>
                </div>

                <div class="result-content" id="result-content">
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">暂无解析结果</div>
                        <div class="empty-hint">请先在输入页面添加内容并解析</div>
                    </div>
                </div>

                <div class="confidence-section" id="confidence-section" style="display: none;">
                    <h4>置信度评估</h4>
                    <div class="confidence-bars" id="confidence-bars"></div>
                </div>

                <div class="validation-section" id="validation-section" style="display: none;">
                    <h4>数据验证</h4>
                    <div class="validation-results" id="validation-results"></div>
                </div>
            </div>
        `;
    }

    /**
     * 创建设置区域模板
     */
    createSettingsSectionTemplate() {
        return `
            <div class="settings-section">
                <div class="settings-group">
                    <h3>AI设置</h3>

                    <div class="setting-item">
                        <label class="setting-label">API密钥状态</label>
                        <div class="setting-value">
                            <span class="status-indicator" id="api-status">🔴 未配置</span>
                            <button class="mdac-button mdac-button-small" id="test-api-button">测试连接</button>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label class="setting-label">置信度阈值</label>
                        <div class="setting-value">
                            <input type="range" id="confidence-threshold" min="0" max="1" step="0.1" value="0.7">
                            <span id="confidence-value">70%</span>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>表单填充</h3>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-fill-toggle">
                            <span class="checkmark"></span>
                            解析后自动填充表单
                        </label>
                    </div>

                    <div class="setting-item">
                        <label class="setting-label">填充延迟</label>
                        <div class="setting-value">
                            <input type="range" id="fill-delay" min="0" max="2000" step="100" value="500">
                            <span id="delay-value">500ms</span>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>界面设置</h3>

                    <div class="setting-item">
                        <label class="setting-label">主题</label>
                        <div class="setting-value">
                            <select id="theme-select">
                                <option value="light">浅色</option>
                                <option value="dark">深色</option>
                                <option value="auto">跟随系统</option>
                            </select>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="animations-toggle" checked>
                            <span class="checkmark"></span>
                            启用动画效果
                        </label>
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="mdac-button mdac-button-secondary" id="reset-settings-button">
                        🔄 重置设置
                    </button>
                    <button class="mdac-button mdac-button-secondary" id="export-settings-button">
                        💾 导出设置
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 创建调试区域模板
     */
    createDebugSectionTemplate() {
        return `
            <div class="debug-section">
                <div class="debug-header">
                    <h3>调试信息</h3>
                    <div class="debug-actions">
                        <button class="mdac-button mdac-button-small" id="clear-logs-button">清除日志</button>
                        <button class="mdac-button mdac-button-small" id="export-logs-button">导出日志</button>
                    </div>
                </div>

                <div class="debug-tabs">
                    <button class="debug-tab-button active" data-debug-tab="logs">日志</button>
                    <button class="debug-tab-button" data-debug-tab="state">状态</button>
                    <button class="debug-tab-button" data-debug-tab="performance">性能</button>
                </div>

                <div class="debug-content">
                    <div class="debug-tab-content active" data-debug-tab="logs">
                        <div class="log-console" id="log-console">
                            <div class="log-entry">
                                <span class="log-time">[${new Date().toLocaleTimeString()}]</span>
                                <span class="log-level info">INFO</span>
                                <span class="log-message">调试控制台已启动</span>
                            </div>
                        </div>
                    </div>

                    <div class="debug-tab-content" data-debug-tab="state">
                        <div class="state-viewer" id="state-viewer">
                            <pre id="state-content">加载中...</pre>
                        </div>
                    </div>

                    <div class="debug-tab-content" data-debug-tab="performance">
                        <div class="performance-metrics" id="performance-metrics">
                            <div class="metric-item">
                                <span class="metric-label">内存使用:</span>
                                <span class="metric-value" id="memory-usage">--</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">API调用:</span>
                                <span class="metric-value" id="api-calls">--</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">表单填充:</span>
                                <span class="metric-value" id="form-fills">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 注入CSS样式
     */
    injectStyles() {
        const styleId = 'mdac-ui-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .mdac-sidepanel {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                height: 100vh;
                display: flex;
                flex-direction: column;
                background: #f8f9fa;
                color: #333;
            }

            .mdac-header {
                background: white;
                border-bottom: 1px solid #e5e5e5;
                padding: 16px;
                flex-shrink: 0;
            }

            .header-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .app-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            .app-subtitle {
                font-size: 12px;
                color: #666;
                margin-left: 8px;
            }

            .header-actions {
                display: flex;
                gap: 8px;
            }

            .mdac-tabs {
                background: white;
                border-bottom: 1px solid #e5e5e5;
                flex-shrink: 0;
            }

            .tab-buttons {
                display: flex;
                overflow-x: auto;
            }

            .tab-button {
                background: none;
                border: none;
                padding: 12px 16px;
                cursor: pointer;
                font-size: 14px;
                color: #666;
                border-bottom: 2px solid transparent;
                transition: all 0.2s;
                white-space: nowrap;
            }

            .tab-button:hover {
                color: #333;
                background: #f8f9fa;
            }

            .tab-button.active {
                color: #007bff;
                border-bottom-color: #007bff;
            }

            .mdac-content {
                flex: 1;
                overflow-y: auto;
                position: relative;
            }

            .tab-content {
                display: none;
                padding: 20px;
                height: 100%;
                overflow-y: auto;
            }

            .tab-content.active {
                display: block;
            }

            .mdac-button {
                background: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s;
                display: inline-flex;
                align-items: center;
                gap: 6px;
            }

            .mdac-button:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .mdac-button:disabled {
                background: #6c757d;
                cursor: not-allowed;
                transform: none;
            }

            .mdac-button-secondary {
                background: #6c757d;
            }

            .mdac-button-secondary:hover {
                background: #545b62;
            }

            .mdac-button-small {
                padding: 4px 8px;
                font-size: 12px;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 初始化组件
     */
    async initializeComponents() {
        console.log('🎨 [UIRenderer] 初始化UI组件');

        // 初始化各个组件
        this.initializeImageUpload();
        this.initializeTabSwitching();
        this.initializeSettings();
        this.initializeDebugPanel();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        console.log('🎨 [UIRenderer] 绑定UI事件');

        // 快速访问MDAC按钮
        const quickAccessBtn = document.getElementById('quick-access-mdac');
        if (quickAccessBtn) {
            quickAccessBtn.addEventListener('click', () => {
                this.openMDACWebsite();
            });
        }

        // 调试模式切换
        const debugToggle = document.getElementById('toggle-debug');
        if (debugToggle) {
            debugToggle.addEventListener('click', () => {
                this.toggleDebugMode();
            });
        }
    }

    /**
     * 初始化图片上传
     */
    initializeImageUpload() {
        const uploadArea = document.getElementById('image-upload-area');
        const fileInput = document.getElementById('image-upload');
        const previewImage = document.getElementById('preview-image');
        const removeButton = document.getElementById('remove-image');

        if (!uploadArea || !fileInput) return;

        // 点击上传
        uploadArea.addEventListener('click', () => {
            if (!uploadArea.classList.contains('has-image')) {
                fileInput.click();
            }
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleImageUpload(file);
            }
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                this.handleImageUpload(files[0]);
            }
        });

        // 移除图片
        if (removeButton) {
            removeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeImage();
            });
        }
    }

    /**
     * 处理图片上传
     * @param {File} file - 图片文件
     */
    handleImageUpload(file) {
        const uploadArea = document.getElementById('image-upload-area');
        const previewImage = document.getElementById('preview-image');
        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');

        if (!uploadArea || !previewImage) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            previewImage.src = e.target.result;
            placeholder.style.display = 'none';
            preview.style.display = 'block';
            uploadArea.classList.add('has-image');

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('data.inputImage', file);
            }

            // 发布事件
            if (this.eventBus) {
                this.eventBus.emit('ui:image-uploaded', { file });
            }
        };
        reader.readAsDataURL(file);
    }

    /**
     * 移除图片
     */
    removeImage() {
        const uploadArea = document.getElementById('image-upload-area');
        const fileInput = document.getElementById('image-upload');
        const placeholder = uploadArea.querySelector('.upload-placeholder');
        const preview = uploadArea.querySelector('.image-preview');

        if (!uploadArea) return;

        placeholder.style.display = 'block';
        preview.style.display = 'none';
        uploadArea.classList.remove('has-image');
        fileInput.value = '';

        // 更新状态
        if (this.stateManager) {
            this.stateManager.set('data.inputImage', null);
        }

        // 发布事件
        if (this.eventBus) {
            this.eventBus.emit('ui:image-removed');
        }
    }

    /**
     * 初始化标签页切换功能
     */
    initializeTabSwitching() {
        console.log('🔄 [UIRenderer] 初始化标签页切换功能');

        try {
            // 获取所有标签按钮和内容区域
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            if (tabButtons.length === 0) {
                console.warn('⚠️ [UIRenderer] 未找到标签按钮，跳过标签切换初始化');
                return;
            }

            // 为每个标签按钮添加点击事件
            tabButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetTab = button.getAttribute('data-tab');
                    this.switchTab(targetTab);
                });
            });

            console.log('✅ [UIRenderer] 标签页切换功能初始化完成');

        } catch (error) {
            console.error('❌ [UIRenderer] 标签页切换初始化失败:', error);
        }
    }

    /**
     * 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        console.log(`🔄 [UIRenderer] 切换到标签页: ${tabName}`);

        try {
            // 移除所有活跃状态
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => button.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 激活目标标签
            const targetButton = document.querySelector(`[data-tab="${tabName}"]`);
            const targetContent = document.querySelector(`.tab-content[data-tab="${tabName}"]`);

            if (targetButton) {
                targetButton.classList.add('active');
            }

            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 发布标签切换事件
            if (this.eventBus) {
                this.eventBus.emit('ui:tab-switched', { tabName });
            }

            // 更新状态
            if (this.stateManager) {
                this.stateManager.set('ui.activeTab', tabName);
            }

        } catch (error) {
            console.error('❌ [UIRenderer] 标签页切换失败:', error);
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIRenderer;
} else {
    window.UIRenderer = UIRenderer;
}

console.log('✅ [UIRenderer] UI渲染器已加载');