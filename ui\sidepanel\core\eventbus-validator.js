/**
 * EventBus 修复版本验证器
 * 用于快速验证 EventBus-fixed.js 是否正常工作
 */

(function() {
    'use strict';

    console.log('🔍 [EventBus验证器] 开始验证修复版本...');

    function testEventBus() {
        try {
            // 检查EventBus类是否存在
            if (typeof window.EventBus !== 'function') {
                console.error('❌ [验证失败] EventBus类未定义');
                return false;
            }
            console.log('✅ [验证通过] EventBus类已定义');

            // 检查全局实例是否存在
            if (typeof window.mdacEventBus !== 'object') {
                console.error('❌ [验证失败] mdacEventBus实例未定义');
                return false;
            }
            console.log('✅ [验证通过] mdacEventBus实例已创建');

            // 测试基本功能
            let testPassed = false;
            const testData = { message: 'test-message', timestamp: Date.now() };

            // 注册测试监听器
            const unsubscribe = window.mdacEventBus.on('test-event', (data) => {
                if (data && data.message === 'test-message') {
                    testPassed = true;
                    console.log('✅ [验证通过] 事件发布-订阅功能正常');
                }
            });

            // 发布测试事件
            window.mdacEventBus.emit('test-event', testData);

            // 清理测试监听器
            unsubscribe();

            if (!testPassed) {
                console.error('❌ [验证失败] 事件发布-订阅功能异常');
                return false;
            }

            // 检查统计信息
            const stats = window.mdacEventBus.getStats();
            console.log('📊 [统计信息]', stats);

            console.log('🎉 [验证完成] EventBus修复版本验证通过！');
            return true;

        } catch (error) {
            console.error('❌ [验证异常] EventBus验证过程中发生错误:', error);
            return false;
        }
    }

    // 导出验证函数到全局对象
    window.validateEventBusFix = testEventBus;

    // 如果EventBus已经加载，立即测试
    if (window.EventBus && window.mdacEventBus) {
        setTimeout(testEventBus, 100);
    } else {
        console.log('⏳ [EventBus验证器] 等待EventBus加载...');
        
        // 等待EventBus加载
        const checkInterval = setInterval(() => {
            if (window.EventBus && window.mdacEventBus) {
                clearInterval(checkInterval);
                testEventBus();
            }
        }, 500);

        // 5秒后停止等待
        setTimeout(() => {
            clearInterval(checkInterval);
            if (!window.EventBus || !window.mdacEventBus) {
                console.error('❌ [验证超时] EventBus未在5秒内加载完成');
            }
        }, 5000);
    }

})();
