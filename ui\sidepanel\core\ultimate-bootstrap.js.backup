/**
 * 模块加载器引导程序 - 一劳永逸解决重复声明问题
 * 通过统一的模块加载管理，确保每个模块只被加载一次
 * 创建日期: 2025-07-12
 * 解决方案: 方案一 - 统一模块加载器管理
 */

(function() {
    'use strict';

    // 防止多次执行
    if (window.mdacUltimateBootstrap) {
        console.warn('⚠️ [UltimateBootstrap] 终极引导程序已存在，跳过重复加载');
        return;
    }

    /**
     * 终极模块加载器 - 彻底解决重复声明问题
     */
    class UltimateModuleBootstrap {
        constructor() {
            // 模块状态追踪
            this.moduleStates = new Map();
            this.loadingQueue = [];
            this.loadedCount = 0;
            this.totalModules = 0;
            
            // 全局状态监控
            this.globalObjects = new Set();
            this.duplicateDetector = new Map();
            
            // 错误处理
            this.errors = [];
            this.warnings = [];
            
            console.log('🔥 [UltimateBootstrap] 终极模块加载器启动 - 彻底解决重复声明问题');
            
            // 立即开始预防性检查
            this.preventDuplicateDeclarations();
        }

        /**
         * 预防性重复声明检查
         */
        preventDuplicateDeclarations() {
            const classesToProtect = [
                'EventBus', 'StateManager', 'DebugLogger', 'DateFormatter', 
                'MessageHelper', 'StorageService', 'DataValidator', 'ModalManager',
                'DataManager', 'FieldMatcher', 'AIService', 'UIRenderer', 
                'FormFiller', 'CityViewer', 'TextParser', 'ConfidenceEvaluator',
                'AutoParseManager', 'ModuleRegistry', 'ModuleLoader',
                'EventManager', 'SidePanelCore', 'ModuleInitializer',
                'ImageProcessor', 'ProgressVisualizer', 'PreviewManager',
                'LegacyAdapter'
            ];

            classesToProtect.forEach(className => {
                if (window[className]) {
                    console.log(`🛡️ [UltimateBootstrap] 检测到已存在的类: ${className}`);
                    this.duplicateDetector.set(className, {
                        existing: true,
                        source: 'pre-existing'
                    });
                } else {
                    // 设置属性拦截器防止重复声明
                    this.setupPropertyProtection(className);
                }
            });
        }

        /**
         * 设置属性保护
         */
        setupPropertyProtection(className) {
            let isSet = false;
            let actualValue = undefined;
            
            Object.defineProperty(window, className, {
                get() {
                    return actualValue;
                },
                set(value) {
                    if (isSet && actualValue) {
                        console.warn(`⚠️ [UltimateBootstrap] 阻止重复声明: ${className}`);
                        return;
                    }
                    isSet = true;
                    actualValue = value;
                    console.log(`✅ [UltimateBootstrap] 类 ${className} 已安全注册`);
                },
                configurable: true,
                enumerable: true
            });
        }

        /**
         * 获取优化后的模块定义
         */
        getModuleDefinitions() {
            return {
                // 核心模块 - 最基础的功能
                'EventBus': {
                    path: 'ui/sidepanel/core/EventBus.js',
                    dependencies: [],
                    essential: true,
                    priority: 1
                },
                'DebugLogger': {
                    path: 'ui/sidepanel/utils/DebugLogger.js',
                    dependencies: ['EventBus'],
                    essential: true,
                    priority: 2
                },
                'StateManager': {
                    path: 'ui/sidepanel/core/StateManager.js',
                    dependencies: ['EventBus', 'DebugLogger'],
                    essential: true,
                    priority: 3
                },
                'EventManager': {
                    path: 'ui/sidepanel/core/EventManager.js',
                    dependencies: ['EventBus', 'StateManager'],
                    essential: true,
                    priority: 4
                },
                'SidePanelCore': {
                    path: 'ui/sidepanel/core/SidePanelCore.js',
                    dependencies: ['EventBus', 'StateManager', 'EventManager'],
                    essential: true,
                    priority: 5
                },
                'ModuleInitializer': {
                    path: 'ui/sidepanel/core/ModuleInitializer.js',
                    dependencies: ['EventBus'],
                    essential: true,
                    priority: 6
                },
                'ModuleRegistry': {
                    path: 'ui/sidepanel/core/ModuleRegistry.js',
                    dependencies: ['EventBus'],
                    essential: true,
                    priority: 7
                },
                'ModuleLoader': {
                    path: 'ui/sidepanel/core/ModuleLoader.js',
                    dependencies: ['EventBus', 'ModuleRegistry'],
                    essential: true,
                    priority: 8
                },

                // 工具模块
                'DateFormatter': {
                    path: 'ui/sidepanel/utils/DateFormatter.js',
                    dependencies: [],
                    priority: 10
                },
                'MessageHelper': {
                    path: 'ui/sidepanel/utils/MessageHelper.js',
                    dependencies: ['EventBus'],
                    priority: 11
                },
                'ModuleLoadingMonitor': {
                    path: 'ui/sidepanel/utils/ModuleLoadingMonitor.js',
                    dependencies: ['EventBus'],
                    priority: 12,
                    optional: true
                },

                // 数据管理模块
                'StorageService': {
                    path: 'ui/sidepanel/data/StorageService.js',
                    dependencies: ['EventBus'],
                    priority: 20
                },
                'DataManager': {
                    path: 'ui/sidepanel/data/DataManager.js',
                    dependencies: ['EventBus', 'StorageService', 'StateManager'],
                    priority: 21
                },

                // 表单处理模块
                'DataValidator': {
                    path: 'ui/sidepanel/form/DataValidator.js',
                    dependencies: ['EventBus'],
                    priority: 30
                },
                'FieldMatcher': {
                    path: 'ui/sidepanel/form/FieldMatcher.js',
                    dependencies: ['EventBus'],
                    priority: 31
                },
                'FormFiller': {
                    path: 'ui/sidepanel/form/FormFiller.js',
                    dependencies: ['EventBus', 'FieldMatcher', 'DataValidator'],
                    priority: 32
                },

                // UI 模块
                'ModalManager': {
                    path: 'ui/sidepanel/ui/ModalManager.js',
                    dependencies: ['EventBus'],
                    priority: 40
                },
                'UIRenderer': {
                    path: 'ui/sidepanel/ui/UIRenderer.js',
                    dependencies: ['EventBus', 'StateManager', 'ModalManager'],
                    priority: 41
                },

                // AI 功能模块
                'AIService': {
                    path: 'ui/sidepanel/ai/AIService.js',
                    dependencies: ['EventBus'],
                    priority: 50
                },
                'TextParser': {
                    path: 'ui/sidepanel/ai/TextParser.js',
                    dependencies: ['EventBus'],
                    priority: 51
                },

                // 特色功能模块
                'CityViewer': {
                    path: 'ui/sidepanel/features/CityViewer.js',
                    dependencies: ['EventBus', 'ModalManager'],
                    priority: 60
                },
                'AutoParseManager': {
                    path: 'ui/sidepanel/features/AutoParseManager.js',
                    dependencies: ['EventBus', 'StateManager', 'AIService'],
                    priority: 61
                }
            };
        }

        /**
         * 检查模块是否已加载
         */
        isModuleLoaded(moduleName) {
            const definition = this.getModuleDefinitions()[moduleName];
            if (!definition) return false;

            // 检查全局对象是否存在
            try {
                // 首先检查属性描述符
                const descriptor = Object.getOwnPropertyDescriptor(window, moduleName);
                if (descriptor && descriptor.set) {
                    // 如果有 setter，说明属性保护已设置，检查是否有值
                    const value = window[moduleName];
                    return value !== undefined && value !== null;
                }
                
                // 直接检查全局对象
                return !!window[moduleName];
            } catch (error) {
                console.warn(`检查模块 ${moduleName} 时出错:`, error);
                return false;
            }
        }

        /**
         * 检查依赖是否满足
         */
        areDependenciesSatisfied(moduleName) {
            const definition = this.getModuleDefinitions()[moduleName];
            if (!definition || !definition.dependencies) return true;

            return definition.dependencies.every(dep => this.isModuleLoaded(dep));
        }

        /**
         * 安全加载脚本
         */
        async loadScript(path, moduleName) {
            return new Promise((resolve, reject) => {
                // 转换路径：将ui/sidepanel/开头的路径转换为相对于当前HTML的路径
                let actualPath = path;
                if (path.startsWith('ui/sidepanel/')) {
                    actualPath = path.replace('ui/sidepanel/', 'sidepanel/');
                }

                // 检查是否已有相同路径的脚本
                const existingScript = document.querySelector(`script[src*="${actualPath}"]`);
                if (existingScript) {
                    console.log(`📄 [UltimateBootstrap] 脚本已存在: ${actualPath}`);
                    resolve();
                    return;
                }

                // 创建新脚本
                const script = document.createElement('script');
                script.src = actualPath;
                script.dataset.module = moduleName;
                script.async = false;

                const timeout = setTimeout(() => {
                    reject(new Error(`模块 ${moduleName} 加载超时`));
                }, 15000);

                script.onload = () => {
                    clearTimeout(timeout);
                    console.log(`✅ [UltimateBootstrap] 模块 ${moduleName} 加载成功`);
                    
                    // 验证模块是否正确加载，给予更多时间让模块执行
                    setTimeout(() => {
                        if (!window[moduleName]) {
                            console.warn(`⚠️ [UltimateBootstrap] 模块 ${moduleName} 脚本已加载但全局对象未找到，正在重试...`);
                            // 尝试强制评估属性以触发 getter
                            try {
                                // 检查属性描述符，看是否有 setter
                                const descriptor = Object.getOwnPropertyDescriptor(window, moduleName);
                                if (descriptor && descriptor.set) {
                                    console.log(`✅ [UltimateBootstrap] 模块 ${moduleName} 属性保护已设置`);
                                    resolve();
                                    return;
                                }
                                
                                const testValue = window[moduleName];
                                if (!testValue) {
                                    // 给予额外时间重试
                                    setTimeout(() => {
                                        if (!window[moduleName]) {
                                            console.error(`❌ [UltimateBootstrap] 模块 ${moduleName} 最终验证失败`);
                                        } else {
                                            console.log(`✅ [UltimateBootstrap] 模块 ${moduleName} 延迟注册成功`);
                                        }
                                    }, 200);
                                }
                            } catch (error) {
                                console.error(`❌ [UltimateBootstrap] 检查模块 ${moduleName} 时出错:`, error);
                            }
                        } else {
                            console.log(`✅ [UltimateBootstrap] 模块 ${moduleName} 全局对象验证成功`);
                        }
                        resolve();
                    }, 200);
                };

                script.onerror = (error) => {
                    clearTimeout(timeout);
                    if (script.parentNode) {
                        script.parentNode.removeChild(script);
                    }
                    reject(new Error(`脚本加载失败: ${actualPath} (原路径: ${path})`));
                };

                document.head.appendChild(script);
            });
        }

        /**
         * 加载单个模块
         */
        async loadModule(moduleName) {
            if (this.isModuleLoaded(moduleName)) {
                console.log(`✅ [UltimateBootstrap] 模块 ${moduleName} 已存在`);
                return;
            }

            const definition = this.getModuleDefinitions()[moduleName];
            if (!definition) {
                throw new Error(`未找到模块定义: ${moduleName}`);
            }

            // 确保依赖已加载
            if (!this.areDependenciesSatisfied(moduleName)) {
                // 先加载依赖
                for (const dep of definition.dependencies) {
                    if (!this.isModuleLoaded(dep)) {
                        await this.loadModule(dep);
                    }
                }
            }

            // 加载模块
            try {
                await this.loadScript(definition.path, moduleName);
                this.loadedCount++;
                
                // 更新进度
                const progress = Math.round((this.loadedCount / this.totalModules) * 100);
                console.log(`📈 [UltimateBootstrap] 进度: ${progress}% (${this.loadedCount}/${this.totalModules})`);
                
            } catch (error) {
                console.error(`❌ [UltimateBootstrap] 模块 ${moduleName} 加载失败:`, error);
                this.errors.push({
                    module: moduleName,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                throw error;
            }
        }

        /**
         * 批量加载所有模块
         */
        async loadAllModules() {
            console.log('🚀 [UltimateBootstrap] 开始加载所有模块');
            
            const definitions = this.getModuleDefinitions();
            const modules = Object.keys(definitions);
            this.totalModules = modules.length;

            // 按优先级排序
            const sortedModules = modules.sort((a, b) => {
                const priorityA = definitions[a].priority || 999;
                const priorityB = definitions[b].priority || 999;
                return priorityA - priorityB;
            });

            console.log('📋 [UltimateBootstrap] 加载顺序:', sortedModules);

            const startTime = Date.now();

            // 逐个加载模块
            for (const moduleName of sortedModules) {
                try {
                    await this.loadModule(moduleName);
                } catch (error) {
                    console.error(`💥 [UltimateBootstrap] 关键模块 ${moduleName} 加载失败:`, error);
                    
                    // 如果是必要模块，继续尝试
                    const definition = definitions[moduleName];
                    if (!definition.essential) {
                        console.warn(`⚠️ [UltimateBootstrap] 跳过非必要模块: ${moduleName}`);
                        continue;
                    }
                }
            }

            const loadTime = Date.now() - startTime;
            console.log(`🎉 [UltimateBootstrap] 模块加载完成! 耗时: ${loadTime}ms`);

            // 加载主应用
            await this.loadMainApplication();
        }

        /**
         * 加载主应用
         */
        async loadMainApplication() {
            console.log('🏗️ [UltimateBootstrap] 加载主应用');
            
            try {
                // 检查核心模块
                const requiredModules = ['EventBus', 'StateManager'];
                const missing = requiredModules.filter(mod => !this.isModuleLoaded(mod));
                
                if (missing.length > 0) {
                    console.warn(`⚠️ [UltimateBootstrap] 缺少核心模块，将进入降级模式: ${missing.join(', ')}`);
                }

                // 加载主应用
                await this.loadScript('ui/sidepanel/ui-sidepanel-modular.js', 'MDACModularSidePanel');
                
                console.log('✅ [UltimateBootstrap] 主应用加载成功');
                
                // 生成加载报告
                this.generateLoadReport();
                
            } catch (error) {
                console.error('❌ [UltimateBootstrap] 主应用加载失败:', error);
                this.enterEmergencyMode();
            }
        }

        /**
         * 生成加载报告
         */
        generateLoadReport() {
            const report = {
                timestamp: new Date().toISOString(),
                totalModules: this.totalModules,
                loadedModules: this.loadedCount,
                successRate: Math.round((this.loadedCount / this.totalModules) * 100),
                errors: this.errors,
                warnings: this.warnings
            };

            console.log('📊 [UltimateBootstrap] 加载报告:', report);

            // 存储报告到 window 对象供调试使用
            window.mdacBootstrapReport = report;
        }

        /**
         * 紧急模式 - 最小功能运行
         */
        enterEmergencyMode() {
            console.warn('🚨 [UltimateBootstrap] 进入紧急模式');
            
            // 创建最基本的事件总线
            if (!window.mdacEventBus) {
                window.mdacEventBus = {
                    events: new Map(),
                    on(event, handler) {
                        if (!this.events.has(event)) {
                            this.events.set(event, []);
                        }
                        this.events.get(event).push(handler);
                    },
                    emit(event, data) {
                        if (this.events.has(event)) {
                            this.events.get(event).forEach(handler => {
                                try {
                                    handler(data);
                                } catch (error) {
                                    console.error('事件处理器错误:', error);
                                }
                            });
                        }
                    }
                };
                console.log('🆘 [UltimateBootstrap] 创建紧急事件总线');
            }

            // 显示错误消息
            this.showEmergencyUI();
        }

        /**
         * 显示紧急 UI
         */
        showEmergencyUI() {
            const emergency = document.createElement('div');
            emergency.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: #ff4444;
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 10000;
                font-family: monospace;
                font-size: 12px;
                max-width: 300px;
            `;
            emergency.innerHTML = `
                <strong>🚨 模块加载失败</strong><br>
                系统运行在紧急模式<br>
                <small>请刷新页面重试</small>
            `;
            document.body.appendChild(emergency);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (emergency.parentNode) {
                    emergency.parentNode.removeChild(emergency);
                }
            }, 5000);
        }

        /**
         * 获取加载统计
         */
        getStats() {
            return {
                totalModules: this.totalModules,
                loadedCount: this.loadedCount,
                successRate: Math.round((this.loadedCount / this.totalModules) * 100),
                errors: this.errors.length,
                warnings: this.warnings.length
            };
        }
    }

    // 创建全局实例
    window.mdacUltimateBootstrap = new UltimateModuleBootstrap();

    // 修复 DOM 元素 ID 问题
    function fixDOMIssues() {
        // 修复 autoParseTravel Enabled ID 中的空格问题
        const problematicElement = document.getElementById('autoParseTravel Enabled');
        if (problematicElement) {
            problematicElement.id = 'autoParseTravel_Enabled';
            console.log('🔧 [UltimateBootstrap] 修复了 DOM 元素 ID 空格问题');
        }

        // 检查其他可能的 DOM 问题
        const elements = document.querySelectorAll('[id*=" "]');
        elements.forEach(el => {
            const oldId = el.id;
            const newId = oldId.replace(/\s+/g, '_');
            if (oldId !== newId) {
                el.id = newId;
                console.log(`🔧 [UltimateBootstrap] 修复 ID: ${oldId} -> ${newId}`);
            }
        });
    }

    // DOM 就绪后自动开始加载
    function initializeWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    fixDOMIssues();
                    window.mdacUltimateBootstrap.loadAllModules().catch(error => {
                        console.error('💥 [UltimateBootstrap] 初始化失败:', error);
                    });
                }, 100);
            });
        } else {
            setTimeout(() => {
                fixDOMIssues();
                window.mdacUltimateBootstrap.loadAllModules().catch(error => {
                    console.error('💥 [UltimateBootstrap] 初始化失败:', error);
                });
            }, 100);
        }
    }

    // 立即开始初始化
    initializeWhenReady();

    console.log('🔥 [UltimateBootstrap] 终极模块引导程序已加载 - 重复声明问题终结者');

})();
