/**
 * 日期格式化工具 - 处理各种日期格式转换
 * 支持多种输入格式，统一输出为MDAC要求的格式
 * 创建日期: 2025-01-11
 */

class DateFormatter {
    constructor() {
        // 支持的日期格式模式
        this.patterns = [
            // 标准格式
            /^(\d{4})-(\d{1,2})-(\d{1,2})$/,           // 2024-01-15
            /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,         // 15/01/2024
            /^(\d{1,2})-(\d{1,2})-(\d{4})$/,          // 15-01-2024
            /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,        // 2024/01/15
            
            // 中文格式
            /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,       // 2024年1月15日
            /^(\d{1,2})月(\d{1,2})日$/,                // 1月15日 (当年)
            
            // 英文格式
            /^(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})$/i,  // 15 Jan 2024
            /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2}),?\s+(\d{4})$/i, // Jan 15, 2024
            
            // 其他格式
            /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,        // 15.01.2024
            /^(\d{4})\.(\d{1,2})\.(\d{1,2})$/         // 2024.01.15
        ];

        // 月份映射
        this.monthMap = {
            'jan': 1, 'january': 1,
            'feb': 2, 'february': 2,
            'mar': 3, 'march': 3,
            'apr': 4, 'april': 4,
            'may': 5,
            'jun': 6, 'june': 6,
            'jul': 7, 'july': 7,
            'aug': 8, 'august': 8,
            'sep': 9, 'september': 9,
            'oct': 10, 'october': 10,
            'nov': 11, 'november': 11,
            'dec': 12, 'december': 12
        };

        console.log('📅 [DateFormatter] 日期格式化工具已初始化');
    }

    /**
     * 解析日期字符串
     * @param {string} dateStr - 日期字符串
     * @returns {Object} 解析结果 {year, month, day, isValid, originalFormat}
     */
    parseDate(dateStr) {
        if (!dateStr || typeof dateStr !== 'string') {
            return { isValid: false, error: '无效的日期字符串' };
        }

        const trimmed = dateStr.trim();
        
        // 尝试各种格式
        for (let i = 0; i < this.patterns.length; i++) {
            const pattern = this.patterns[i];
            const match = trimmed.match(pattern);
            
            if (match) {
                const result = this.extractDateFromMatch(match, i);
                if (result.isValid) {
                    result.originalFormat = trimmed;
                    return result;
                }
            }
        }

        // 尝试使用JavaScript的Date构造函数
        const jsDate = new Date(trimmed);
        if (!isNaN(jsDate.getTime())) {
            return {
                year: jsDate.getFullYear(),
                month: jsDate.getMonth() + 1,
                day: jsDate.getDate(),
                isValid: true,
                originalFormat: trimmed,
                parsedBy: 'javascript'
            };
        }

        return { 
            isValid: false, 
            error: `无法解析日期格式: ${trimmed}`,
            originalFormat: trimmed
        };
    }

    /**
     * 从正则匹配结果中提取日期
     * @param {Array} match - 正则匹配结果
     * @param {number} patternIndex - 模式索引
     */
    extractDateFromMatch(match, patternIndex) {
        let year, month, day;

        switch (patternIndex) {
            case 0: // YYYY-MM-DD
                year = parseInt(match[1]);
                month = parseInt(match[2]);
                day = parseInt(match[3]);
                break;
                
            case 1: // DD/MM/YYYY
                day = parseInt(match[1]);
                month = parseInt(match[2]);
                year = parseInt(match[3]);
                break;
                
            case 2: // DD-MM-YYYY
                day = parseInt(match[1]);
                month = parseInt(match[2]);
                year = parseInt(match[3]);
                break;
                
            case 3: // YYYY/MM/DD
                year = parseInt(match[1]);
                month = parseInt(match[2]);
                day = parseInt(match[3]);
                break;
                
            case 4: // YYYY年MM月DD日
                year = parseInt(match[1]);
                month = parseInt(match[2]);
                day = parseInt(match[3]);
                break;
                
            case 5: // MM月DD日 (当年)
                year = new Date().getFullYear();
                month = parseInt(match[1]);
                day = parseInt(match[2]);
                break;
                
            case 6: // DD Mon YYYY
                day = parseInt(match[1]);
                month = this.monthMap[match[2].toLowerCase()];
                year = parseInt(match[3]);
                break;
                
            case 7: // Mon DD, YYYY
                month = this.monthMap[match[1].toLowerCase()];
                day = parseInt(match[2]);
                year = parseInt(match[3]);
                break;
                
            case 8: // DD.MM.YYYY
                day = parseInt(match[1]);
                month = parseInt(match[2]);
                year = parseInt(match[3]);
                break;
                
            case 9: // YYYY.MM.DD
                year = parseInt(match[1]);
                month = parseInt(match[2]);
                day = parseInt(match[3]);
                break;
        }

        // 验证日期有效性
        if (this.isValidDate(year, month, day)) {
            return { year, month, day, isValid: true, patternIndex };
        } else {
            return { 
                isValid: false, 
                error: `无效的日期: ${year}-${month}-${day}`,
                year, month, day
            };
        }
    }

    /**
     * 验证日期是否有效
     * @param {number} year - 年
     * @param {number} month - 月 (1-12)
     * @param {number} day - 日
     */
    isValidDate(year, month, day) {
        // 基本范围检查
        if (year < 1900 || year > 2100) return false;
        if (month < 1 || month > 12) return false;
        if (day < 1 || day > 31) return false;

        // 使用JavaScript Date对象验证
        const date = new Date(year, month - 1, day);
        return date.getFullYear() === year && 
               date.getMonth() === month - 1 && 
               date.getDate() === day;
    }

    /**
     * 格式化为MDAC标准格式 (DD/MM/YYYY)
     * @param {string|Object} input - 日期字符串或解析结果对象
     */
    formatForMDAC(input) {
        let parsed;
        
        if (typeof input === 'string') {
            parsed = this.parseDate(input);
        } else if (input && typeof input === 'object') {
            parsed = input;
        } else {
            return { isValid: false, error: '无效的输入' };
        }

        if (!parsed.isValid) {
            return parsed;
        }

        const { year, month, day } = parsed;
        const formattedDate = `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
        
        return {
            isValid: true,
            formatted: formattedDate,
            year,
            month,
            day,
            originalFormat: parsed.originalFormat
        };
    }

    /**
     * 格式化为ISO格式 (YYYY-MM-DD)
     * @param {string|Object} input - 日期字符串或解析结果对象
     */
    formatToISO(input) {
        let parsed;
        
        if (typeof input === 'string') {
            parsed = this.parseDate(input);
        } else if (input && typeof input === 'object') {
            parsed = input;
        } else {
            return { isValid: false, error: '无效的输入' };
        }

        if (!parsed.isValid) {
            return parsed;
        }

        const { year, month, day } = parsed;
        const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        
        return {
            isValid: true,
            formatted: formattedDate,
            year,
            month,
            day,
            originalFormat: parsed.originalFormat
        };
    }

    /**
     * 批量格式化日期
     * @param {Array} dates - 日期字符串数组
     * @param {string} format - 目标格式 ('mdac' | 'iso')
     */
    batchFormat(dates, format = 'mdac') {
        if (!Array.isArray(dates)) {
            return { isValid: false, error: '输入必须是数组' };
        }

        const results = dates.map(date => {
            if (format === 'iso') {
                return this.formatToISO(date);
            } else {
                return this.formatForMDAC(date);
            }
        });

        const successful = results.filter(r => r.isValid);
        const failed = results.filter(r => !r.isValid);

        return {
            isValid: true,
            results,
            successful,
            failed,
            successCount: successful.length,
            failCount: failed.length,
            totalCount: dates.length
        };
    }

    /**
     * 获取日期范围内的所有日期
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @param {string} format - 输出格式
     */
    getDateRange(startDate, endDate, format = 'mdac') {
        const start = this.parseDate(startDate);
        const end = this.parseDate(endDate);

        if (!start.isValid || !end.isValid) {
            return { 
                isValid: false, 
                error: '开始或结束日期无效',
                startError: start.isValid ? null : start.error,
                endError: end.isValid ? null : end.error
            };
        }

        const startDateObj = new Date(start.year, start.month - 1, start.day);
        const endDateObj = new Date(end.year, end.month - 1, end.day);

        if (startDateObj > endDateObj) {
            return { isValid: false, error: '开始日期不能晚于结束日期' };
        }

        const dates = [];
        const currentDate = new Date(startDateObj);

        while (currentDate <= endDateObj) {
            const dateObj = {
                year: currentDate.getFullYear(),
                month: currentDate.getMonth() + 1,
                day: currentDate.getDate(),
                isValid: true
            };

            if (format === 'iso') {
                const formatted = this.formatToISO(dateObj);
                dates.push(formatted.formatted);
            } else {
                const formatted = this.formatForMDAC(dateObj);
                dates.push(formatted.formatted);
            }

            currentDate.setDate(currentDate.getDate() + 1);
        }

        return {
            isValid: true,
            dates,
            count: dates.length,
            startDate: format === 'iso' ? this.formatToISO(start).formatted : this.formatForMDAC(start).formatted,
            endDate: format === 'iso' ? this.formatToISO(end).formatted : this.formatForMDAC(end).formatted
        };
    }

    /**
     * 计算两个日期之间的天数差
     * @param {string} date1 - 第一个日期
     * @param {string} date2 - 第二个日期
     */
    getDaysDifference(date1, date2) {
        const parsed1 = this.parseDate(date1);
        const parsed2 = this.parseDate(date2);

        if (!parsed1.isValid || !parsed2.isValid) {
            return { 
                isValid: false, 
                error: '日期解析失败',
                date1Error: parsed1.isValid ? null : parsed1.error,
                date2Error: parsed2.isValid ? null : parsed2.error
            };
        }

        const dateObj1 = new Date(parsed1.year, parsed1.month - 1, parsed1.day);
        const dateObj2 = new Date(parsed2.year, parsed2.month - 1, parsed2.day);

        const timeDiff = Math.abs(dateObj2.getTime() - dateObj1.getTime());
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        return {
            isValid: true,
            days: daysDiff,
            date1: this.formatForMDAC(parsed1).formatted,
            date2: this.formatForMDAC(parsed2).formatted,
            isDate1Earlier: dateObj1 < dateObj2
        };
    }

    /**
     * 获取支持的日期格式示例
     */
    getSupportedFormats() {
        return [
            { format: 'YYYY-MM-DD', example: '2024-01-15', description: 'ISO标准格式' },
            { format: 'DD/MM/YYYY', example: '15/01/2024', description: 'MDAC标准格式' },
            { format: 'DD-MM-YYYY', example: '15-01-2024', description: '短横线分隔' },
            { format: 'YYYY/MM/DD', example: '2024/01/15', description: '年月日斜线' },
            { format: 'YYYY年MM月DD日', example: '2024年1月15日', description: '中文格式' },
            { format: 'MM月DD日', example: '1月15日', description: '中文格式(当年)' },
            { format: 'DD Mon YYYY', example: '15 Jan 2024', description: '英文格式' },
            { format: 'Mon DD, YYYY', example: 'Jan 15, 2024', description: '英文格式带逗号' },
            { format: 'DD.MM.YYYY', example: '15.01.2024', description: '点号分隔' },
            { format: 'YYYY.MM.DD', example: '2024.01.15', description: '年月日点号' }
        ];
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DateFormatter;
} else {
    window.DateFormatter = DateFormatter;
}

console.log('✅ [DateFormatter] 日期格式化工具已加载');
