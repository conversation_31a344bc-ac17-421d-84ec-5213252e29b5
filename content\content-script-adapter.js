/**
 * Content Script 适配器
 * 为content-script.js提供与新模块化架构的兼容性
 * 创建日期: 2025-01-11
 */

// 创建兼容性垫片，确保旧的模块类在content-script中可用
(function() {
    'use strict';

    console.log('🔄 [ContentScriptAdapter] 初始化Content Script兼容性适配器');

    // 检查是否已经加载过适配器
    if (window.MDAC_CONTENT_ADAPTER_LOADED) {
        console.log('⚠️ [ContentScriptAdapter] 适配器已加载，跳过重复初始化');
        return;
    }

    // 标记适配器已加载
    window.MDAC_CONTENT_ADAPTER_LOADED = true;

    /**
     * MDACLogger 兼容性类
     * 提供与旧版本相同的API，但使用新的调试日志系统
     */
    class MDACLogger {
        constructor() {
            this.logs = [];
            this.maxLogs = 1000;
            this.isEnabled = true;
            
            console.log('📝 [MDACLogger] 兼容性日志器已初始化');
        }

        log(level, module, message, data = null) {
            if (!this.isEnabled) return;

            const logEntry = {
                timestamp: Date.now(),
                level: level,
                module: module,
                message: message,
                data: data
            };

            this.logs.push(logEntry);
            
            // 限制日志数量
            if (this.logs.length > this.maxLogs) {
                this.logs.shift();
            }

            // 输出到控制台
            this.outputToConsole(logEntry);

            // 尝试发送到sidepanel的调试日志器
            this.sendToSidePanel(logEntry);
        }

        debug(module, message, data = null) {
            this.log('DEBUG', module, message, data);
        }

        info(module, message, data = null) {
            this.log('INFO', module, message, data);
        }

        warn(module, message, data = null) {
            this.log('WARN', module, message, data);
        }

        error(module, message, data = null) {
            this.log('ERROR', module, message, data);
        }

        outputToConsole(logEntry) {
            const { level, module, message, data } = logEntry;
            const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
            const logMessage = `[${timestamp}] [${level}] ${module}: ${message}`;

            switch (level) {
                case 'DEBUG':
                    console.debug(logMessage, data);
                    break;
                case 'INFO':
                    console.info(logMessage, data);
                    break;
                case 'WARN':
                    console.warn(logMessage, data);
                    break;
                case 'ERROR':
                    console.error(logMessage, data);
                    break;
                default:
                    console.log(logMessage, data);
            }
        }

        sendToSidePanel(logEntry) {
            try {
                // 尝试通过Chrome消息API发送到sidepanel
                chrome.runtime.sendMessage({
                    action: 'content-log',
                    type: 'debug-log',
                    data: logEntry
                }).catch(error => {
                    // 静默处理错误，避免循环日志
                });
            } catch (error) {
                // 静默处理错误
            }
        }

        getLogs(filter = {}) {
            let filteredLogs = this.logs;

            if (filter.level) {
                filteredLogs = filteredLogs.filter(log => log.level === filter.level);
            }

            if (filter.module) {
                filteredLogs = filteredLogs.filter(log => log.module === filter.module);
            }

            if (filter.limit) {
                filteredLogs = filteredLogs.slice(-filter.limit);
            }

            return filteredLogs;
        }

        clearLogs() {
            this.logs = [];
        }

        enable() {
            this.isEnabled = true;
        }

        disable() {
            this.isEnabled = false;
        }
    }

    /**
     * MDACDebugConsole 兼容性类
     */
    class MDACDebugConsole {
        constructor() {
            this.isVisible = false;
            this.element = null;
            
            console.log('🖥️ [MDACDebugConsole] 兼容性调试控制台已初始化');
        }

        show() {
            console.log('🖥️ [MDACDebugConsole] 显示调试控制台（兼容性模式）');
            this.isVisible = true;
            
            // 发送消息到sidepanel请求显示调试面板
            try {
                chrome.runtime.sendMessage({
                    action: 'show-debug-panel',
                    source: 'content-script'
                });
            } catch (error) {
                console.warn('⚠️ [MDACDebugConsole] 无法发送显示调试面板消息', error);
            }
        }

        hide() {
            console.log('🖥️ [MDACDebugConsole] 隐藏调试控制台（兼容性模式）');
            this.isVisible = false;
            
            // 发送消息到sidepanel请求隐藏调试面板
            try {
                chrome.runtime.sendMessage({
                    action: 'hide-debug-panel',
                    source: 'content-script'
                });
            } catch (error) {
                console.warn('⚠️ [MDACDebugConsole] 无法发送隐藏调试面板消息', error);
            }
        }

        toggle() {
            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        }

        log(message, data = null) {
            if (window.mdacLogger) {
                window.mdacLogger.info('DebugConsole', message, data);
            } else {
                console.log('[DebugConsole]', message, data);
            }
        }
    }

    /**
     * 简化的FormFieldDetector兼容性类
     */
    class FormFieldDetector {
        constructor() {
            this.detectedFields = {};
            this.isInitialized = false;
            
            console.log('🔍 [FormFieldDetector] 兼容性字段检测器已初始化');
            this.initialize();
        }

        async initialize() {
            try {
                // 基础初始化
                this.isInitialized = true;
                console.log('✅ [FormFieldDetector] 初始化完成');
            } catch (error) {
                console.error('❌ [FormFieldDetector] 初始化失败', error);
            }
        }

        async detectFields() {
            console.log('🔍 [FormFieldDetector] 开始检测表单字段（兼容性模式）');
            
            // 基础字段检测逻辑
            const fields = {};
            const inputs = document.querySelectorAll('input, select, textarea');
            
            inputs.forEach((input, index) => {
                const fieldInfo = {
                    element: input,
                    type: input.type || input.tagName.toLowerCase(),
                    name: input.name || input.id || `field_${index}`,
                    id: input.id,
                    placeholder: input.placeholder,
                    label: this.getFieldLabel(input)
                };
                
                fields[fieldInfo.name] = fieldInfo;
            });

            this.detectedFields = fields;
            console.log(`✅ [FormFieldDetector] 检测到 ${Object.keys(fields).length} 个字段`);
            
            return fields;
        }

        getFieldLabel(input) {
            // 尝试找到关联的label
            if (input.id) {
                const label = document.querySelector(`label[for="${input.id}"]`);
                if (label) return label.textContent.trim();
            }

            // 查找父级元素中的label
            let parent = input.parentElement;
            while (parent && parent !== document.body) {
                const label = parent.querySelector('label');
                if (label) return label.textContent.trim();
                parent = parent.parentElement;
            }

            return input.placeholder || input.name || input.id || '';
        }

        // 添加content-script.js期望的detectFormFields方法
        async detectFormFields() {
            console.log('🔍 [FormFieldDetector] detectFormFields方法被调用（兼容性适配）');
            return await this.detectFields();
        }

        // 添加content-script.js期望的validateDetection方法
        validateDetection(detectedFields) {
            console.log('✅ [FormFieldDetector] validateDetection方法被调用（兼容性适配）');

            const validation = {
                isValid: true,
                fieldCount: Object.keys(detectedFields).length,
                warnings: [],
                errors: []
            };

            // 基础验证逻辑
            if (validation.fieldCount === 0) {
                validation.isValid = false;
                validation.errors.push('未检测到任何表单字段');
            }

            return validation;
        }

        getDetectedFields() {
            return this.detectedFields;
        }
    }

    /**
     * 简化的ErrorRecoveryManager兼容性类
     */
    class ErrorRecoveryManager {
        constructor() {
            this.errorHistory = [];
            this.maxErrors = 100;
            
            console.log('🛡️ [ErrorRecoveryManager] 兼容性错误恢复管理器已初始化');
        }

        async loadErrorHistory() {
            try {
                // 从localStorage加载错误历史
                const stored = localStorage.getItem('mdac_error_history');
                if (stored) {
                    this.errorHistory = JSON.parse(stored);
                }
                console.log('✅ [ErrorRecoveryManager] 错误历史加载完成');
            } catch (error) {
                console.warn('⚠️ [ErrorRecoveryManager] 错误历史加载失败', error);
            }
        }

        recordError(error, context = {}) {
            const errorRecord = {
                timestamp: Date.now(),
                message: error.message || String(error),
                stack: error.stack,
                context: context,
                url: window.location.href
            };

            this.errorHistory.push(errorRecord);
            
            // 限制错误历史大小
            if (this.errorHistory.length > this.maxErrors) {
                this.errorHistory.shift();
            }

            // 保存到localStorage
            try {
                localStorage.setItem('mdac_error_history', JSON.stringify(this.errorHistory));
            } catch (e) {
                console.warn('⚠️ [ErrorRecoveryManager] 无法保存错误历史', e);
            }

            // 发送到sidepanel
            try {
                chrome.runtime.sendMessage({
                    action: 'error-report',
                    data: errorRecord
                });
            } catch (e) {
                // 静默处理
            }
        }

        getErrorHistory() {
            return this.errorHistory;
        }

        clearErrorHistory() {
            this.errorHistory = [];
            localStorage.removeItem('mdac_error_history');
        }
    }

    /**
     * 简化的FillMonitor兼容性类
     */
    class FillMonitor {
        constructor() {
            this.fillHistory = [];
            this.isMonitoring = false;
            
            console.log('📊 [FillMonitor] 兼容性填充监控器已初始化');
        }

        startMonitoring() {
            this.isMonitoring = true;
            console.log('📊 [FillMonitor] 开始监控填充操作');
        }

        stopMonitoring() {
            this.isMonitoring = false;
            console.log('📊 [FillMonitor] 停止监控填充操作');
        }

        recordFill(fieldName, value, success = true) {
            if (!this.isMonitoring) return;

            const fillRecord = {
                timestamp: Date.now(),
                fieldName: fieldName,
                value: value ? '***' : '', // 不记录实际值，只记录是否有值
                success: success,
                url: window.location.href
            };

            this.fillHistory.push(fillRecord);
            
            // 发送到sidepanel
            try {
                chrome.runtime.sendMessage({
                    action: 'fill-record',
                    data: fillRecord
                });
            } catch (e) {
                // 静默处理
            }
        }

        getFillHistory() {
            return this.fillHistory;
        }

        clearFillHistory() {
            this.fillHistory = [];
        }
    }

    /**
     * 简化的ProgressVisualizer兼容性类
     */
    class ProgressVisualizer {
        constructor() {
            this.currentProgress = 0;
            this.isVisible = false;
            
            console.log('📈 [ProgressVisualizer] 兼容性进度可视化器已初始化');
        }

        show(options = {}) {
            this.isVisible = true;
            console.log('📈 [ProgressVisualizer] 显示进度（兼容性模式）');
            
            // 发送到sidepanel
            try {
                chrome.runtime.sendMessage({
                    action: 'show-progress',
                    data: { ...options, progress: this.currentProgress }
                });
            } catch (e) {
                // 静默处理
            }
        }

        hide() {
            this.isVisible = false;
            console.log('📈 [ProgressVisualizer] 隐藏进度（兼容性模式）');
            
            // 发送到sidepanel
            try {
                chrome.runtime.sendMessage({
                    action: 'hide-progress'
                });
            } catch (e) {
                // 静默处理
            }
        }

        updateProgress(progress, message = '') {
            this.currentProgress = progress;
            console.log(`📈 [ProgressVisualizer] 更新进度: ${progress}% - ${message}`);
            
            // 发送到sidepanel
            try {
                chrome.runtime.sendMessage({
                    action: 'update-progress',
                    data: { progress, message }
                });
            } catch (e) {
                // 静默处理
            }
        }

        setProgress(progress) {
            this.updateProgress(progress);
        }
    }

    // 将兼容性类暴露到全局作用域
    window.MDACLogger = MDACLogger;
    window.MDACDebugConsole = MDACDebugConsole;
    window.FormFieldDetector = FormFieldDetector;
    window.ErrorRecoveryManager = ErrorRecoveryManager;
    window.FillMonitor = FillMonitor;
    window.ProgressVisualizer = ProgressVisualizer;

    // 创建全局实例（如果需要）
    if (!window.mdacLogger) {
        window.mdacLogger = new MDACLogger();
    }

    console.log('✅ [ContentScriptAdapter] Content Script兼容性适配器初始化完成');

})();
