/**
 * 遗留代码兼容性适配器
 * 确保旧版本代码能够与新模块化架构兼容
 * 创建日期: 2025-01-11
 */

class LegacyAdapter {
    constructor(modularSidePanel) {
        this.modularSidePanel = modularSidePanel;
        this.legacyInstances = new Map();
        
        console.log('🔄 [LegacyAdapter] 初始化遗留代码兼容性适配器');
        
        // 初始化兼容性映射
        this.initializeCompatibilityMappings();
        
        // 设置全局兼容性对象
        this.setupGlobalCompatibility();
    }

    /**
     * 初始化兼容性映射
     */
    initializeCompatibilityMappings() {
        // 模块映射关系
        this.moduleMapping = {
            // 旧模块名 -> 新模块路径
            'Logger': 'modules.debugLogger',
            'DebugConsole': 'modules.debugLogger',
            'ConfidenceEvaluator': 'modules.confidenceEvaluator',
            'ProgressVisualizer': 'modules.progressVisualizer',
            'DataPreviewManager': 'modules.previewManager',
            'ErrorRecoveryManager': 'modules.debugLogger', // 错误恢复功能集成到调试日志器
            'FillMonitor': 'modules.formFiller', // 填充监控集成到表单填充器
            'FormValidator': 'modules.dataValidator',
            'DateFormatter': 'modules.dateFormatter',
            'EnhancedFormFiller': 'modules.formFiller',
            'MDACValidator': 'modules.dataValidator'
        };

        // API映射关系
        this.apiMapping = {
            // 旧API -> 新API
            'log': 'debugLogger.log',
            'logError': 'debugLogger.error',
            'logInfo': 'debugLogger.info',
            'logDebug': 'debugLogger.debug',
            'showProgress': 'progressVisualizer.show',
            'hideProgress': 'progressVisualizer.hide',
            'updateProgress': 'progressVisualizer.update',
            'evaluateConfidence': 'confidenceEvaluator.evaluateConfidence',
            'validateData': 'dataValidator.validateData',
            'fillForm': 'formFiller.fillForm',
            'formatDate': 'dateFormatter.formatDate'
        };

        // 事件映射关系
        this.eventMapping = {
            // 旧事件名 -> 新事件名
            'log-message': 'debug:log',
            'progress-update': 'progress:update',
            'confidence-evaluated': 'confidence:evaluation-complete',
            'form-filled': 'form:fill-complete',
            'data-validated': 'data:validation-complete',
            'error-occurred': 'error:occurred'
        };
    }

    /**
     * 设置全局兼容性对象
     */
    setupGlobalCompatibility() {
        // 为旧代码提供全局访问点
        window.MDAC_LEGACY = {
            // 模块实例访问
            getModule: (moduleName) => this.getModuleInstance(moduleName),
            
            // API调用
            callAPI: (apiName, ...args) => this.callAPI(apiName, ...args),
            
            // 事件发布
            emit: (eventName, data) => this.emitEvent(eventName, data),
            
            // 事件监听
            on: (eventName, handler) => this.addEventListener(eventName, handler),
            
            // 兼容性检查
            isCompatible: (feature) => this.checkCompatibility(feature)
        };

        // 创建旧模块的兼容性实例
        this.createLegacyInstances();
    }

    /**
     * 创建旧模块的兼容性实例
     */
    createLegacyInstances() {
        // Logger兼容性实例
        window.Logger = {
            log: (...args) => this.callAPI('log', 'INFO', ...args),
            error: (...args) => this.callAPI('logError', ...args),
            info: (...args) => this.callAPI('logInfo', ...args),
            debug: (...args) => this.callAPI('logDebug', ...args),
            warn: (...args) => this.callAPI('log', 'WARN', ...args)
        };

        // DebugConsole兼容性实例
        window.DebugConsole = {
            show: () => this.getModuleInstance('DebugConsole')?.show?.(),
            hide: () => this.getModuleInstance('DebugConsole')?.hide?.(),
            toggle: () => this.getModuleInstance('DebugConsole')?.toggle?.(),
            log: (...args) => window.Logger.log(...args)
        };

        // ConfidenceEvaluator兼容性实例 - 使用不同的名称避免冲突
        window.LegacyConfidenceEvaluator = {
            evaluate: (data, options) => this.callAPI('evaluateConfidence', data, options),
            getLastEvaluation: () => this.getModuleInstance('ConfidenceEvaluator')?.getLastEvaluation?.()
        };

        // ProgressVisualizer兼容性实例 - 使用不同的名称避免冲突
        window.LegacyProgressVisualizer = {
            show: (options) => this.callAPI('showProgress', options),
            hide: () => this.callAPI('hideProgress'),
            update: (progress, message) => this.callAPI('updateProgress', progress, message),
            setProgress: (progress) => this.callAPI('updateProgress', progress)
        };

        // DataPreviewManager兼容性实例
        window.DataPreviewManager = {
            show: (data, options) => this.getModuleInstance('DataPreviewManager')?.show?.(data, options),
            hide: () => this.getModuleInstance('DataPreviewManager')?.hide?.(),
            export: (format) => this.getModuleInstance('DataPreviewManager')?.export?.(format)
        };

        // FormValidator兼容性实例
        window.FormValidator = {
            validate: (data) => this.callAPI('validateData', data),
            validateField: (field, value) => this.getModuleInstance('FormValidator')?.validateField?.(field, value)
        };

        // DateFormatter兼容性实例 - 使用不同的名称避免冲突
        window.LegacyDateFormatter = {
            format: (date, format) => this.callAPI('formatDate', date, format),
            parse: (dateString) => this.getModuleInstance('DateFormatter')?.parseDate?.(dateString),
            toMDACFormat: (date) => this.callAPI('formatDate', date, 'mdac')
        };

        // EnhancedFormFiller兼容性实例
        window.EnhancedFormFiller = {
            fill: (data, options) => this.callAPI('fillForm', data, options),
            fillField: (field, value) => this.getModuleInstance('EnhancedFormFiller')?.fillField?.(field, value),
            getStats: () => this.getModuleInstance('EnhancedFormFiller')?.getStats?.()
        };

        // MDACValidator兼容性实例
        window.MDACValidator = {
            validate: (data) => this.callAPI('validateData', data),
            validateField: (field, value) => this.getModuleInstance('MDACValidator')?.validateField?.(field, value),
            getCodeMappings: () => this.getModuleInstance('MDACValidator')?.getCodeMappings?.()
        };
    }

    /**
     * 获取模块实例
     * @param {string} moduleName - 模块名称
     */
    getModuleInstance(moduleName) {
        const modulePath = this.moduleMapping[moduleName];
        if (!modulePath) {
            console.warn(`⚠️ [LegacyAdapter] 未找到模块映射: ${moduleName}`);
            return null;
        }

        // 解析模块路径
        const pathParts = modulePath.split('.');
        let moduleInstance = this.modularSidePanel;

        for (const part of pathParts) {
            if (moduleInstance && moduleInstance[part]) {
                moduleInstance = moduleInstance[part];
            } else {
                console.warn(`⚠️ [LegacyAdapter] 模块路径无效: ${modulePath}`);
                return null;
            }
        }

        return moduleInstance;
    }

    /**
     * 调用API
     * @param {string} apiName - API名称
     * @param {...any} args - 参数
     */
    callAPI(apiName, ...args) {
        const apiPath = this.apiMapping[apiName];
        if (!apiPath) {
            console.warn(`⚠️ [LegacyAdapter] 未找到API映射: ${apiName}`);
            return null;
        }

        // 解析API路径
        const pathParts = apiPath.split('.');
        const moduleName = pathParts[0];
        const methodName = pathParts[1];

        const moduleInstance = this.getModuleInstance(moduleName);
        if (!moduleInstance || typeof moduleInstance[methodName] !== 'function') {
            console.warn(`⚠️ [LegacyAdapter] API方法不存在: ${apiPath}`);
            return null;
        }

        try {
            return moduleInstance[methodName](...args);
        } catch (error) {
            console.error(`❌ [LegacyAdapter] API调用失败: ${apiPath}`, error);
            return null;
        }
    }

    /**
     * 发布事件
     * @param {string} eventName - 事件名称
     * @param {any} data - 事件数据
     */
    emitEvent(eventName, data) {
        const newEventName = this.eventMapping[eventName] || eventName;
        
        if (this.modularSidePanel.modules.eventManager) {
            this.modularSidePanel.modules.eventManager.emit(newEventName, data);
        } else {
            console.warn(`⚠️ [LegacyAdapter] 事件管理器不可用: ${newEventName}`);
        }
    }

    /**
     * 添加事件监听器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 处理函数
     */
    addEventListener(eventName, handler) {
        const newEventName = this.eventMapping[eventName] || eventName;
        
        if (this.modularSidePanel.modules.eventManager) {
            return this.modularSidePanel.modules.eventManager.on(newEventName, handler);
        } else {
            console.warn(`⚠️ [LegacyAdapter] 事件管理器不可用: ${newEventName}`);
            return () => {}; // 返回空的移除函数
        }
    }

    /**
     * 检查兼容性
     * @param {string} feature - 功能名称
     */
    checkCompatibility(feature) {
        const compatibilityMap = {
            'Logger': !!this.getModuleInstance('Logger'),
            'DebugConsole': !!this.getModuleInstance('DebugConsole'),
            'ConfidenceEvaluator': !!this.getModuleInstance('ConfidenceEvaluator'),
            'ProgressVisualizer': !!this.getModuleInstance('ProgressVisualizer'),
            'DataPreviewManager': !!this.getModuleInstance('DataPreviewManager'),
            'FormValidator': !!this.getModuleInstance('FormValidator'),
            'DateFormatter': !!this.getModuleInstance('DateFormatter'),
            'EnhancedFormFiller': !!this.getModuleInstance('EnhancedFormFiller'),
            'MDACValidator': !!this.getModuleInstance('MDACValidator')
        };

        return compatibilityMap[feature] !== undefined ? compatibilityMap[feature] : false;
    }

    /**
     * 获取兼容性报告
     */
    getCompatibilityReport() {
        const features = [
            'Logger', 'DebugConsole', 'ConfidenceEvaluator', 'ProgressVisualizer',
            'DataPreviewManager', 'FormValidator', 'DateFormatter', 
            'EnhancedFormFiller', 'MDACValidator'
        ];

        const report = {
            compatible: [],
            incompatible: [],
            total: features.length,
            compatibilityRate: 0
        };

        features.forEach(feature => {
            if (this.checkCompatibility(feature)) {
                report.compatible.push(feature);
            } else {
                report.incompatible.push(feature);
            }
        });

        report.compatibilityRate = report.compatible.length / report.total;

        return report;
    }

    /**
     * 迁移旧代码到新架构
     * @param {string} oldCode - 旧代码
     */
    migrateCode(oldCode) {
        let migratedCode = oldCode;

        // 替换旧的模块调用
        Object.entries(this.moduleMapping).forEach(([oldModule, newModule]) => {
            const regex = new RegExp(`\\b${oldModule}\\b`, 'g');
            migratedCode = migratedCode.replace(regex, `MDAC_LEGACY.getModule('${oldModule}')`);
        });

        // 替换旧的API调用
        Object.entries(this.apiMapping).forEach(([oldAPI, newAPI]) => {
            const regex = new RegExp(`\\b${oldAPI}\\(`, 'g');
            migratedCode = migratedCode.replace(regex, `MDAC_LEGACY.callAPI('${oldAPI}', `);
        });

        // 替换旧的事件调用
        Object.entries(this.eventMapping).forEach(([oldEvent, newEvent]) => {
            const regex = new RegExp(`'${oldEvent}'`, 'g');
            migratedCode = migratedCode.replace(regex, `'${newEvent}'`);
        });

        return migratedCode;
    }

    /**
     * 销毁适配器
     */
    destroy() {
        // 清理全局对象
        delete window.MDAC_LEGACY;
        delete window.Logger;
        delete window.DebugConsole;
        delete window.ConfidenceEvaluator;
        delete window.ProgressVisualizer;
        delete window.DataPreviewManager;
        delete window.FormValidator;
        delete window.DateFormatter;
        delete window.EnhancedFormFiller;
        delete window.MDACValidator;

        // 清理实例
        this.legacyInstances.clear();

        console.log('🗑️ [LegacyAdapter] 兼容性适配器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LegacyAdapter;
} else {
    window.LegacyAdapter = LegacyAdapter;
}

console.log('✅ [LegacyAdapter] 遗留代码兼容性适配器已加载');
