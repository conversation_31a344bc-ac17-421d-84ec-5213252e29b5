/**
 * 状态管理器 - 管理侧边栏的全局状态
 * 负责状态的存储、更新和通知
 * 创建日期: 2025-01-11
 * 修复日期: 2025-07-12
 */

// 防止重复声明的新方法 - 使用简单检查
(function() {
    'use strict';

    // 如果StateManager已存在且功能正常，则跳过
    if (typeof window.StateManager === 'function') {
        console.log('✅ [StateManager] 已存在且正常，跳过重新定义');
        return;
    }

    class StateManager {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 初始状态
        this.state = {
            // UI状态
            ui: {
                isVisible: false,
                currentTab: 'input',
                isLoading: false,
                loadingMessage: '',
                showDebugConsole: false
            },
            
            // 数据状态
            data: {
                inputText: '',
                inputImage: null,
                parsedData: null,
                confidence: {},
                validationErrors: []
            },
            
            // AI状态
            ai: {
                isProcessing: false,
                lastResponse: null,
                apiUsage: {
                    requestCount: 0,
                    lastRequestTime: null
                }
            },
            
            // 表单状态
            form: {
                isAutoFilling: false,
                fillProgress: 0,
                lastFillResult: null,
                detectedFields: {}
            },
            
            // 设置状态
            settings: {
                autoParseEnabled: false,
                debugMode: false,
                autoFillDelay: 500,
                confidenceThreshold: 0.7
            },
            
            // 错误状态
            errors: {
                lastError: null,
                errorHistory: []
            }
        };

        // 状态变更监听器
        this.listeners = new Map();
        
        // 状态变更历史（用于调试）
        this.stateHistory = [];
        this.maxHistorySize = 50;

        console.log('🗂️ [StateManager] 状态管理器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (this.eventBus) {
            // 监听状态重置事件
            this.eventBus.on('state:reset', () => this.reset());
            
            // 监听状态导出事件
            this.eventBus.on('state:export', () => this.exportState());
            
            // 监听状态导入事件
            this.eventBus.on('state:import', (data) => this.importState(data));
        }
    }

    /**
     * 获取完整状态
     */
    getState() {
        return JSON.parse(JSON.stringify(this.state)); // 深拷贝
    }

    /**
     * 获取状态的某个部分
     * @param {string} path - 状态路径，如 'ui.currentTab'
     */
    get(path) {
        const keys = path.split('.');
        let current = this.state;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        
        return current;
    }

    /**
     * 设置状态
     * @param {string} path - 状态路径
     * @param {*} value - 新值
     * @param {Object} options - 选项
     */
    set(path, value, options = {}) {
        const { silent = false, merge = false } = options;
        
        // 记录变更前的状态
        const oldValue = this.get(path);
        
        // 更新状态
        const keys = path.split('.');
        let current = this.state;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        const lastKey = keys[keys.length - 1];
        
        if (merge && typeof value === 'object' && typeof current[lastKey] === 'object') {
            current[lastKey] = { ...current[lastKey], ...value };
        } else {
            current[lastKey] = value;
        }

        // 记录状态变更历史
        this.recordStateChange(path, oldValue, value);

        // 发布状态变更事件
        if (!silent && this.eventBus) {
            this.eventBus.emit('state:changed', {
                path,
                oldValue,
                newValue: value,
                timestamp: Date.now()
            });

            // 发布特定路径的变更事件
            this.eventBus.emit(`state:changed:${path}`, {
                oldValue,
                newValue: value,
                timestamp: Date.now()
            });
        }

        // 调用监听器
        this.notifyListeners(path, value, oldValue);

        console.log(`🔄 [StateManager] 状态已更新: ${path}`, { oldValue, newValue: value });
    }

    /**
     * 批量更新状态
     * @param {Object} updates - 更新对象，键为路径，值为新值
     * @param {Object} options - 选项
     */
    batchUpdate(updates, options = {}) {
        const { silent = false } = options;
        
        console.log('🔄 [StateManager] 批量更新状态', updates);
        
        const changes = [];
        
        for (const [path, value] of Object.entries(updates)) {
            const oldValue = this.get(path);
            this.set(path, value, { silent: true });
            changes.push({ path, oldValue, newValue: value });
        }

        // 发布批量变更事件
        if (!silent && this.eventBus) {
            this.eventBus.emit('state:batch-changed', {
                changes,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 添加状态监听器
     * @param {string} path - 监听的状态路径
     * @param {Function} callback - 回调函数
     */
    subscribe(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, []);
        }
        
        this.listeners.get(path).push(callback);
        
        // 返回取消订阅函数
        return () => {
            const listeners = this.listeners.get(path);
            if (listeners) {
                const index = listeners.indexOf(callback);
                if (index !== -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    }

    /**
     * 通知监听器
     * @param {string} path - 状态路径
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     */
    notifyListeners(path, newValue, oldValue) {
        // 通知精确路径的监听器
        const exactListeners = this.listeners.get(path);
        if (exactListeners) {
            exactListeners.forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error(`❌ [StateManager] 监听器执行失败: ${path}`, error);
                }
            });
        }

        // 通知父路径的监听器
        const pathParts = path.split('.');
        for (let i = pathParts.length - 1; i > 0; i--) {
            const parentPath = pathParts.slice(0, i).join('.');
            const parentListeners = this.listeners.get(parentPath);
            if (parentListeners) {
                parentListeners.forEach(callback => {
                    try {
                        callback(this.get(parentPath), undefined, parentPath);
                    } catch (error) {
                        console.error(`❌ [StateManager] 父路径监听器执行失败: ${parentPath}`, error);
                    }
                });
            }
        }
    }

    /**
     * 记录状态变更历史
     * @param {string} path - 状态路径
     * @param {*} oldValue - 旧值
     * @param {*} newValue - 新值
     */
    recordStateChange(path, oldValue, newValue) {
        this.stateHistory.push({
            path,
            oldValue,
            newValue,
            timestamp: Date.now()
        });

        // 限制历史记录大小
        if (this.stateHistory.length > this.maxHistorySize) {
            this.stateHistory.shift();
        }
    }

    /**
     * 重置状态到初始值
     */
    reset() {
        console.log('🔄 [StateManager] 重置状态');
        
        const oldState = this.getState();
        
        // 重置到初始状态（保留设置）
        const settings = this.state.settings;
        this.state = {
            ui: {
                isVisible: false,
                currentTab: 'input',
                isLoading: false,
                loadingMessage: '',
                showDebugConsole: false
            },
            data: {
                inputText: '',
                inputImage: null,
                parsedData: null,
                confidence: {},
                validationErrors: []
            },
            ai: {
                isProcessing: false,
                lastResponse: null,
                apiUsage: {
                    requestCount: 0,
                    lastRequestTime: null
                }
            },
            form: {
                isAutoFilling: false,
                fillProgress: 0,
                lastFillResult: null,
                detectedFields: {}
            },
            settings, // 保留设置
            errors: {
                lastError: null,
                errorHistory: []
            }
        };

        // 发布重置事件
        if (this.eventBus) {
            this.eventBus.emit('state:reset-complete', {
                oldState,
                newState: this.getState(),
                timestamp: Date.now()
            });
        }
    }

    /**
     * 导出状态
     */
    exportState() {
        const exportData = {
            state: this.getState(),
            history: this.stateHistory,
            timestamp: Date.now(),
            version: '1.0.0'
        };

        console.log('📤 [StateManager] 导出状态', exportData);
        return exportData;
    }

    /**
     * 导入状态
     * @param {Object} data - 导入的状态数据
     */
    importState(data) {
        try {
            if (data && data.state) {
                console.log('📥 [StateManager] 导入状态', data);
                
                const oldState = this.getState();
                this.state = { ...data.state };
                
                if (data.history) {
                    this.stateHistory = data.history;
                }

                // 发布导入完成事件
                if (this.eventBus) {
                    this.eventBus.emit('state:import-complete', {
                        oldState,
                        newState: this.getState(),
                        timestamp: Date.now()
                    });
                }
            }
        } catch (error) {
            console.error('❌ [StateManager] 导入状态失败', error);
            throw error;
        }
    }

    /**
     * 获取状态变更历史
     */
    getHistory() {
        return [...this.stateHistory];
    }

    /**
     * 清理资源
     */
    destroy() {
        this.listeners.clear();
        this.stateHistory = [];
        
        if (this.eventBus) {
            this.eventBus.off('state:reset');
            this.eventBus.off('state:export');
            this.eventBus.off('state:import');
        }
        
        console.log('🗑️ [StateManager] 状态管理器已销毁');
    }
}

} // 结束重复声明保护

    // 立即注册到全局对象
    window.StateManager = StateManager;
    console.log('✅ [StateManager] 类已注册到全局对象');

    // 导出类和实例 - 兼容 ultimate-bootstrap 属性保护系统
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = StateManager;
    }

    console.log('✅ [StateManager] 状态管理器模块已加载完成');

})();
