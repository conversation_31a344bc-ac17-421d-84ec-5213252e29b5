/**
 * 模态框管理器 - 统一的模态框和对话框管理
 * 负责创建、显示、隐藏和管理各种模态框组件
 * 创建日期: 2025-01-11
 */

class ModalManager {
    constructor(eventBus = window.mdacEventBus, messageHelper = null) {
        this.eventBus = eventBus;
        this.messageHelper = messageHelper;
        
        // 模态框容器
        this.modalContainer = null;
        
        // 活跃的模态框
        this.activeModals = new Map();
        
        // 模态框配置
        this.config = {
            zIndexBase: 10000,
            animationDuration: 300,
            closeOnOverlayClick: true,
            closeOnEscape: true,
            stackable: true
        };

        // 模态框模板
        this.modalTemplates = {
            confirm: this.createConfirmTemplate.bind(this),
            alert: this.createAlertTemplate.bind(this),
            prompt: this.createPromptTemplate.bind(this),
            custom: this.createCustomTemplate.bind(this),
            loading: this.createLoadingTemplate.bind(this),
            preview: this.createPreviewTemplate.bind(this)
        };

        console.log('🪟 [ModalManager] 模态框管理器已初始化');
        this.initializeContainer();
        this.initializeEventListeners();
    }

    /**
     * 初始化模态框容器
     */
    initializeContainer() {
        // 检查是否已存在容器
        this.modalContainer = document.getElementById('mdac-modal-container');
        
        if (!this.modalContainer) {
            this.modalContainer = document.createElement('div');
            this.modalContainer.id = 'mdac-modal-container';
            this.modalContainer.className = 'modal-container';
            
            // 添加样式
            this.modalContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: ${this.config.zIndexBase};
            `;
            
            document.body.appendChild(this.modalContainer);
        }

        // 注入CSS样式
        this.injectStyles();
    }

    /**
     * 注入CSS样式
     */
    injectStyles() {
        const styleId = 'mdac-modal-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: auto;
            }
            
            .modal-overlay.show {
                opacity: 1;
            }
            
            .modal-content {
                background: white;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                max-width: 90vw;
                max-height: 90vh;
                overflow: auto;
                transform: scale(0.9) translateY(-20px);
                transition: transform 0.3s ease;
                position: relative;
            }
            
            .modal-overlay.show .modal-content {
                transform: scale(1) translateY(0);
            }
            
            .modal-header {
                padding: 20px 24px 16px;
                border-bottom: 1px solid #e5e5e5;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            
            .modal-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
                padding: 0;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: background-color 0.2s;
            }
            
            .modal-close:hover {
                background-color: #f5f5f5;
                color: #333;
            }
            
            .modal-body {
                padding: 20px 24px;
            }
            
            .modal-footer {
                padding: 16px 24px 20px;
                border-top: 1px solid #e5e5e5;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }
            
            .modal-button {
                padding: 8px 16px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s;
            }
            
            .modal-button.primary {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }
            
            .modal-button.danger {
                background: #dc3545;
                color: white;
                border-color: #dc3545;
            }
            
            .modal-button:hover {
                opacity: 0.9;
                transform: translateY(-1px);
            }
            
            .modal-input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-top: 8px;
            }
            
            .modal-loading {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 20px;
            }
            
            .modal-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .modal-preview {
                max-width: 800px;
                width: 90vw;
            }
            
            .modal-preview-content {
                max-height: 60vh;
                overflow: auto;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 全局键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.config.closeOnEscape) {
                this.closeTopModal();
            }
        });

        // 事件总线监听
        if (this.eventBus) {
            this.eventBus.on('modal:show', (data) => {
                this.showModal(data.type, data.options);
            });

            this.eventBus.on('modal:close', (data) => {
                this.closeModal(data.modalId);
            });

            this.eventBus.on('modal:close-all', () => {
                this.closeAllModals();
            });
        }
    }

    /**
     * 显示模态框
     * @param {string} type - 模态框类型
     * @param {Object} options - 选项
     */
    showModal(type, options = {}) {
        try {
            console.log(`🪟 [ModalManager] 显示模态框: ${type}`, options);

            const modalId = this.generateModalId();
            const template = this.modalTemplates[type];
            
            if (!template) {
                throw new Error(`未知的模态框类型: ${type}`);
            }

            // 创建模态框元素
            const modalElement = this.createModalElement(modalId, template, options);
            
            // 添加到容器
            this.modalContainer.appendChild(modalElement);
            
            // 设置z-index
            const zIndex = this.config.zIndexBase + this.activeModals.size;
            modalElement.style.zIndex = zIndex;
            
            // 创建模态框对象
            const modal = {
                id: modalId,
                type: type,
                element: modalElement,
                options: options,
                zIndex: zIndex,
                createdAt: Date.now(),
                promise: null,
                resolve: null,
                reject: null
            };

            // 如果需要Promise支持
            if (options.promise !== false) {
                modal.promise = new Promise((resolve, reject) => {
                    modal.resolve = resolve;
                    modal.reject = reject;
                });
            }

            // 注册模态框
            this.activeModals.set(modalId, modal);

            // 显示动画
            requestAnimationFrame(() => {
                modalElement.classList.add('show');
            });

            // 发布显示事件
            if (this.eventBus) {
                this.eventBus.emit('modal:shown', {
                    modalId: modalId,
                    type: type,
                    timestamp: Date.now()
                });
            }

            return modal.promise || modal;

        } catch (error) {
            console.error('❌ [ModalManager] 模态框显示失败', error);
            throw error;
        }
    }

    /**
     * 创建模态框元素
     * @param {string} modalId - 模态框ID
     * @param {Function} template - 模板函数
     * @param {Object} options - 选项
     */
    createModalElement(modalId, template, options) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.dataset.modalId = modalId;

        // 点击遮罩关闭
        if (this.config.closeOnOverlayClick && options.closeOnOverlayClick !== false) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.closeModal(modalId);
                }
            });
        }

        // 创建内容
        const content = template(options);
        overlay.appendChild(content);

        return overlay;
    }

    /**
     * 确认对话框模板
     * @param {Object} options - 选项
     */
    createConfirmTemplate(options) {
        const {
            title = '确认',
            message = '确定要执行此操作吗？',
            confirmText = '确定',
            cancelText = '取消',
            type = 'warning'
        } = options;

        const content = document.createElement('div');
        content.className = 'modal-content';

        const iconMap = {
            warning: '⚠️',
            danger: '❌',
            info: 'ℹ️',
            success: '✅'
        };

        content.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">${iconMap[type] || '❓'} ${title}</h3>
                <button class="modal-close" data-action="close">×</button>
            </div>
            <div class="modal-body">
                <p>${message}</p>
            </div>
            <div class="modal-footer">
                <button class="modal-button" data-action="cancel">${cancelText}</button>
                <button class="modal-button ${type === 'danger' ? 'danger' : 'primary'}" data-action="confirm">${confirmText}</button>
            </div>
        `;

        // 绑定事件
        this.bindModalEvents(content, options);

        return content;
    }

    /**
     * 警告对话框模板
     * @param {Object} options - 选项
     */
    createAlertTemplate(options) {
        const {
            title = '提示',
            message = '',
            buttonText = '确定',
            type = 'info'
        } = options;

        const content = document.createElement('div');
        content.className = 'modal-content';

        const iconMap = {
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            success: '✅'
        };

        content.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">${iconMap[type] || 'ℹ️'} ${title}</h3>
                <button class="modal-close" data-action="close">×</button>
            </div>
            <div class="modal-body">
                <p>${message}</p>
            </div>
            <div class="modal-footer">
                <button class="modal-button primary" data-action="confirm">${buttonText}</button>
            </div>
        `;

        this.bindModalEvents(content, options);
        return content;
    }

    /**
     * 输入对话框模板
     * @param {Object} options - 选项
     */
    createPromptTemplate(options) {
        const {
            title = '输入',
            message = '请输入内容：',
            placeholder = '',
            defaultValue = '',
            confirmText = '确定',
            cancelText = '取消'
        } = options;

        const content = document.createElement('div');
        content.className = 'modal-content';

        content.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">✏️ ${title}</h3>
                <button class="modal-close" data-action="close">×</button>
            </div>
            <div class="modal-body">
                <p>${message}</p>
                <input type="text" class="modal-input" placeholder="${placeholder}" value="${defaultValue}" data-input="prompt">
            </div>
            <div class="modal-footer">
                <button class="modal-button" data-action="cancel">${cancelText}</button>
                <button class="modal-button primary" data-action="confirm">${confirmText}</button>
            </div>
        `;

        this.bindModalEvents(content, options);
        
        // 自动聚焦输入框
        setTimeout(() => {
            const input = content.querySelector('[data-input="prompt"]');
            if (input) {
                input.focus();
                input.select();
            }
        }, 100);

        return content;
    }

    /**
     * 加载对话框模板
     * @param {Object} options - 选项
     */
    createLoadingTemplate(options) {
        const {
            title = '加载中',
            message = '请稍候...',
            showProgress = false,
            progress = 0
        } = options;

        const content = document.createElement('div');
        content.className = 'modal-content';

        let progressHtml = '';
        if (showProgress) {
            progressHtml = `
                <div style="margin-top: 12px;">
                    <div style="background: #f0f0f0; border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="background: #007bff; height: 100%; width: ${progress}%; transition: width 0.3s ease;" data-progress="bar"></div>
                    </div>
                    <div style="text-align: center; margin-top: 8px; font-size: 12px; color: #666;" data-progress="text">${progress}%</div>
                </div>
            `;
        }

        content.innerHTML = `
            <div class="modal-body modal-loading">
                <div class="modal-spinner"></div>
                <div>
                    <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                    <div style="color: #666; font-size: 14px;">${message}</div>
                    ${progressHtml}
                </div>
            </div>
        `;

        return content;
    }

    /**
     * 自定义模板
     * @param {Object} options - 选项
     */
    createCustomTemplate(options) {
        const {
            title = '',
            content = '',
            width = 'auto',
            height = 'auto',
            showHeader = true,
            showFooter = false,
            buttons = []
        } = options;

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        
        if (width !== 'auto') {
            modalContent.style.width = width;
        }
        if (height !== 'auto') {
            modalContent.style.height = height;
        }

        let html = '';

        if (showHeader) {
            html += `
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="modal-close" data-action="close">×</button>
                </div>
            `;
        }

        html += `<div class="modal-body">${content}</div>`;

        if (showFooter && buttons.length > 0) {
            const buttonHtml = buttons.map(btn => 
                `<button class="modal-button ${btn.class || ''}" data-action="${btn.action || 'custom'}" data-value="${btn.value || ''}">${btn.text}</button>`
            ).join('');
            
            html += `<div class="modal-footer">${buttonHtml}</div>`;
        }

        modalContent.innerHTML = html;
        this.bindModalEvents(modalContent, options);
        
        return modalContent;
    }

    /**
     * 预览模板
     * @param {Object} options - 选项
     */
    createPreviewTemplate(options) {
        const {
            title = '预览',
            content = '',
            type = 'text'
        } = options;

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content modal-preview';

        let previewContent = content;
        if (type === 'json') {
            previewContent = `<pre>${JSON.stringify(content, null, 2)}</pre>`;
        } else if (type === 'html') {
            previewContent = content;
        }

        modalContent.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">👁️ ${title}</h3>
                <button class="modal-close" data-action="close">×</button>
            </div>
            <div class="modal-body">
                <div class="modal-preview-content">${previewContent}</div>
            </div>
            <div class="modal-footer">
                <button class="modal-button primary" data-action="close">关闭</button>
            </div>
        `;

        this.bindModalEvents(modalContent, options);
        return modalContent;
    }

    /**
     * 绑定模态框事件
     * @param {Element} content - 内容元素
     * @param {Object} options - 选项
     */
    bindModalEvents(content, options) {
        content.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (!action) return;

            const modalElement = e.target.closest('.modal-overlay');
            const modalId = modalElement.dataset.modalId;
            const modal = this.activeModals.get(modalId);

            if (!modal) return;

            switch (action) {
                case 'close':
                case 'cancel':
                    this.resolveModal(modal, false);
                    break;
                    
                case 'confirm':
                    let result = true;
                    
                    // 获取输入值
                    const input = content.querySelector('[data-input="prompt"]');
                    if (input) {
                        result = input.value;
                    }
                    
                    this.resolveModal(modal, result);
                    break;
                    
                case 'custom':
                    const value = e.target.dataset.value;
                    this.resolveModal(modal, value);
                    break;
            }
        });
    }

    /**
     * 解析模态框Promise
     * @param {Object} modal - 模态框对象
     * @param {*} result - 结果
     */
    resolveModal(modal, result) {
        if (modal.resolve) {
            modal.resolve(result);
        }
        this.closeModal(modal.id);
    }

    /**
     * 关闭模态框
     * @param {string} modalId - 模态框ID
     */
    closeModal(modalId) {
        const modal = this.activeModals.get(modalId);
        if (!modal) return;

        console.log(`🪟 [ModalManager] 关闭模态框: ${modalId}`);

        // 隐藏动画
        modal.element.classList.remove('show');

        // 延迟移除元素
        setTimeout(() => {
            if (modal.element.parentElement) {
                modal.element.remove();
            }
            this.activeModals.delete(modalId);
        }, this.config.animationDuration);

        // 发布关闭事件
        if (this.eventBus) {
            this.eventBus.emit('modal:closed', {
                modalId: modalId,
                type: modal.type,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 关闭顶层模态框
     */
    closeTopModal() {
        if (this.activeModals.size === 0) return;

        // 找到z-index最高的模态框
        let topModal = null;
        let highestZIndex = 0;

        for (const modal of this.activeModals.values()) {
            if (modal.zIndex > highestZIndex) {
                highestZIndex = modal.zIndex;
                topModal = modal;
            }
        }

        if (topModal) {
            this.closeModal(topModal.id);
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        console.log('🪟 [ModalManager] 关闭所有模态框');
        
        const modalIds = Array.from(this.activeModals.keys());
        modalIds.forEach(modalId => {
            this.closeModal(modalId);
        });
    }

    /**
     * 更新加载进度
     * @param {string} modalId - 模态框ID
     * @param {number} progress - 进度百分比
     * @param {string} message - 消息
     */
    updateLoadingProgress(modalId, progress, message = null) {
        const modal = this.activeModals.get(modalId);
        if (!modal || modal.type !== 'loading') return;

        const progressBar = modal.element.querySelector('[data-progress="bar"]');
        const progressText = modal.element.querySelector('[data-progress="text"]');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${progress}%`;
        }

        if (message) {
            const messageElement = modal.element.querySelector('.modal-loading div:last-child div:last-child');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
    }

    /**
     * 快捷方法：确认对话框
     * @param {string} message - 消息
     * @param {Object} options - 选项
     */
    confirm(message, options = {}) {
        return this.showModal('confirm', { message, ...options });
    }

    /**
     * 快捷方法：警告对话框
     * @param {string} message - 消息
     * @param {Object} options - 选项
     */
    alert(message, options = {}) {
        return this.showModal('alert', { message, ...options });
    }

    /**
     * 快捷方法：输入对话框
     * @param {string} message - 消息
     * @param {Object} options - 选项
     */
    prompt(message, options = {}) {
        return this.showModal('prompt', { message, ...options });
    }

    /**
     * 快捷方法：加载对话框
     * @param {string} message - 消息
     * @param {Object} options - 选项
     */
    loading(message, options = {}) {
        return this.showModal('loading', { message, ...options });
    }

    /**
     * 快捷方法：预览对话框
     * @param {string} content - 内容
     * @param {Object} options - 选项
     */
    preview(content, options = {}) {
        return this.showModal('preview', { content, ...options });
    }

    /**
     * 获取活跃模态框数量
     */
    getActiveModalCount() {
        return this.activeModals.size;
    }

    /**
     * 获取模态框信息
     * @param {string} modalId - 模态框ID
     */
    getModalInfo(modalId) {
        const modal = this.activeModals.get(modalId);
        if (!modal) return null;

        return {
            id: modal.id,
            type: modal.type,
            zIndex: modal.zIndex,
            createdAt: modal.createdAt,
            age: Date.now() - modal.createdAt
        };
    }

    /**
     * 生成模态框ID
     */
    generateModalId() {
        return 'modal_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 销毁模态框管理器
     */
    destroy() {
        // 关闭所有模态框
        this.closeAllModals();
        
        // 移除容器
        if (this.modalContainer && this.modalContainer.parentElement) {
            this.modalContainer.remove();
        }

        // 移除样式
        const styles = document.getElementById('mdac-modal-styles');
        if (styles) {
            styles.remove();
        }

        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('modal:show');
            this.eventBus.off('modal:close');
            this.eventBus.off('modal:close-all');
        }

        console.log('🗑️ [ModalManager] 模态框管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalManager;
} else {
    window.ModalManager = ModalManager;
}

console.log('✅ [ModalManager] 模态框管理器模块已加载');
