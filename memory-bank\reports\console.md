---
type: "manual"
---

<div id="errorsList">
        <!--?lit$507789790$--><!---->
          <div class="item-container">
            <div class="cr-row error-item selected">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="true">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="352">
                  <!--?lit$507789790$-->❌ [ModuleBootstrap] 脚本加载失败: chrome-extension://kjdbgfgomclamokpjmnaeeoblfleignl/sidepanel/core/EventBus.js [object Event]
                </div>
                <div class="cr-icon icon-expand-less">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="352" aria-describedby="352" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="false" class="collapse-opened" style="" opened="">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:345 (script.onerror)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along." is-active="">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="353">
                  <!--?lit$507789790$-->❌ [ModuleBootstrap] 模块加载失败: EventBus Error: 脚本加载失败: chrome-extension://kjdbgfgomclamokpjmnaeeoblfleignl/sidepanel/core/EventBus.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="353" aria-describedby="353" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:300 (loadScript)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="354">
                  <!--?lit$507789790$-->❌ [ModuleBootstrap] 必需模块加载失败: EventBus Error: 脚本加载失败: chrome-extension://kjdbgfgomclamokpjmnaeeoblfleignl/sidepanel/core/EventBus.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="354" aria-describedby="354" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:410 (loadAllModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="355">
                  <!--?lit$507789790$-->❌ [ModuleBootstrap] 模块加载过程失败 Error: 脚本加载失败: chrome-extension://kjdbgfgomclamokpjmnaeeoblfleignl/sidepanel/core/EventBus.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="355" aria-describedby="355" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:439 (loadAllModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="Error">
                </cr-icon>
                <div class="error-message" id="356">
                  <!--?lit$507789790$-->❌ [ModuleBootstrap] 模块系统初始化失败 Error: 脚本加载失败: chrome-extension://kjdbgfgomclamokpjmnaeeoblfleignl/sidepanel/core/EventBus.js
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="356" aria-describedby="356" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:481 (initializeModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="Warning">
                </cr-icon>
                <div class="error-message" id="357">
                  <!--?lit$507789790$-->⚠️ [ModuleBootstrap] 尝试进入降级模式
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="Clear entry" data-error-id="357" aria-describedby="357" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$507789790$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Context
                  </div>
                  <span class="context-url">
                    <!--?lit$507789790$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    Stack Trace
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$507789790$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$507789790$-->ui/sidepanel/core/module-loader-bootstrap.js:484 (initializeModules)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="Nothing to see here, move along.">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>