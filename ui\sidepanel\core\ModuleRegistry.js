/**
 * 模块注册表 - 管理所有模块的配置和依赖关系
 * 定义模块加载顺序和依赖关系
 * 创建日期: 2025-01-11
 */

// 防止重复声明 - 修复版本
if (typeof window.ModuleRegistry !== 'undefined') {
    console.warn('⚠️ [ModuleRegistry] 类已存在，跳过重复声明');
} else {

class ModuleRegistry {
    constructor() {
        this.modules = new Map();
        this.initializeModuleDefinitions();
        
        console.log('📋 [ModuleRegistry] 模块注册表已初始化');
    }

    /**
     * 初始化所有模块定义
     */
    initializeModuleDefinitions() {
        // 核心模块 - 第一层，无依赖
        this.registerModule('EventBus', {
            path: 'ui/sidepanel/core/EventBus.js',
            dependencies: [],
            globalName: 'EventBus',
            priority: 1,
            essential: true
        });

        this.registerModule('ModuleLoader', {
            path: 'ui/sidepanel/core/ModuleLoader.js',
            dependencies: [],
            globalName: 'ModuleLoader',
            priority: 1,
            essential: true
        });

        // 工具模块 - 第二层，依赖核心模块
        this.registerModule('DateFormatter', {
            path: 'ui/sidepanel/utils/DateFormatter.js',
            dependencies: ['EventBus'],
            globalName: 'DateFormatter',
            priority: 2
        });

        this.registerModule('MessageHelper', {
            path: 'ui/sidepanel/utils/MessageHelper.js',
            dependencies: ['EventBus'],
            globalName: 'MessageHelper',
            priority: 2
        });

        this.registerModule('DebugLogger', {
            path: 'ui/sidepanel/utils/DebugLogger.js',
            dependencies: ['EventBus'],
            globalName: 'DebugLogger',
            priority: 2
        });

        this.registerModule('ModuleLoadingMonitor', {
            path: 'ui/sidepanel/utils/ModuleLoadingMonitor.js',
            dependencies: ['EventBus'],
            globalName: 'ModuleLoadingMonitor',
            priority: 2,
            optional: true  // 监控工具，非必需
        });

        // 核心业务模块 - 第三层
        this.registerModule('StateManager', {
            path: 'ui/sidepanel/core/StateManager.js',
            dependencies: ['EventBus', 'DebugLogger'],
            globalName: 'StateManager',
            priority: 3,
            essential: true
        });

        this.registerModule('EventManager', {
            path: 'ui/sidepanel/core/EventManager.js',
            dependencies: ['EventBus', 'StateManager'],
            globalName: 'EventManager',
            priority: 3,
            essential: true
        });

        // 数据服务模块 - 第四层
        this.registerModule('StorageService', {
            path: 'ui/sidepanel/data/StorageService.js',
            dependencies: ['EventBus', 'DebugLogger'],
            globalName: 'StorageService',
            priority: 4
        });

        this.registerModule('DataValidator', {
            path: 'ui/sidepanel/form/DataValidator.js',
            dependencies: ['EventBus', 'DateFormatter'],
            globalName: 'DataValidator',
            priority: 4
        });

        // AI服务模块 - 第五层
        this.registerModule('AIService', {
            path: 'ui/sidepanel/ai/AIService.js',
            dependencies: ['EventBus', 'DebugLogger', 'StorageService'],
            globalName: 'AIService',
            priority: 5
        });

        this.registerModule('TextParser', {
            path: 'ui/sidepanel/ai/TextParser.js',
            dependencies: ['EventBus', 'AIService', 'DateFormatter'],
            globalName: 'TextParser',
            priority: 5
        });

        this.registerModule('ImageProcessor', {
            path: 'ui/sidepanel/ai/ImageProcessor.js',
            dependencies: ['EventBus', 'AIService'],
            globalName: 'ImageProcessor',
            priority: 5
        });

        // 表单处理模块 - 第六层
        this.registerModule('FieldMatcher', {
            path: 'ui/sidepanel/form/FieldMatcher.js',
            dependencies: ['EventBus', 'DataValidator'],
            globalName: 'FieldMatcher',
            priority: 6
        });

        this.registerModule('FormFiller', {
            path: 'ui/sidepanel/form/FormFiller.js',
            dependencies: ['EventBus', 'FieldMatcher', 'DataValidator', 'DebugLogger'],
            globalName: 'FormFiller',
            priority: 6
        });

        // UI组件模块 - 第七层
        this.registerModule('ModalManager', {
            path: 'ui/sidepanel/ui/ModalManager.js',
            dependencies: ['EventBus', 'MessageHelper'],
            globalName: 'ModalManager',
            priority: 7
        });

        this.registerModule('ProgressVisualizer', {
            path: 'ui/sidepanel/ui/ProgressVisualizer.js',
            dependencies: ['EventBus'],
            globalName: 'ProgressVisualizer',
            priority: 7
        });

        this.registerModule('UIRenderer', {
            path: 'ui/sidepanel/ui/UIRenderer.js',
            dependencies: ['EventBus', 'ModalManager', 'MessageHelper'],
            globalName: 'UIRenderer',
            priority: 7
        });

        // 数据管理模块 - 第八层
        this.registerModule('DataManager', {
            path: 'ui/sidepanel/data/DataManager.js',
            dependencies: ['EventBus', 'StorageService', 'DataValidator'],
            globalName: 'DataManager',
            priority: 8
        });

        this.registerModule('PreviewManager', {
            path: 'ui/sidepanel/data/PreviewManager.js',
            dependencies: ['EventBus', 'UIRenderer', 'ModalManager'],
            globalName: 'PreviewManager',
            priority: 8
        });

        // 特色功能模块 - 第九层
        this.registerModule('ConfidenceEvaluator', {
            path: 'ui/sidepanel/features/ConfidenceEvaluator.js',
            dependencies: ['EventBus', 'DataValidator'],
            globalName: 'ConfidenceEvaluator',
            priority: 9
        });

        this.registerModule('AutoParseManager', {
            path: 'ui/sidepanel/features/AutoParseManager.js',
            dependencies: ['EventBus', 'AIService', 'TextParser', 'StorageService'],
            globalName: 'AutoParseManager',
            priority: 9
        });

        this.registerModule('CityViewer', {
            path: 'ui/sidepanel/features/CityViewer.js',
            dependencies: ['EventBus', 'UIRenderer', 'DataValidator', 'FormFiller'],
            globalName: 'CityViewer',
            priority: 9
        });

        // 主控制器 - 第十层，最后加载
        this.registerModule('SidePanelCore', {
            path: 'ui/sidepanel/core/SidePanelCore.js',
            dependencies: [
                'EventBus', 'StateManager', 'EventManager',
                'AIService', 'FormFiller', 'UIRenderer', 'DataManager',
                'AutoParseManager', 'CityViewer', 'ConfidenceEvaluator'
            ],
            globalName: 'SidePanelCore',
            priority: 10,
            essential: true
        });

        console.log(`📋 [ModuleRegistry] 已注册 ${this.modules.size} 个模块`);
    }

    /**
     * 注册模块
     * @param {string} name - 模块名称
     * @param {Object} config - 模块配置
     */
    registerModule(name, config) {
        const moduleConfig = {
            name,
            path: config.path,
            dependencies: config.dependencies || [],
            globalName: config.globalName || name,
            priority: config.priority || 999,
            essential: config.essential || false,
            lazy: config.lazy || false,
            timeout: config.timeout || 10000,
            retries: config.retries || 3,
            validator: config.validator || null,
            ...config
        };

        this.modules.set(name, moduleConfig);
    }

    /**
     * 获取模块配置
     * @param {string} name - 模块名称
     */
    getModule(name) {
        return this.modules.get(name);
    }

    /**
     * 获取所有模块
     */
    getAllModules() {
        return Array.from(this.modules.values());
    }

    /**
     * 按优先级排序获取模块
     */
    getModulesByPriority() {
        return Array.from(this.modules.values())
            .sort((a, b) => a.priority - b.priority);
    }

    /**
     * 获取必需模块
     */
    getEssentialModules() {
        return Array.from(this.modules.values())
            .filter(module => module.essential)
            .sort((a, b) => a.priority - b.priority);
    }

    /**
     * 获取模块的依赖链
     * @param {string} moduleName - 模块名称
     */
    getDependencyChain(moduleName) {
        const visited = new Set();
        const chain = [];

        const buildChain = (name) => {
            if (visited.has(name)) {
                return; // 避免循环依赖
            }

            visited.add(name);
            const module = this.modules.get(name);
            
            if (!module) {
                throw new Error(`模块未找到: ${name}`);
            }

            // 先处理依赖
            for (const dep of module.dependencies) {
                buildChain(dep);
            }

            // 再添加当前模块
            if (!chain.includes(name)) {
                chain.push(name);
            }
        };

        buildChain(moduleName);
        return chain;
    }

    /**
     * 检查循环依赖
     */
    checkCircularDependencies() {
        const visiting = new Set();
        const visited = new Set();
        const cycles = [];

        const visit = (moduleName, path = []) => {
            if (visiting.has(moduleName)) {
                // 发现循环依赖
                const cycleStart = path.indexOf(moduleName);
                cycles.push([...path.slice(cycleStart), moduleName]);
                return;
            }

            if (visited.has(moduleName)) {
                return;
            }

            visiting.add(moduleName);
            const module = this.modules.get(moduleName);
            
            if (module) {
                for (const dep of module.dependencies) {
                    visit(dep, [...path, moduleName]);
                }
            }

            visiting.delete(moduleName);
            visited.add(moduleName);
        };

        for (const moduleName of this.modules.keys()) {
            if (!visited.has(moduleName)) {
                visit(moduleName);
            }
        }

        return cycles;
    }

    /**
     * 验证模块注册表
     */
    validate() {
        const errors = [];

        // 检查循环依赖
        const cycles = this.checkCircularDependencies();
        if (cycles.length > 0) {
            errors.push(`发现循环依赖: ${cycles.map(cycle => cycle.join(' -> ')).join(', ')}`);
        }

        // 检查依赖是否存在
        for (const [name, module] of this.modules) {
            for (const dep of module.dependencies) {
                if (!this.modules.has(dep)) {
                    errors.push(`模块 ${name} 的依赖 ${dep} 不存在`);
                }
            }
        }

        // 检查路径是否有效
        for (const [name, module] of this.modules) {
            if (!module.path) {
                errors.push(`模块 ${name} 缺少路径配置`);
            }
        }

        if (errors.length > 0) {
            console.error('❌ [ModuleRegistry] 模块注册表验证失败:', errors);
            return { valid: false, errors };
        }

        console.log('✅ [ModuleRegistry] 模块注册表验证通过');
        return { valid: true, errors: [] };
    }

    /**
     * 获取加载计划
     * @param {Array} moduleNames - 要加载的模块名称
     */
    getLoadPlan(moduleNames = null) {
        const targetModules = moduleNames || Array.from(this.modules.keys());
        const plan = new Map();

        // 收集所有需要加载的模块（包括依赖）
        const allModules = new Set();
        for (const moduleName of targetModules) {
            const chain = this.getDependencyChain(moduleName);
            chain.forEach(name => allModules.add(name));
        }

        // 按优先级分组
        for (const moduleName of allModules) {
            const module = this.modules.get(moduleName);
            if (module) {
                const priority = module.priority;
                if (!plan.has(priority)) {
                    plan.set(priority, []);
                }
                plan.get(priority).push(moduleName);
            }
        }

        // 转换为有序数组
        return Array.from(plan.keys())
            .sort((a, b) => a - b)
            .map(priority => ({
                priority,
                modules: plan.get(priority)
            }));
    }
}

// 创建全局模块注册表实例
window.mdacModuleRegistry = new ModuleRegistry();

// 导出类和实例 - 确保在任何情况下都能正确导出
// 导出类 - 兼容 ultimate-bootstrap 属性保护系统
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModuleRegistry, moduleRegistry: window.mdacModuleRegistry };
}

// 安全的全局导出，兼容属性保护
try {
    if (typeof window.ModuleRegistry === 'undefined') {
        window.ModuleRegistry = ModuleRegistry;
    }
} catch (error) {
    // 如果属性被保护，直接设置
    window.ModuleRegistry = ModuleRegistry;
}

} // 结束重复声明保护

console.log('✅ [ModuleRegistry] 模块注册表已加载');
