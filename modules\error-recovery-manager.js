/**
 * ErrorRecoveryManager模块 - 兼容性存根
 * Content Script Adapter中已提供ErrorRecoveryManager，此文件确保路径存在
 */

console.log('🛡️ [modules/error-recovery-manager.js] 兼容性存根文件已加载 - ErrorRecoveryManager在adapter中提供');

// 确保ErrorRecoveryManager可用的后备方案
if (typeof window.ErrorRecoveryManager === 'undefined') {
    console.warn('⚠️ [modules/error-recovery-manager.js] ErrorRecoveryManager未定义，可能是adapter加载失败');
    
    // 提供基础的兼容性实现
    window.ErrorRecoveryManager = class {
        constructor() {
            console.log('🛡️ [ErrorRecoveryManager] 后备实现已初始化');
        }
        async initialize() { /* 空实现 */ }
        handleError(error) { console.error('Error:', error); }
        recover() { /* 空实现 */ }
    };
}
