/**
 * 模块加载测试工具
 * 验证方案一的效果，确保没有重复声明错误
 * 创建日期: 2025-01-11
 * 修复日期: 2025-07-12
 */

// 防止重复声明的新方法 - 使用简单检查
(function() {
    'use strict';

    // 如果ModuleLoadingTester已存在且功能正常，则跳过
    if (typeof window.ModuleLoadingTester === 'function') {
        console.log('✅ [ModuleLoadingTester] 已存在且正常，跳过重新定义');
        return;
    }

    class ModuleLoadingTester {
    constructor() {
        this.testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            errors: [],
            startTime: null,
            endTime: null,
            testDuration: 0
        };

        this.expectedModules = [
            'EventBus', 'StateManager', 'ModuleRegistry', 'ModuleLoader',
            'EventManager', 'SidePanelCore', 'ModuleInitializer',
            'ModuleLoadingMonitor', 'DateFormatter', 'MessageHelper', 'DebugLogger',
            'StorageService', 'DataManager', 'PreviewManager',
            'AIService', 'TextParser', 'ImageProcessor',
            'DataValidator', 'FieldMatcher', 'FormFiller',
            'ModalManager', 'ProgressVisualizer', 'UIRenderer',
            'ConfidenceEvaluator', 'CityViewer', 'AutoParseManager',
            'LegacyAdapter', 'MDACModularSidePanel'
        ];

        console.log('🧪 [ModuleLoadingTester] 模块加载测试工具已初始化');
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🚀 [ModuleLoadingTester] 开始运行所有测试');
        
        this.testResults.startTime = Date.now();

        try {
            // 测试1: 检查控制台错误
            await this.testConsoleErrors();

            // 测试2: 检查模块可用性
            await this.testModuleAvailability();

            // 测试3: 检查重复声明
            await this.testDuplicateDeclarations();

            // 测试4: 检查事件总线功能
            await this.testEventBusFunctionality();

            // 测试5: 检查模块初始化顺序
            await this.testModuleInitializationOrder();

            // 测试6: 性能测试
            await this.testLoadingPerformance();

        } catch (error) {
            console.error('❌ [ModuleLoadingTester] 测试过程中发生错误', error);
            this.addError('测试过程异常', error.message);
        }

        this.testResults.endTime = Date.now();
        this.testResults.testDuration = this.testResults.endTime - this.testResults.startTime;

        this.generateReport();
    }

    /**
     * 测试控制台错误
     */
    async testConsoleErrors() {
        this.startTest('控制台错误检查');

        try {
            // 捕获控制台错误
            const originalError = console.error;
            const errors = [];

            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };

            // 等待一段时间收集错误
            await this.delay(2000);

            // 恢复原始console.error
            console.error = originalError;

            // 检查是否有重复声明错误
            const duplicateErrors = errors.filter(error => 
                error.includes('has already been declared') ||
                error.includes('already been declared') ||
                error.includes('Identifier') && error.includes('declared')
            );

            if (duplicateErrors.length > 0) {
                this.failTest('发现重复声明错误', duplicateErrors);
            } else {
                this.passTest('未发现重复声明错误');
            }

        } catch (error) {
            this.failTest('控制台错误检查失败', error.message);
        }
    }

    /**
     * 测试模块可用性
     */
    async testModuleAvailability() {
        this.startTest('模块可用性检查');

        try {
            const missingModules = [];
            const availableModules = [];

            for (const moduleName of this.expectedModules) {
                if (window[moduleName] && typeof window[moduleName] === 'function') {
                    availableModules.push(moduleName);
                } else {
                    missingModules.push(moduleName);
                }
            }

            if (missingModules.length > 0) {
                this.failTest(`缺失模块: ${missingModules.join(', ')}`);
            } else {
                this.passTest(`所有模块可用 (${availableModules.length}/${this.expectedModules.length})`);
            }

        } catch (error) {
            this.failTest('模块可用性检查失败', error.message);
        }
    }

    /**
     * 测试重复声明
     */
    async testDuplicateDeclarations() {
        this.startTest('重复声明检查');

        try {
            const testResults = [];

            for (const moduleName of this.expectedModules.slice(0, 5)) { // 测试前5个模块
                try {
                    // 尝试重新声明（这应该被防护代码阻止）
                    const testScript = `
                        if (typeof ${moduleName} !== 'undefined') {
                            console.log('✅ 重复声明防护生效: ${moduleName}');
                        } else {
                            console.warn('⚠️ 模块不存在: ${moduleName}');
                        }
                    `;
                    
                    eval(testScript);
                    testResults.push(`${moduleName}: 防护正常`);
                    
                } catch (error) {
                    testResults.push(`${moduleName}: 防护失败 - ${error.message}`);
                }
            }

            this.passTest(`重复声明防护测试完成: ${testResults.length} 个模块测试`);

        } catch (error) {
            this.failTest('重复声明检查失败', error.message);
        }
    }

    /**
     * 测试事件总线功能
     */
    async testEventBusFunctionality() {
        this.startTest('事件总线功能检查');

        try {
            if (!window.mdacEventBus) {
                this.failTest('事件总线不存在');
                return;
            }

            // 测试事件发布和订阅
            let eventReceived = false;
            const testEvent = 'test:module-loading-test';
            const testData = { message: 'Hello from test', timestamp: Date.now() };

            const unsubscribe = window.mdacEventBus.on(testEvent, (data) => {
                if (data.message === testData.message) {
                    eventReceived = true;
                }
            });

            // 发送测试事件
            window.mdacEventBus.emit(testEvent, testData);

            // 等待事件处理
            await this.delay(100);

            // 清理
            unsubscribe();

            if (eventReceived) {
                this.passTest('事件总线功能正常');
            } else {
                this.failTest('事件总线功能异常');
            }

        } catch (error) {
            this.failTest('事件总线功能检查失败', error.message);
        }
    }

    /**
     * 测试模块初始化顺序
     */
    async testModuleInitializationOrder() {
        this.startTest('模块初始化顺序检查');

        try {
            // 检查关键模块的依赖关系
            const coreModules = ['EventBus', 'StateManager', 'ModuleLoader'];
            const uiModules = ['UIRenderer', 'ModalManager'];
            
            let orderCorrect = true;
            const orderIssues = [];

            // 检查核心模块是否先于UI模块加载
            for (const coreModule of coreModules) {
                if (!window[coreModule]) {
                    orderIssues.push(`核心模块未加载: ${coreModule}`);
                    orderCorrect = false;
                }
            }

            if (orderCorrect) {
                this.passTest('模块初始化顺序正确');
            } else {
                this.failTest(`模块初始化顺序异常: ${orderIssues.join(', ')}`);
            }

        } catch (error) {
            this.failTest('模块初始化顺序检查失败', error.message);
        }
    }

    /**
     * 测试加载性能
     */
    async testLoadingPerformance() {
        this.startTest('加载性能检查');

        try {
            // 检查模块加载器是否存在
            if (!window.mdacModuleBootstrap) {
                this.failTest('模块引导程序不存在');
                return;
            }

            const status = window.mdacModuleBootstrap.getLoadingStatus();
            
            const performanceMetrics = {
                totalModules: status.totalModules,
                loadedModules: status.loadedModules.length,
                loadingModules: status.loadingModules.length,
                completionRate: (status.loadedModules.length / status.totalModules * 100).toFixed(1)
            };

            this.passTest(`性能指标: ${JSON.stringify(performanceMetrics)}`);

        } catch (error) {
            this.failTest('加载性能检查失败', error.message);
        }
    }

    /**
     * 开始测试
     */
    startTest(testName) {
        this.testResults.totalTests++;
        console.log(`🧪 [ModuleLoadingTester] 开始测试: ${testName}`);
    }

    /**
     * 测试通过
     */
    passTest(message) {
        this.testResults.passedTests++;
        console.log(`✅ [ModuleLoadingTester] 测试通过: ${message}`);
    }

    /**
     * 测试失败
     */
    failTest(message, details = null) {
        this.testResults.failedTests++;
        console.error(`❌ [ModuleLoadingTester] 测试失败: ${message}`);
        
        this.addError(message, details);
    }

    /**
     * 添加错误
     */
    addError(message, details = null) {
        this.testResults.errors.push({
            message,
            details,
            timestamp: Date.now()
        });
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        const report = {
            summary: {
                totalTests: this.testResults.totalTests,
                passedTests: this.testResults.passedTests,
                failedTests: this.testResults.failedTests,
                successRate: ((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(1),
                testDuration: this.testResults.testDuration
            },
            errors: this.testResults.errors,
            expectedModules: this.expectedModules,
            timestamp: Date.now()
        };

        console.log('📊 [ModuleLoadingTester] 测试报告:', report);

        // 显示总结
        const status = report.summary.failedTests === 0 ? '✅ 全部通过' : '❌ 存在问题';
        console.log(`🏁 [ModuleLoadingTester] 测试完成 ${status} (${report.summary.passedTests}/${report.summary.totalTests})`);

        // 保存到全局以便调试
        window.mdacTestReport = report;

        return report;
    }

    /**
     * 运行快速验证
     */
    async quickValidation() {
        console.log('⚡ [ModuleLoadingTester] 运行快速验证');

        const results = {
            bootstrap: !!window.mdacModuleBootstrap,
            eventBus: !!window.mdacEventBus,
            moduleCount: 0,
            errors: []
        };

        // 计算加载的模块数量
        for (const moduleName of this.expectedModules) {
            if (window[moduleName]) {
                results.moduleCount++;
            }
        }

        results.completionRate = (results.moduleCount / this.expectedModules.length * 100).toFixed(1);

        console.log('📊 快速验证结果:', results);
        return results;
    }
}

    // 立即注册到全局对象
    window.ModuleLoadingTester = ModuleLoadingTester;
    console.log('✅ [ModuleLoadingTester] 类已注册到全局对象');

    // 创建全局测试实例
    if (!window.mdacModuleLoadingTester) {
        window.mdacModuleLoadingTester = new ModuleLoadingTester();
        console.log('🧪 [ModuleLoadingTester] 全局测试实例已创建');
    }

    // 自动运行快速验证
    setTimeout(() => {
        if (window.mdacModuleLoadingTester) {
            window.mdacModuleLoadingTester.quickValidation();
        }
    }, 5000);

    // 导出类 - 兼容 ultimate-bootstrap 属性保护系统
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ModuleLoadingTester;
    }

    console.log('✅ [ModuleLoadingTester] 模块加载测试工具已加载完成');

})();
