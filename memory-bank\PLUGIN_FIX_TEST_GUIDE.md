# MDAC插件修复测试指南

## 修复内容概述
已修复插件无法操作的核心问题：
- ✅ 修复EventBus全局实例创建问题
- ✅ 创建新的稳定引导程序
- ✅ 添加系统验证和错误诊断
- ✅ 禁用有问题的原引导程序

## 测试步骤

### 1. 打开Chrome扩展
1. 在Chrome中打开扩展管理页面 (`chrome://extensions/`)
2. 确保MDAC AI助手扩展已启用
3. 打开任意网页（建议打开MDAC官方网站）

### 2. 打开侧边栏
1. 点击Chrome工具栏中的MDAC扩展图标
2. 或者使用右键菜单选择"打开MDAC AI助手"

### 3. 检查修复状态

#### 方法一：查看通知
- 如果修复成功，应该在页面顶部看到绿色通知："✅ 系统修复成功！"
- 如果部分修复，会显示："⚠️ 系统部分修复"
- 如果修复失败，会显示："❌ 系统修复失败"

#### 方法二：查看控制台
1. 按F12打开开发者工具
2. 切换到"Console"选项卡
3. 查找以下成功信息：
   ```
   ✅ [Test] EventBus类存在: 通过
   ✅ [Test] EventBus实例存在: 通过
   ✅ [Test] StateManager类存在: 通过
   🎉 [SystemValidator] 系统修复成功，所有测试通过！
   ```

#### 方法三：测试功能
1. 尝试在文本框中输入内容
2. 尝试上传图片
3. 查看按钮是否可以点击
4. 检查是否有交互反馈

### 4. 详细诊断信息

在控制台中输入以下命令查看详细状态：
```javascript
// 查看验证结果
console.log(window.mdacValidationResults);

// 查看调试信息（如果加载了调试脚本）
console.log(window.mdacDebugInfo);

// 手动检查核心组件
console.log('EventBus:', typeof window.EventBus);
console.log('EventBus实例:', typeof window.mdacEventBus);
console.log('StateManager:', typeof window.StateManager);
console.log('固定引导程序:', typeof window.mdacFixedBootstrap);
```

## 预期结果

### 成功状态
- ✅ 页面顶部显示绿色成功通知
- ✅ 控制台显示所有测试通过
- ✅ 插件界面可以正常交互
- ✅ 没有红色错误信息

### 失败排查
如果仍然有问题：

1. **检查脚本加载**
   ```javascript
   // 检查脚本是否加载
   Array.from(document.scripts).forEach(s => {
       if (s.src.includes('fixed-bootstrap')) {
           console.log('✅ 修复引导程序已加载:', s.src);
       }
   });
   ```

2. **检查错误信息**
   - 查看控制台是否有红色错误
   - 注意任何加载失败的脚本

3. **手动重启**
   - 刷新页面重试
   - 重新启动扩展
   - 重启Chrome浏览器

## 回滚步骤
如果需要恢复到修复前状态：

1. 恢复原引导程序：
   ```
   cd "插件目录\ui\sidepanel\core"
   ren ultimate-bootstrap.js.backup ultimate-bootstrap.js
   ```

2. 修改HTML文件，将引用改回：
   ```html
   <script src="sidepanel/core/ultimate-bootstrap.js"></script>
   ```

3. 删除修复文件：
   - 删除 `fixed-bootstrap.js`
   - 删除 `system-validator.js`

## 技术支持
如果问题持续存在，请提供：
- 控制台错误截图
- `window.mdacValidationResults` 输出
- Chrome版本和操作系统信息
- 详细的复现步骤

---
测试指南版本：1.0  
修复日期：2025-07-12
