/**
 * 消息提示工具 - 统一的消息显示和用户反馈
 * 提供各种类型的消息提示和确认对话框
 * 创建日期: 2025-01-11
 */

class MessageHelper {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 消息容器
        this.messageContainer = null;
        
        // 消息队列
        this.messageQueue = [];
        this.isProcessingQueue = false;
        
        // 配置
        this.config = {
            maxMessages: 5,
            defaultDuration: 5000,
            animationDuration: 300,
            position: 'top-right'
        };

        // 消息类型配置
        this.messageTypes = {
            success: {
                icon: '✅',
                className: 'message-success',
                color: '#28a745',
                duration: 3000
            },
            error: {
                icon: '❌',
                className: 'message-error',
                color: '#dc3545',
                duration: 8000
            },
            warning: {
                icon: '⚠️',
                className: 'message-warning',
                color: '#ffc107',
                duration: 5000
            },
            info: {
                icon: 'ℹ️',
                className: 'message-info',
                color: '#17a2b8',
                duration: 4000
            },
            loading: {
                icon: '⏳',
                className: 'message-loading',
                color: '#6c757d',
                duration: 0 // 不自动消失
            }
        };

        console.log('💬 [MessageHelper] 消息提示工具已初始化');
        this.initializeContainer();
    }

    /**
     * 初始化消息容器
     */
    initializeContainer() {
        // 检查是否已存在容器
        this.messageContainer = document.getElementById('mdac-message-container');
        
        if (!this.messageContainer) {
            this.messageContainer = document.createElement('div');
            this.messageContainer.id = 'mdac-message-container';
            this.messageContainer.className = `message-container position-${this.config.position}`;
            
            // 添加样式
            this.messageContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            
            document.body.appendChild(this.messageContainer);
        }

        // 添加CSS样式
        this.injectStyles();
    }

    /**
     * 注入CSS样式
     */
    injectStyles() {
        const styleId = 'mdac-message-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .message-container {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .message-item {
                background: white;
                border-radius: 8px;
                padding: 12px 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-left: 4px solid #ccc;
                display: flex;
                align-items: flex-start;
                gap: 12px;
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                max-width: 100%;
                word-wrap: break-word;
            }
            
            .message-item.show {
                transform: translateX(0);
                opacity: 1;
            }
            
            .message-item.hide {
                transform: translateX(100%);
                opacity: 0;
                margin-bottom: -60px;
            }
            
            .message-success {
                border-left-color: #28a745;
            }
            
            .message-error {
                border-left-color: #dc3545;
            }
            
            .message-warning {
                border-left-color: #ffc107;
            }
            
            .message-info {
                border-left-color: #17a2b8;
            }
            
            .message-loading {
                border-left-color: #6c757d;
            }
            
            .message-icon {
                font-size: 16px;
                flex-shrink: 0;
                margin-top: 2px;
            }
            
            .message-content {
                flex: 1;
                min-width: 0;
            }
            
            .message-title {
                font-weight: 600;
                margin-bottom: 4px;
                color: #333;
                font-size: 14px;
            }
            
            .message-text {
                color: #666;
                font-size: 13px;
                line-height: 1.4;
            }
            
            .message-close {
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: #999;
                padding: 0;
                margin-left: 8px;
                flex-shrink: 0;
            }
            
            .message-close:hover {
                color: #333;
            }
            
            .message-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 2px;
                background: rgba(0,0,0,0.1);
                transition: width linear;
            }
            
            .message-loading .message-icon {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 显示消息
     * @param {string} type - 消息类型
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    show(type, message, options = {}) {
        const messageConfig = this.messageTypes[type] || this.messageTypes.info;
        
        const messageData = {
            id: this.generateId(),
            type,
            message,
            title: options.title || '',
            duration: options.duration !== undefined ? options.duration : messageConfig.duration,
            closable: options.closable !== false,
            persistent: options.persistent || false,
            timestamp: Date.now(),
            ...messageConfig
        };

        // 添加到队列
        this.messageQueue.push(messageData);
        
        // 处理队列
        this.processQueue();

        // 发布事件
        if (this.eventBus) {
            this.eventBus.emit('message:shown', messageData);
        }

        return messageData.id;
    }

    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    success(message, options = {}) {
        return this.show('success', message, options);
    }

    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    error(message, options = {}) {
        return this.show('error', message, options);
    }

    /**
     * 显示警告消息
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    warning(message, options = {}) {
        return this.show('warning', message, options);
    }

    /**
     * 显示信息消息
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    info(message, options = {}) {
        return this.show('info', message, options);
    }

    /**
     * 显示加载消息
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    loading(message, options = {}) {
        return this.show('loading', message, { ...options, persistent: true });
    }

    /**
     * 处理消息队列
     */
    async processQueue() {
        if (this.isProcessingQueue || this.messageQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.messageQueue.length > 0) {
            const messageData = this.messageQueue.shift();
            await this.displayMessage(messageData);
            
            // 检查消息数量限制
            this.enforceMessageLimit();
        }

        this.isProcessingQueue = false;
    }

    /**
     * 显示单个消息
     * @param {Object} messageData - 消息数据
     */
    async displayMessage(messageData) {
        const messageElement = this.createMessageElement(messageData);
        this.messageContainer.appendChild(messageElement);

        // 触发显示动画
        await this.delay(10);
        messageElement.classList.add('show');

        // 设置自动消失
        if (messageData.duration > 0 && !messageData.persistent) {
            setTimeout(() => {
                this.hideMessage(messageData.id);
            }, messageData.duration);
        }
    }

    /**
     * 创建消息元素
     * @param {Object} messageData - 消息数据
     */
    createMessageElement(messageData) {
        const messageElement = document.createElement('div');
        messageElement.className = `message-item ${messageData.className}`;
        messageElement.dataset.messageId = messageData.id;

        const iconElement = document.createElement('div');
        iconElement.className = 'message-icon';
        iconElement.textContent = messageData.icon;

        const contentElement = document.createElement('div');
        contentElement.className = 'message-content';

        if (messageData.title) {
            const titleElement = document.createElement('div');
            titleElement.className = 'message-title';
            titleElement.textContent = messageData.title;
            contentElement.appendChild(titleElement);
        }

        const textElement = document.createElement('div');
        textElement.className = 'message-text';
        textElement.textContent = messageData.message;
        contentElement.appendChild(textElement);

        messageElement.appendChild(iconElement);
        messageElement.appendChild(contentElement);

        // 添加关闭按钮
        if (messageData.closable) {
            const closeButton = document.createElement('button');
            closeButton.className = 'message-close';
            closeButton.innerHTML = '×';
            closeButton.onclick = () => this.hideMessage(messageData.id);
            messageElement.appendChild(closeButton);
        }

        // 添加进度条（如果有持续时间）
        if (messageData.duration > 0 && !messageData.persistent) {
            const progressElement = document.createElement('div');
            progressElement.className = 'message-progress';
            progressElement.style.width = '100%';
            progressElement.style.background = messageData.color;
            messageElement.style.position = 'relative';
            messageElement.appendChild(progressElement);

            // 动画进度条
            setTimeout(() => {
                progressElement.style.width = '0%';
                progressElement.style.transition = `width ${messageData.duration}ms linear`;
            }, 10);
        }

        return messageElement;
    }

    /**
     * 隐藏消息
     * @param {string} messageId - 消息ID
     */
    hideMessage(messageId) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (!messageElement) return;

        messageElement.classList.add('hide');

        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.remove();
            }
        }, this.config.animationDuration);

        // 发布事件
        if (this.eventBus) {
            this.eventBus.emit('message:hidden', { messageId });
        }
    }

    /**
     * 更新消息内容
     * @param {string} messageId - 消息ID
     * @param {string} newMessage - 新消息内容
     * @param {Object} options - 选项
     */
    updateMessage(messageId, newMessage, options = {}) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (!messageElement) return false;

        const textElement = messageElement.querySelector('.message-text');
        if (textElement) {
            textElement.textContent = newMessage;
        }

        const titleElement = messageElement.querySelector('.message-title');
        if (options.title && titleElement) {
            titleElement.textContent = options.title;
        }

        return true;
    }

    /**
     * 清除所有消息
     */
    clearAll() {
        const messages = this.messageContainer.querySelectorAll('.message-item');
        messages.forEach(message => {
            message.classList.add('hide');
        });

        setTimeout(() => {
            this.messageContainer.innerHTML = '';
        }, this.config.animationDuration);

        // 清空队列
        this.messageQueue = [];

        // 发布事件
        if (this.eventBus) {
            this.eventBus.emit('message:cleared-all');
        }
    }

    /**
     * 清除指定类型的消息
     * @param {string} type - 消息类型
     */
    clearByType(type) {
        const messages = this.messageContainer.querySelectorAll(`.message-${type}`);
        messages.forEach(message => {
            const messageId = message.dataset.messageId;
            this.hideMessage(messageId);
        });
    }

    /**
     * 确认对话框
     * @param {string} message - 确认消息
     * @param {Object} options - 选项
     */
    confirm(message, options = {}) {
        return new Promise((resolve) => {
            const {
                title = '确认',
                confirmText = '确定',
                cancelText = '取消',
                type = 'warning'
            } = options;

            // 创建确认对话框
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            `;

            const messageConfig = this.messageTypes[type] || this.messageTypes.warning;

            dialog.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                    <span style="font-size: 24px; margin-right: 12px;">${messageConfig.icon}</span>
                    <h3 style="margin: 0; color: #333; font-size: 18px;">${title}</h3>
                </div>
                <p style="margin: 0 0 24px 0; color: #666; line-height: 1.5;">${message}</p>
                <div style="display: flex; gap: 12px; justify-content: flex-end;">
                    <button id="cancel-btn" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">${cancelText}</button>
                    <button id="confirm-btn" style="
                        background: ${messageConfig.color};
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                    ">${confirmText}</button>
                </div>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            // 事件处理
            const cleanup = () => {
                document.body.removeChild(overlay);
            };

            dialog.querySelector('#confirm-btn').onclick = () => {
                cleanup();
                resolve(true);
            };

            dialog.querySelector('#cancel-btn').onclick = () => {
                cleanup();
                resolve(false);
            };

            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    cleanup();
                    resolve(false);
                }
            };

            // ESC键取消
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    document.removeEventListener('keydown', handleKeydown);
                    cleanup();
                    resolve(false);
                }
            };
            document.addEventListener('keydown', handleKeydown);
        });
    }

    /**
     * 强制执行消息数量限制
     */
    enforceMessageLimit() {
        const messages = this.messageContainer.querySelectorAll('.message-item');
        if (messages.length > this.config.maxMessages) {
            const excess = messages.length - this.config.maxMessages;
            for (let i = 0; i < excess; i++) {
                const messageId = messages[i].dataset.messageId;
                this.hideMessage(messageId);
            }
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取当前显示的消息
     */
    getActiveMessages() {
        const messages = this.messageContainer.querySelectorAll('.message-item');
        return Array.from(messages).map(msg => ({
            id: msg.dataset.messageId,
            type: msg.className.match(/message-(\w+)/)?.[1],
            text: msg.querySelector('.message-text')?.textContent
        }));
    }

    /**
     * 销毁消息助手
     */
    destroy() {
        this.clearAll();
        
        if (this.messageContainer && this.messageContainer.parentElement) {
            this.messageContainer.remove();
        }

        const styles = document.getElementById('mdac-message-styles');
        if (styles) {
            styles.remove();
        }

        console.log('🗑️ [MessageHelper] 消息提示工具已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MessageHelper;
} else {
    window.MessageHelper = MessageHelper;
}

console.log('✅ [MessageHelper] 消息提示工具已加载');
