/**
 * 模块重复声明保护工具
 * 自动为所有JavaScript模块添加重复声明检查
 * 创建日期: 2025-01-11
 */

const fs = require('fs');
const path = require('path');

class ModuleDuplicateProtector {
    constructor() {
        this.processedFiles = [];
        this.errors = [];
        
        // 需要处理的模块目录
        this.moduleDirectories = [
            'ui/sidepanel/core',
            'ui/sidepanel/utils', 
            'ui/sidepanel/data',
            'ui/sidepanel/ai',
            'ui/sidepanel/form',
            'ui/sidepanel/ui',
            'ui/sidepanel/features',
            'ui/sidepanel/compatibility',
            'ui/sidepanel/config',
            'ui/sidepanel/tests'
        ];
    }

    /**
     * 处理所有模块文件
     */
    async processAllModules() {
        console.log('🔧 [ModuleDuplicateProtector] 开始处理所有模块文件');

        for (const dir of this.moduleDirectories) {
            try {
                await this.processDirectory(dir);
            } catch (error) {
                console.error(`❌ 处理目录失败: ${dir}`, error);
                this.errors.push({ directory: dir, error: error.message });
            }
        }

        console.log(`✅ [ModuleDuplicateProtector] 处理完成`);
        console.log(`- 已处理文件: ${this.processedFiles.length}`);
        console.log(`- 错误数量: ${this.errors.length}`);

        if (this.errors.length > 0) {
            console.log('错误详情:', this.errors);
        }
    }

    /**
     * 处理目录中的所有JS文件
     */
    async processDirectory(dirPath) {
        if (!fs.existsSync(dirPath)) {
            console.warn(`⚠️ 目录不存在: ${dirPath}`);
            return;
        }

        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
            if (file.endsWith('.js')) {
                const filePath = path.join(dirPath, file);
                await this.processFile(filePath);
            }
        }
    }

    /**
     * 处理单个文件
     */
    async processFile(filePath) {
        try {
            console.log(`📝 处理文件: ${filePath}`);

            const content = fs.readFileSync(filePath, 'utf8');
            
            // 检查是否已经有保护代码
            if (content.includes('防止重复声明') || content.includes('结束重复声明检查')) {
                console.log(`⏭️ 文件已有保护代码，跳过: ${filePath}`);
                return;
            }

            // 提取类名
            const className = this.extractClassName(content);
            if (!className) {
                console.log(`⏭️ 未找到类定义，跳过: ${filePath}`);
                return;
            }

            // 添加保护代码
            const protectedContent = this.addDuplicateProtection(content, className);
            
            // 写回文件
            fs.writeFileSync(filePath, protectedContent, 'utf8');
            
            this.processedFiles.push(filePath);
            console.log(`✅ 已添加保护代码: ${filePath} (${className})`);

        } catch (error) {
            console.error(`❌ 处理文件失败: ${filePath}`, error);
            this.errors.push({ file: filePath, error: error.message });
        }
    }

    /**
     * 提取类名
     */
    extractClassName(content) {
        const classMatch = content.match(/class\s+(\w+)\s*{/);
        return classMatch ? classMatch[1] : null;
    }

    /**
     * 添加重复声明保护
     */
    addDuplicateProtection(content, className) {
        // 在类定义前添加保护检查
        const classDefRegex = new RegExp(`(class\\s+${className}\\s*{)`, 'g');
        
        const protectionCode = `// 防止重复声明
if (typeof ${className} !== 'undefined') {
    console.warn('⚠️ [${className}] 类已存在，跳过重复声明');
} else {

$1`;

        let protectedContent = content.replace(classDefRegex, protectionCode);

        // 在文件末尾添加结束标记
        if (protectedContent.includes('} // 结束重复声明检查')) {
            return protectedContent;
        }

        // 在最后一个console.log之后添加结束标记
        const lastConsoleLogRegex = /(console\.log\([^)]*\);)(\s*)$/;
        if (lastConsoleLogRegex.test(protectedContent)) {
            protectedContent = protectedContent.replace(lastConsoleLogRegex, 
                `$1$2
} // 结束重复声明检查$2`);
        } else {
            // 如果没有console.log，在文件末尾添加
            protectedContent += `\n\n} // 结束重复声明检查\n`;
        }

        return protectedContent;
    }

    /**
     * 生成报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            processedFiles: this.processedFiles.length,
            errors: this.errors.length,
            files: this.processedFiles,
            errorDetails: this.errors
        };

        const reportPath = 'duplicate-protection-report.json';
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📊 报告已生成: ${reportPath}`);
        return report;
    }
}

// 如果作为脚本运行
if (require.main === module) {
    const protector = new ModuleDuplicateProtector();
    protector.processAllModules()
        .then(() => protector.generateReport())
        .catch(console.error);
}

module.exports = ModuleDuplicateProtector;
