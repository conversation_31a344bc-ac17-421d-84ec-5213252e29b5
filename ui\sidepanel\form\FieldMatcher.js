/**
 * 字段匹配器 - 智能表单字段识别和匹配
 * 负责识别网页表单字段并与数据字段进行智能匹配
 * 创建日期: 2025-01-11
 */

class FieldMatcher {
    constructor(eventBus = window.mdacEventBus, dataValidator = null) {
        this.eventBus = eventBus;
        this.dataValidator = dataValidator;
        
        // 字段匹配规则
        this.matchingRules = {
            // 个人信息字段匹配
            personalInfo: {
                name: {
                    selectors: [
                        'input[name*="name"]',
                        'input[id*="name"]',
                        'input[placeholder*="姓名"]',
                        'input[placeholder*="Name"]',
                        'input[name*="fullname"]',
                        'input[id*="fullname"]'
                    ],
                    keywords: ['姓名', 'name', 'fullname', '全名', 'applicant'],
                    priority: 10
                },
                gender: {
                    selectors: [
                        'select[name*="gender"]',
                        'select[id*="gender"]',
                        'input[name*="sex"]',
                        'select[name*="sex"]',
                        'input[type="radio"][name*="gender"]'
                    ],
                    keywords: ['性别', 'gender', 'sex', '男女'],
                    priority: 8
                },
                birthDate: {
                    selectors: [
                        'input[name*="birth"]',
                        'input[id*="birth"]',
                        'input[name*="dob"]',
                        'input[id*="dob"]',
                        'input[type="date"]'
                    ],
                    keywords: ['出生', 'birth', 'dob', '生日', 'birthday'],
                    priority: 9
                },
                nationality: {
                    selectors: [
                        'select[name*="nationality"]',
                        'select[id*="nationality"]',
                        'select[name*="country"]',
                        'input[name*="nationality"]'
                    ],
                    keywords: ['国籍', 'nationality', 'country', '国家'],
                    priority: 7
                },
                passportNumber: {
                    selectors: [
                        'input[name*="passport"]',
                        'input[id*="passport"]',
                        'input[name*="document"]',
                        'input[placeholder*="护照"]'
                    ],
                    keywords: ['护照', 'passport', 'document', '证件'],
                    priority: 9
                }
            },
            
            // 联系信息字段匹配
            contactInfo: {
                phone: {
                    selectors: [
                        'input[name*="phone"]',
                        'input[id*="phone"]',
                        'input[name*="mobile"]',
                        'input[name*="tel"]',
                        'input[type="tel"]'
                    ],
                    keywords: ['电话', 'phone', 'mobile', 'tel', '手机'],
                    priority: 8
                },
                email: {
                    selectors: [
                        'input[name*="email"]',
                        'input[id*="email"]',
                        'input[type="email"]',
                        'input[name*="mail"]'
                    ],
                    keywords: ['邮箱', 'email', 'mail', '电子邮件'],
                    priority: 9
                },
                address: {
                    selectors: [
                        'input[name*="address"]',
                        'textarea[name*="address"]',
                        'input[id*="address"]',
                        'input[name*="location"]'
                    ],
                    keywords: ['地址', 'address', 'location', '住址'],
                    priority: 7
                }
            },
            
            // 旅行信息字段匹配
            travelInfo: {
                destination: {
                    selectors: [
                        'input[name*="destination"]',
                        'select[name*="destination"]',
                        'input[name*="place"]',
                        'input[name*="location"]'
                    ],
                    keywords: ['目的地', 'destination', 'place', 'location'],
                    priority: 8
                },
                entryDate: {
                    selectors: [
                        'input[name*="entry"]',
                        'input[name*="arrival"]',
                        'input[name*="checkin"]',
                        'input[id*="entry"]'
                    ],
                    keywords: ['入境', 'entry', 'arrival', 'checkin', '到达'],
                    priority: 9
                },
                exitDate: {
                    selectors: [
                        'input[name*="exit"]',
                        'input[name*="departure"]',
                        'input[name*="checkout"]',
                        'input[id*="exit"]'
                    ],
                    keywords: ['离境', 'exit', 'departure', 'checkout', '离开'],
                    priority: 9
                },
                accommodation: {
                    selectors: [
                        'input[name*="hotel"]',
                        'input[name*="accommodation"]',
                        'textarea[name*="hotel"]',
                        'input[name*="stay"]'
                    ],
                    keywords: ['住宿', 'hotel', 'accommodation', 'stay', '酒店'],
                    priority: 7
                }
            },
            
            // 交通信息字段匹配
            transportInfo: {
                flightNumber: {
                    selectors: [
                        'input[name*="flight"]',
                        'input[id*="flight"]',
                        'input[name*="airline"]',
                        'input[placeholder*="航班"]'
                    ],
                    keywords: ['航班', 'flight', 'airline', '班机'],
                    priority: 8
                },
                vehicleNumber: {
                    selectors: [
                        'input[name*="vehicle"]',
                        'input[name*="plate"]',
                        'input[name*="car"]',
                        'input[id*="vehicle"]'
                    ],
                    keywords: ['车牌', 'vehicle', 'plate', 'car', '车号'],
                    priority: 8
                },
                modeOfTravel: {
                    selectors: [
                        'select[name*="travel"]',
                        'select[name*="transport"]',
                        'input[name*="mode"]',
                        'select[name*="mode"]'
                    ],
                    keywords: ['交通方式', 'travel', 'transport', 'mode'],
                    priority: 7
                }
            }
        };

        // 匹配缓存
        this.fieldCache = new Map();
        this.lastScanTime = 0;
        this.cacheTimeout = 30000; // 30秒缓存

        // 匹配统计
        this.matchingStats = {
            totalScans: 0,
            successfulMatches: 0,
            failedMatches: 0,
            cacheHits: 0
        };

        console.log('🎯 [FieldMatcher] 字段匹配器已初始化');
    }

    /**
     * 扫描并匹配表单字段
     * @param {Object} options - 扫描选项
     */
    async scanAndMatchFields(options = {}) {
        try {
            console.log('🔍 [FieldMatcher] 开始扫描表单字段');

            const {
                useCache = true,
                forceRescan = false,
                targetUrl = null
            } = options;

            this.matchingStats.totalScans++;

            // 检查缓存
            if (useCache && !forceRescan && this.isCacheValid()) {
                console.log('📋 [FieldMatcher] 使用缓存的字段匹配结果');
                this.matchingStats.cacheHits++;
                return this.fieldCache.get('lastScanResult');
            }

            // 发布扫描开始事件
            if (this.eventBus) {
                this.eventBus.emit('fieldMatcher:scan-start', {
                    timestamp: Date.now(),
                    targetUrl
                });
            }

            // 获取当前页面的表单字段
            const detectedFields = await this.detectFormFields(targetUrl);
            
            // 执行字段匹配
            const matchedFields = this.matchFieldsToData(detectedFields);
            
            // 验证匹配结果
            const validationResult = this.validateMatches(matchedFields);

            const result = {
                success: true,
                detectedFields: detectedFields,
                matchedFields: matchedFields,
                validation: validationResult,
                timestamp: Date.now(),
                scanDuration: Date.now() - this.lastScanTime
            };

            // 更新缓存
            this.updateCache(result);
            
            // 更新统计
            this.matchingStats.successfulMatches++;

            console.log('✅ [FieldMatcher] 字段匹配完成', {
                detectedCount: detectedFields.length,
                matchedCount: Object.keys(matchedFields).length,
                validationScore: validationResult.score
            });

            // 发布扫描完成事件
            if (this.eventBus) {
                this.eventBus.emit('fieldMatcher:scan-complete', result);
            }

            return result;

        } catch (error) {
            console.error('❌ [FieldMatcher] 字段匹配失败', error);
            
            this.matchingStats.failedMatches++;

            const errorResult = {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };

            // 发布扫描错误事件
            if (this.eventBus) {
                this.eventBus.emit('fieldMatcher:scan-error', errorResult);
            }

            return errorResult;
        }
    }

    /**
     * 检测表单字段
     * @param {string} targetUrl - 目标URL
     */
    async detectFormFields(targetUrl = null) {
        // 发送消息到content script获取表单字段
        const response = await chrome.tabs.query({ active: true, currentWindow: true });
        const activeTab = response[0];

        if (!activeTab) {
            throw new Error('无法获取当前活跃标签页');
        }

        // 检查是否是MDAC网站
        if (!activeTab.url.includes('imigresen-online.imi.gov.my')) {
            throw new Error('当前页面不是MDAC网站');
        }

        try {
            const fieldData = await chrome.tabs.sendMessage(activeTab.id, {
                action: 'detectFormFields',
                options: {
                    includeHidden: false,
                    includeDisabled: false,
                    includeReadonly: false
                }
            });

            if (!fieldData || !fieldData.success) {
                throw new Error(fieldData?.error || '字段检测失败');
            }

            return fieldData.fields || [];

        } catch (error) {
            console.error('❌ [FieldMatcher] 表单字段检测失败', error);
            throw new Error(`字段检测失败: ${error.message}`);
        }
    }

    /**
     * 匹配字段到数据
     * @param {Array} detectedFields - 检测到的字段
     */
    matchFieldsToData(detectedFields) {
        const matchedFields = {};

        // 遍历所有数据类别
        Object.entries(this.matchingRules).forEach(([category, fields]) => {
            matchedFields[category] = {};

            // 遍历类别中的每个字段
            Object.entries(fields).forEach(([fieldName, rule]) => {
                const bestMatch = this.findBestMatch(detectedFields, rule, fieldName);
                
                if (bestMatch) {
                    matchedFields[category][fieldName] = {
                        element: bestMatch.element,
                        confidence: bestMatch.confidence,
                        matchType: bestMatch.matchType,
                        selector: bestMatch.selector
                    };
                }
            });
        });

        return matchedFields;
    }

    /**
     * 寻找最佳匹配
     * @param {Array} detectedFields - 检测到的字段
     * @param {Object} rule - 匹配规则
     * @param {string} fieldName - 字段名称
     */
    findBestMatch(detectedFields, rule, fieldName) {
        let bestMatch = null;
        let highestScore = 0;

        detectedFields.forEach(field => {
            const score = this.calculateMatchScore(field, rule, fieldName);
            
            if (score > highestScore && score > 0.3) { // 最低置信度阈值
                highestScore = score;
                bestMatch = {
                    element: field,
                    confidence: score,
                    matchType: this.getMatchType(field, rule),
                    selector: this.generateSelector(field)
                };
            }
        });

        return bestMatch;
    }

    /**
     * 计算匹配分数
     * @param {Object} field - 字段信息
     * @param {Object} rule - 匹配规则
     * @param {string} fieldName - 字段名称
     */
    calculateMatchScore(field, rule, fieldName) {
        let score = 0;

        // 选择器匹配 (权重: 40%)
        const selectorScore = this.calculateSelectorScore(field, rule.selectors);
        score += selectorScore * 0.4;

        // 关键词匹配 (权重: 35%)
        const keywordScore = this.calculateKeywordScore(field, rule.keywords, fieldName);
        score += keywordScore * 0.35;

        // 类型匹配 (权重: 15%)
        const typeScore = this.calculateTypeScore(field, fieldName);
        score += typeScore * 0.15;

        // 位置和上下文匹配 (权重: 10%)
        const contextScore = this.calculateContextScore(field, rule.keywords);
        score += contextScore * 0.1;

        return Math.min(score, 1.0); // 确保分数不超过1
    }

    /**
     * 计算选择器匹配分数
     * @param {Object} field - 字段信息
     * @param {Array} selectors - 选择器数组
     */
    calculateSelectorScore(field, selectors) {
        for (const selector of selectors) {
            if (this.matchesSelector(field, selector)) {
                return 1.0;
            }
        }
        return 0;
    }

    /**
     * 计算关键词匹配分数
     * @param {Object} field - 字段信息
     * @param {Array} keywords - 关键词数组
     * @param {string} fieldName - 字段名称
     */
    calculateKeywordScore(field, keywords, fieldName) {
        const searchText = [
            field.name || '',
            field.id || '',
            field.placeholder || '',
            field.label || '',
            fieldName
        ].join(' ').toLowerCase();

        let maxScore = 0;
        
        keywords.forEach(keyword => {
            const keywordLower = keyword.toLowerCase();
            
            if (searchText.includes(keywordLower)) {
                // 精确匹配得分更高
                if (searchText === keywordLower) {
                    maxScore = Math.max(maxScore, 1.0);
                } else if (searchText.startsWith(keywordLower) || searchText.endsWith(keywordLower)) {
                    maxScore = Math.max(maxScore, 0.8);
                } else {
                    maxScore = Math.max(maxScore, 0.6);
                }
            }
        });

        return maxScore;
    }

    /**
     * 计算类型匹配分数
     * @param {Object} field - 字段信息
     * @param {string} fieldName - 字段名称
     */
    calculateTypeScore(field, fieldName) {
        const typeMapping = {
            email: ['email'],
            phone: ['tel'],
            birthDate: ['date'],
            entryDate: ['date'],
            exitDate: ['date']
        };

        if (typeMapping[fieldName] && typeMapping[fieldName].includes(field.type)) {
            return 1.0;
        }

        // 特殊类型匹配
        if (fieldName === 'gender' && (field.type === 'radio' || field.tagName === 'SELECT')) {
            return 0.8;
        }

        if (fieldName.includes('Date') && field.type === 'text') {
            return 0.5; // 文本输入也可能是日期
        }

        return 0;
    }

    /**
     * 计算上下文匹配分数
     * @param {Object} field - 字段信息
     * @param {Array} keywords - 关键词数组
     */
    calculateContextScore(field, keywords) {
        // 检查相邻元素的文本内容
        const contextText = (field.context || '').toLowerCase();
        
        let score = 0;
        keywords.forEach(keyword => {
            if (contextText.includes(keyword.toLowerCase())) {
                score = Math.max(score, 0.7);
            }
        });

        return score;
    }

    /**
     * 检查字段是否匹配选择器
     * @param {Object} field - 字段信息
     * @param {string} selector - CSS选择器
     */
    matchesSelector(field, selector) {
        // 简化的选择器匹配逻辑
        if (selector.includes('[name*=')) {
            const namePattern = selector.match(/\[name\*="([^"]+)"\]/);
            if (namePattern && field.name) {
                return field.name.toLowerCase().includes(namePattern[1].toLowerCase());
            }
        }

        if (selector.includes('[id*=')) {
            const idPattern = selector.match(/\[id\*="([^"]+)"\]/);
            if (idPattern && field.id) {
                return field.id.toLowerCase().includes(idPattern[1].toLowerCase());
            }
        }

        if (selector.includes('[type=')) {
            const typePattern = selector.match(/\[type="([^"]+)"\]/);
            if (typePattern) {
                return field.type === typePattern[1];
            }
        }

        if (selector.includes('[placeholder*=')) {
            const placeholderPattern = selector.match(/\[placeholder\*="([^"]+)"\]/);
            if (placeholderPattern && field.placeholder) {
                return field.placeholder.toLowerCase().includes(placeholderPattern[1].toLowerCase());
            }
        }

        // 标签名匹配
        if (selector.startsWith('input') && field.tagName === 'INPUT') {
            return true;
        }
        if (selector.startsWith('select') && field.tagName === 'SELECT') {
            return true;
        }
        if (selector.startsWith('textarea') && field.tagName === 'TEXTAREA') {
            return true;
        }

        return false;
    }

    /**
     * 获取匹配类型
     * @param {Object} field - 字段信息
     * @param {Object} rule - 匹配规则
     */
    getMatchType(field, rule) {
        // 检查是通过什么方式匹配的
        for (const selector of rule.selectors) {
            if (this.matchesSelector(field, selector)) {
                return 'selector';
            }
        }

        const searchText = [field.name, field.id, field.placeholder, field.label].join(' ').toLowerCase();
        for (const keyword of rule.keywords) {
            if (searchText.includes(keyword.toLowerCase())) {
                return 'keyword';
            }
        }

        return 'unknown';
    }

    /**
     * 生成CSS选择器
     * @param {Object} field - 字段信息
     */
    generateSelector(field) {
        if (field.id) {
            return `#${field.id}`;
        }
        if (field.name) {
            return `${field.tagName.toLowerCase()}[name="${field.name}"]`;
        }
        return field.selector || '';
    }

    /**
     * 验证匹配结果
     * @param {Object} matchedFields - 匹配的字段
     */
    validateMatches(matchedFields) {
        const validation = {
            score: 0,
            coverage: 0,
            confidence: 0,
            issues: [],
            recommendations: []
        };

        let totalFields = 0;
        let matchedCount = 0;
        let totalConfidence = 0;

        // 计算覆盖率和置信度
        Object.entries(this.matchingRules).forEach(([category, fields]) => {
            Object.entries(fields).forEach(([fieldName, rule]) => {
                totalFields++;
                
                if (matchedFields[category] && matchedFields[category][fieldName]) {
                    matchedCount++;
                    totalConfidence += matchedFields[category][fieldName].confidence;
                    
                    // 检查低置信度匹配
                    if (matchedFields[category][fieldName].confidence < 0.6) {
                        validation.issues.push(`${category}.${fieldName} 匹配置信度较低 (${(matchedFields[category][fieldName].confidence * 100).toFixed(1)}%)`);
                    }
                } else if (rule.priority >= 8) {
                    validation.issues.push(`未找到重要字段: ${category}.${fieldName}`);
                }
            });
        });

        validation.coverage = matchedCount / totalFields;
        validation.confidence = matchedCount > 0 ? totalConfidence / matchedCount : 0;
        validation.score = (validation.coverage * 0.6) + (validation.confidence * 0.4);

        // 生成建议
        if (validation.coverage < 0.5) {
            validation.recommendations.push('字段覆盖率较低，建议检查页面是否正确加载');
        }
        if (validation.confidence < 0.7) {
            validation.recommendations.push('匹配置信度较低，建议手动验证字段映射');
        }

        return validation;
    }

    /**
     * 检查缓存是否有效
     */
    isCacheValid() {
        const now = Date.now();
        return this.fieldCache.has('lastScanResult') && 
               (now - this.lastScanTime) < this.cacheTimeout;
    }

    /**
     * 更新缓存
     * @param {Object} result - 扫描结果
     */
    updateCache(result) {
        this.fieldCache.set('lastScanResult', result);
        this.lastScanTime = Date.now();
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.fieldCache.clear();
        this.lastScanTime = 0;
        
        console.log('🧹 [FieldMatcher] 缓存已清除');
        
        if (this.eventBus) {
            this.eventBus.emit('fieldMatcher:cache-cleared', { timestamp: Date.now() });
        }
    }

    /**
     * 获取匹配统计
     */
    getMatchingStats() {
        return {
            ...this.matchingStats,
            cacheSize: this.fieldCache.size,
            lastScanTime: this.lastScanTime,
            cacheValid: this.isCacheValid()
        };
    }

    /**
     * 添加自定义匹配规则
     * @param {string} category - 类别
     * @param {string} fieldName - 字段名
     * @param {Object} rule - 匹配规则
     */
    addMatchingRule(category, fieldName, rule) {
        if (!this.matchingRules[category]) {
            this.matchingRules[category] = {};
        }
        
        this.matchingRules[category][fieldName] = rule;
        
        console.log(`🎯 [FieldMatcher] 添加匹配规则: ${category}.${fieldName}`);
    }

    /**
     * 销毁字段匹配器
     */
    destroy() {
        // 清除缓存
        this.clearCache();
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('fieldMatcher:scan-start');
            this.eventBus.off('fieldMatcher:scan-complete');
            this.eventBus.off('fieldMatcher:scan-error');
        }

        console.log('🗑️ [FieldMatcher] 字段匹配器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FieldMatcher;
} else {
    window.FieldMatcher = FieldMatcher;
}

console.log('✅ [FieldMatcher] 字段匹配器模块已加载');
