/**
 * 修复版本的模块加载器 - 解决时序问题
 * 主要修复：模块验证时序和全局对象注册问题
 */

(function() {
    'use strict';

    // 防止多次执行
    if (window.mdacFixedBootstrap) {
        console.warn('⚠️ [FixedBootstrap] 修复版引导程序已存在，跳过重复加载');
        return;
    }

    class FixedModuleBootstrap {
        constructor() {
            this.loadedModules = new Set();
            this.errors = [];
            console.log('🔧 [FixedBootstrap] 修复版模块加载器启动');
        }

        /**
         * 模块配置 - 简化版本，只包含核心模块
         */
        getCoreModules() {
            return [
                {
                    name: 'EventBus',
                    path: 'sidepanel/core/EventBus-fixed.js',
                    dependencies: []
                },
                {
                    name: 'DebugLogger',
                    path: 'sidepanel/utils/DebugLogger.js',
                    dependencies: ['EventBus']
                },
                {
                    name: 'StateManager',
                    path: 'sidepanel/core/StateManager.js',
                    dependencies: ['EventBus']
                },
                {
                    name: 'EventManager',
                    path: 'sidepanel/core/EventManager.js',
                    dependencies: ['EventBus', 'StateManager']
                },
                {
                    name: 'SidePanelCore',
                    path: 'sidepanel/core/SidePanelCore.js',
                    dependencies: ['EventBus', 'StateManager', 'EventManager']
                }
            ];
        }

        /**
         * 检查模块是否已加载
         */
        isModuleLoaded(moduleName) {
            try {
                return window[moduleName] !== undefined && typeof window[moduleName] === 'function';
            } catch (error) {
                return false;
            }
        }

        /**
         * 等待模块加载完成
         */
        async waitForModule(moduleName, maxWait = 5000) {
            const startTime = Date.now();
            
            while (Date.now() - startTime < maxWait) {
                if (this.isModuleLoaded(moduleName)) {
                    console.log(`✅ [FixedBootstrap] 模块 ${moduleName} 验证成功`);
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.error(`❌ [FixedBootstrap] 模块 ${moduleName} 验证超时`);
            return false;
        }

        /**
         * 加载单个脚本
         */
        async loadScript(path, moduleName) {
            return new Promise((resolve, reject) => {
                // 检查是否已有脚本
                const existingScript = document.querySelector(`script[src="${path}"]`);
                if (existingScript) {
                    console.log(`📄 [FixedBootstrap] 脚本已存在: ${path}`);
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = path;
                script.async = false;

                const timeout = setTimeout(() => {
                    reject(new Error(`脚本加载超时: ${path}`));
                }, 10000);

                script.onload = async () => {
                    clearTimeout(timeout);
                    console.log(`📄 [FixedBootstrap] 脚本加载完成: ${path}`);
                    
                    // 等待模块注册到全局对象
                    const success = await this.waitForModule(moduleName);
                    if (success) {
                        this.loadedModules.add(moduleName);
                        resolve();
                    } else {
                        reject(new Error(`模块 ${moduleName} 未正确注册`));
                    }
                };

                script.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(new Error(`脚本加载失败: ${path}`));
                };

                document.head.appendChild(script);
            });
        }

        /**
         * 按依赖顺序加载模块
         */
        async loadModules() {
            console.log('🚀 [FixedBootstrap] 开始加载核心模块');
            const modules = this.getCoreModules();

            for (const module of modules) {
                try {
                    // 检查依赖
                    for (const dep of module.dependencies) {
                        if (!this.loadedModules.has(dep)) {
                            throw new Error(`依赖模块 ${dep} 未加载`);
                        }
                    }

                    // 跳过已加载的模块
                    if (this.loadedModules.has(module.name)) {
                        console.log(`✅ [FixedBootstrap] 模块 ${module.name} 已加载`);
                        continue;
                    }

                    console.log(`🔄 [FixedBootstrap] 加载模块: ${module.name}`);
                    await this.loadScript(module.path, module.name);
                    
                } catch (error) {
                    console.error(`❌ [FixedBootstrap] 模块 ${module.name} 加载失败:`, error);
                    this.errors.push({
                        module: module.name,
                        error: error.message
                    });
                    // 核心模块失败则停止
                    throw error;
                }
            }

            console.log('✅ [FixedBootstrap] 所有核心模块加载完成');
            await this.initializeApplication();
        }

        /**
         * 初始化应用程序
         */
        async initializeApplication() {
            console.log('🏗️ [FixedBootstrap] 初始化应用程序');

            try {
                // 确保全局事件总线实例存在
                if (window.EventBus && !window.mdacEventBus) {
                    window.mdacEventBus = new window.EventBus();
                    console.log('🚌 [FixedBootstrap] 全局事件总线已创建');
                }

                // 验证事件总线
                if (!window.mdacEventBus) {
                    throw new Error('事件总线未创建成功');
                }

                // 创建状态管理器实例
                if (window.StateManager && window.mdacEventBus && !window.mdacStateManager) {
                    window.mdacStateManager = new window.StateManager(window.mdacEventBus);
                    console.log('📊 [FixedBootstrap] 状态管理器已创建');
                }

                // 创建侧边栏核心实例
                if (window.SidePanelCore && !window.mdacSidePanelCore) {
                    window.mdacSidePanelCore = new window.SidePanelCore();
                    console.log('🏗️ [FixedBootstrap] 侧边栏核心已创建');
                    
                    // 初始化侧边栏
                    if (window.mdacSidePanelCore.initialize) {
                        await window.mdacSidePanelCore.initialize();
                        console.log('✅ [FixedBootstrap] 侧边栏初始化完成');
                    }
                }

                console.log('🎉 [FixedBootstrap] 应用程序初始化成功');
                this.showSuccessMessage();

            } catch (error) {
                console.error('❌ [FixedBootstrap] 应用程序初始化失败:', error);
                this.showErrorMessage(error.message);
            }
        }

        /**
         * 显示成功消息
         */
        showSuccessMessage() {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            `;
            message.innerHTML = `
                <strong>✅ MDAC AI助手</strong><br>
                <small>系统已成功加载</small>
            `;
            document.body.appendChild(message);

            // 3秒后自动隐藏
            setTimeout(() => {
                if (message.parentNode) {
                    message.parentNode.removeChild(message);
                }
            }, 3000);
        }

        /**
         * 显示错误消息
         */
        showErrorMessage(errorMsg) {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: #dc3545;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                max-width: 300px;
            `;
            message.innerHTML = `
                <strong>❌ 加载失败</strong><br>
                <small>${errorMsg}</small><br>
                <button onclick="location.reload()" style="
                    background: white;
                    color: #dc3545;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    margin-top: 5px;
                    cursor: pointer;
                    font-size: 12px;
                ">刷新页面</button>
            `;
            document.body.appendChild(message);
        }

        /**
         * 获取加载统计
         */
        getStats() {
            return {
                loadedModules: Array.from(this.loadedModules),
                errors: this.errors,
                totalLoaded: this.loadedModules.size,
                hasErrors: this.errors.length > 0
            };
        }
    }

    // 创建全局实例
    window.mdacFixedBootstrap = new FixedModuleBootstrap();

    // DOM 就绪后开始加载
    function startLoading() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    window.mdacFixedBootstrap.loadModules().catch(error => {
                        console.error('💥 [FixedBootstrap] 加载失败:', error);
                    });
                }, 100);
            });
        } else {
            setTimeout(() => {
                window.mdacFixedBootstrap.loadModules().catch(error => {
                    console.error('💥 [FixedBootstrap] 加载失败:', error);
                });
            }, 100);
        }
    }

    startLoading();
    console.log('🔧 [FixedBootstrap] 修复版模块引导程序已加载');

})();
