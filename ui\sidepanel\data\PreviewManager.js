/**
 * 预览管理器 - 数据预览和展示管理
 * 负责生成和管理各种数据预览格式
 * 创建日期: 2025-01-11
 */

class PreviewManager {
    constructor(eventBus = window.mdacEventBus, modalManager = null, dataManager = null) {
        this.eventBus = eventBus;
        this.modalManager = modalManager;
        this.dataManager = dataManager;
        
        // 预览配置
        this.config = {
            enableRealTimePreview: true,
            enableExport: true,
            enablePrint: true,
            defaultFormat: 'table',
            maxPreviewSize: 1000000, // 1MB
            enableSyntaxHighlight: true
        };

        // 预览格式
        this.formats = {
            table: this.generateTablePreview.bind(this),
            json: this.generateJSONPreview.bind(this),
            form: this.generateFormPreview.bind(this),
            card: this.generateCardPreview.bind(this),
            list: this.generateListPreview.bind(this),
            raw: this.generateRawPreview.bind(this)
        };

        // 预览历史
        this.previewHistory = [];
        this.maxHistorySize = 20;
        
        // 当前预览
        this.currentPreview = null;
        
        // 预览统计
        this.stats = {
            totalPreviews: 0,
            exportCount: 0,
            printCount: 0,
            lastPreviewTime: null
        };

        console.log('👁️ [PreviewManager] 预览管理器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听预览请求
        this.eventBus.on('preview:show', (data) => {
            this.showPreview(data);
        });

        this.eventBus.on('preview:generate', (data) => {
            this.generatePreview(data);
        });

        this.eventBus.on('preview:export', (data) => {
            this.exportPreview(data);
        });

        this.eventBus.on('preview:print', (data) => {
            this.printPreview(data);
        });

        // 监听数据变化
        this.eventBus.on('data:changed', (data) => {
            this.handleDataChange(data);
        });

        // 监听AI处理完成
        this.eventBus.on('ai:processing-complete', (result) => {
            this.handleAIResult(result);
        });
    }

    /**
     * 显示预览
     * @param {Object} options - 预览选项
     */
    showPreview(options = {}) {
        try {
            const {
                data = null,
                format = this.config.defaultFormat,
                title = '数据预览',
                width = '800px',
                height = '600px',
                enableExport = this.config.enableExport,
                enablePrint = this.config.enablePrint
            } = options;

            console.log('👁️ [PreviewManager] 显示预览', { format, title });

            // 获取数据
            const previewData = data || this.getPreviewData();
            if (!previewData) {
                throw new Error('没有可预览的数据');
            }

            // 生成预览内容
            const previewContent = this.generatePreview({
                data: previewData,
                format: format
            });

            // 创建预览界面
            const content = this.createPreviewInterface(previewContent, {
                format,
                enableExport,
                enablePrint
            });

            // 显示模态框
            if (this.modalManager) {
                const modal = this.modalManager.showModal('custom', {
                    title: title,
                    content: content,
                    width: width,
                    height: height,
                    showHeader: true,
                    showFooter: true,
                    buttons: this.createPreviewButtons(enableExport, enablePrint)
                });

                // 绑定预览事件
                this.bindPreviewEvents(modal, previewData, format);

                // 保存当前预览
                this.currentPreview = {
                    data: previewData,
                    format: format,
                    modal: modal,
                    timestamp: Date.now()
                };

                return modal;
            } else {
                console.warn('⚠️ [PreviewManager] ModalManager不可用');
                return null;
            }

        } catch (error) {
            console.error('❌ [PreviewManager] 预览显示失败', error);
            
            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('preview:error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 生成预览
     * @param {Object} options - 生成选项
     */
    generatePreview(options = {}) {
        try {
            const {
                data,
                format = this.config.defaultFormat
            } = options;

            console.log(`👁️ [PreviewManager] 生成预览: ${format}`);

            if (!data) {
                return this.createEmptyPreview();
            }

            // 检查数据大小
            const dataSize = JSON.stringify(data).length;
            if (dataSize > this.config.maxPreviewSize) {
                console.warn('⚠️ [PreviewManager] 数据过大，使用简化预览');
                return this.createLargeDataPreview(data, dataSize);
            }

            // 生成预览
            const generator = this.formats[format];
            if (!generator) {
                throw new Error(`不支持的预览格式: ${format}`);
            }

            const preview = generator(data);

            // 更新统计
            this.stats.totalPreviews++;
            this.stats.lastPreviewTime = Date.now();

            // 保存到历史
            this.saveToHistory(data, format, preview);

            // 发布预览生成事件
            if (this.eventBus) {
                this.eventBus.emit('preview:generated', {
                    format: format,
                    dataSize: dataSize,
                    timestamp: Date.now()
                });
            }

            return preview;

        } catch (error) {
            console.error('❌ [PreviewManager] 预览生成失败', error);
            return this.createErrorPreview(error.message);
        }
    }

    /**
     * 创建预览界面
     * @param {string} content - 预览内容
     * @param {Object} options - 选项
     */
    createPreviewInterface(content, options = {}) {
        const {
            format,
            enableExport,
            enablePrint
        } = options;

        return `
            <div class="preview-manager">
                <div class="preview-header">
                    <div class="preview-controls">
                        <div class="format-selector">
                            <label>预览格式:</label>
                            <select id="preview-format-select">
                                <option value="table" ${format === 'table' ? 'selected' : ''}>表格</option>
                                <option value="json" ${format === 'json' ? 'selected' : ''}>JSON</option>
                                <option value="form" ${format === 'form' ? 'selected' : ''}>表单</option>
                                <option value="card" ${format === 'card' ? 'selected' : ''}>卡片</option>
                                <option value="list" ${format === 'list' ? 'selected' : ''}>列表</option>
                                <option value="raw" ${format === 'raw' ? 'selected' : ''}>原始</option>
                            </select>
                        </div>
                        
                        <div class="preview-actions">
                            <button class="preview-button" id="refresh-preview" title="刷新预览">
                                🔄 刷新
                            </button>
                            ${enableExport ? `
                                <button class="preview-button" id="export-preview" title="导出数据">
                                    💾 导出
                                </button>
                            ` : ''}
                            ${enablePrint ? `
                                <button class="preview-button" id="print-preview" title="打印预览">
                                    🖨️ 打印
                                </button>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="preview-info">
                        <span id="preview-stats">数据预览</span>
                    </div>
                </div>
                
                <div class="preview-content" id="preview-content">
                    ${content}
                </div>
                
                <div class="preview-footer">
                    <div class="preview-metadata">
                        <span>生成时间: ${new Date().toLocaleString()}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成表格预览
     * @param {Object} data - 数据
     */
    generateTablePreview(data) {
        try {
            let html = '<div class="table-preview">';

            // 遍历数据类别
            Object.entries(data).forEach(([category, fields]) => {
                if (!fields || typeof fields !== 'object') return;

                html += `
                    <div class="table-section">
                        <h3 class="section-title">${this.formatCategoryName(category)}</h3>
                        <table class="preview-table">
                            <thead>
                                <tr>
                                    <th>字段</th>
                                    <th>值</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                Object.entries(fields).forEach(([fieldName, value]) => {
                    const status = this.getFieldStatus(value);
                    const displayValue = this.formatDisplayValue(value);

                    html += `
                        <tr class="field-row ${status.class}">
                            <td class="field-name">${this.formatFieldName(fieldName)}</td>
                            <td class="field-value">${displayValue}</td>
                            <td class="field-status">
                                <span class="status-indicator ${status.class}">${status.text}</span>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            });

            html += '</div>';
            return html;

        } catch (error) {
            console.error('❌ [PreviewManager] 表格预览生成失败', error);
            return this.createErrorPreview('表格预览生成失败');
        }
    }

    /**
     * 生成JSON预览
     * @param {Object} data - 数据
     */
    generateJSONPreview(data) {
        try {
            const jsonString = JSON.stringify(data, null, 2);
            
            let html = '<div class="json-preview">';
            
            if (this.config.enableSyntaxHighlight) {
                html += `<pre class="json-content highlighted">${this.highlightJSON(jsonString)}</pre>`;
            } else {
                html += `<pre class="json-content">${this.escapeHtml(jsonString)}</pre>`;
            }
            
            html += '</div>';
            return html;

        } catch (error) {
            console.error('❌ [PreviewManager] JSON预览生成失败', error);
            return this.createErrorPreview('JSON预览生成失败');
        }
    }

    /**
     * 生成表单预览
     * @param {Object} data - 数据
     */
    generateFormPreview(data) {
        try {
            let html = '<div class="form-preview">';

            Object.entries(data).forEach(([category, fields]) => {
                if (!fields || typeof fields !== 'object') return;

                html += `
                    <div class="form-section">
                        <h3 class="section-title">${this.formatCategoryName(category)}</h3>
                        <div class="form-fields">
                `;

                Object.entries(fields).forEach(([fieldName, value]) => {
                    const displayValue = this.formatDisplayValue(value);
                    const fieldType = this.getFieldType(fieldName);

                    html += `
                        <div class="form-field">
                            <label class="field-label">${this.formatFieldName(fieldName)}</label>
                            <div class="field-input-container">
                                <input type="${fieldType}" 
                                       class="field-input" 
                                       value="${this.escapeHtml(displayValue)}" 
                                       readonly>
                                <span class="field-copy" data-value="${this.escapeHtml(displayValue)}" title="复制">📋</span>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;

        } catch (error) {
            console.error('❌ [PreviewManager] 表单预览生成失败', error);
            return this.createErrorPreview('表单预览生成失败');
        }
    }

    /**
     * 生成卡片预览
     * @param {Object} data - 数据
     */
    generateCardPreview(data) {
        try {
            let html = '<div class="card-preview">';

            Object.entries(data).forEach(([category, fields]) => {
                if (!fields || typeof fields !== 'object') return;

                html += `
                    <div class="preview-card">
                        <div class="card-header">
                            <h3 class="card-title">${this.formatCategoryName(category)}</h3>
                            <span class="card-count">${Object.keys(fields).length} 个字段</span>
                        </div>
                        <div class="card-content">
                `;

                Object.entries(fields).forEach(([fieldName, value]) => {
                    const displayValue = this.formatDisplayValue(value);
                    const status = this.getFieldStatus(value);

                    html += `
                        <div class="card-field ${status.class}">
                            <div class="field-header">
                                <span class="field-name">${this.formatFieldName(fieldName)}</span>
                                <span class="field-status ${status.class}">${status.text}</span>
                            </div>
                            <div class="field-value">${displayValue}</div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;

        } catch (error) {
            console.error('❌ [PreviewManager] 卡片预览生成失败', error);
            return this.createErrorPreview('卡片预览生成失败');
        }
    }

    /**
     * 生成列表预览
     * @param {Object} data - 数据
     */
    generateListPreview(data) {
        try {
            let html = '<div class="list-preview">';

            Object.entries(data).forEach(([category, fields]) => {
                if (!fields || typeof fields !== 'object') return;

                html += `
                    <div class="list-section">
                        <h3 class="section-title">${this.formatCategoryName(category)}</h3>
                        <ul class="field-list">
                `;

                Object.entries(fields).forEach(([fieldName, value]) => {
                    const displayValue = this.formatDisplayValue(value);
                    const status = this.getFieldStatus(value);

                    html += `
                        <li class="list-item ${status.class}">
                            <div class="item-content">
                                <strong>${this.formatFieldName(fieldName)}:</strong>
                                <span class="item-value">${displayValue}</span>
                            </div>
                            <span class="item-status ${status.class}">${status.text}</span>
                        </li>
                    `;
                });

                html += `
                        </ul>
                    </div>
                `;
            });

            html += '</div>';
            return html;

        } catch (error) {
            console.error('❌ [PreviewManager] 列表预览生成失败', error);
            return this.createErrorPreview('列表预览生成失败');
        }
    }

    /**
     * 生成原始预览
     * @param {Object} data - 数据
     */
    generateRawPreview(data) {
        try {
            const rawString = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            
            return `
                <div class="raw-preview">
                    <pre class="raw-content">${this.escapeHtml(rawString)}</pre>
                </div>
            `;

        } catch (error) {
            console.error('❌ [PreviewManager] 原始预览生成失败', error);
            return this.createErrorPreview('原始预览生成失败');
        }
    }

    /**
     * 创建空预览
     */
    createEmptyPreview() {
        return `
            <div class="empty-preview">
                <div class="empty-icon">📋</div>
                <div class="empty-title">暂无数据</div>
                <div class="empty-message">没有可预览的数据</div>
            </div>
        `;
    }

    /**
     * 创建大数据预览
     * @param {Object} data - 数据
     * @param {number} size - 数据大小
     */
    createLargeDataPreview(data, size) {
        const sizeText = this.formatFileSize(size);
        const summary = this.generateDataSummary(data);

        return `
            <div class="large-data-preview">
                <div class="size-warning">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-text">
                        <strong>数据过大 (${sizeText})</strong>
                        <p>显示数据摘要以提高性能</p>
                    </div>
                </div>
                <div class="data-summary">
                    ${summary}
                </div>
            </div>
        `;
    }

    /**
     * 创建错误预览
     * @param {string} message - 错误消息
     */
    createErrorPreview(message) {
        return `
            <div class="error-preview">
                <div class="error-icon">❌</div>
                <div class="error-title">预览生成失败</div>
                <div class="error-message">${this.escapeHtml(message)}</div>
            </div>
        `;
    }

    /**
     * 创建预览按钮
     * @param {boolean} enableExport - 启用导出
     * @param {boolean} enablePrint - 启用打印
     */
    createPreviewButtons(enableExport, enablePrint) {
        const buttons = [
            {
                text: '关闭',
                action: 'close',
                class: 'secondary'
            }
        ];

        if (enableExport) {
            buttons.unshift({
                text: '导出',
                action: 'export',
                class: 'primary',
                value: 'export'
            });
        }

        if (enablePrint) {
            buttons.unshift({
                text: '打印',
                action: 'print',
                class: 'secondary',
                value: 'print'
            });
        }

        return buttons;
    }

    /**
     * 绑定预览事件
     * @param {Object} modal - 模态框对象
     * @param {Object} data - 数据
     * @param {string} format - 格式
     */
    bindPreviewEvents(modal, data, format) {
        const modalElement = modal.element;

        // 格式切换
        const formatSelect = modalElement.querySelector('#preview-format-select');
        if (formatSelect) {
            formatSelect.addEventListener('change', (e) => {
                const newFormat = e.target.value;
                this.refreshPreview(modalElement, data, newFormat);
            });
        }

        // 刷新按钮
        const refreshButton = modalElement.querySelector('#refresh-preview');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.refreshPreview(modalElement, data, format);
            });
        }

        // 复制按钮
        modalElement.addEventListener('click', (e) => {
            if (e.target.classList.contains('field-copy')) {
                const value = e.target.dataset.value;
                this.copyToClipboard(value);
            }
        });

        // 模态框按钮事件
        modalElement.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            
            if (action === 'export') {
                this.exportPreview({ data, format });
            } else if (action === 'print') {
                this.printPreview({ data, format });
            }
        });
    }

    /**
     * 刷新预览
     * @param {Element} modalElement - 模态框元素
     * @param {Object} data - 数据
     * @param {string} format - 格式
     */
    refreshPreview(modalElement, data, format) {
        const contentElement = modalElement.querySelector('#preview-content');
        if (!contentElement) return;

        try {
            const newContent = this.generatePreview({ data, format });
            contentElement.innerHTML = newContent;

            // 更新格式选择器
            const formatSelect = modalElement.querySelector('#preview-format-select');
            if (formatSelect) {
                formatSelect.value = format;
            }

        } catch (error) {
            console.error('❌ [PreviewManager] 预览刷新失败', error);
            contentElement.innerHTML = this.createErrorPreview('预览刷新失败');
        }
    }

    /**
     * 导出预览
     * @param {Object} options - 导出选项
     */
    exportPreview(options = {}) {
        try {
            const {
                data = this.currentPreview?.data,
                format = 'json',
                filename = `mdac_data_${Date.now()}`
            } = options;

            if (!data) {
                throw new Error('没有可导出的数据');
            }

            console.log('👁️ [PreviewManager] 导出预览', { format, filename });

            let content, mimeType, extension;

            switch (format) {
                case 'json':
                    content = JSON.stringify(data, null, 2);
                    mimeType = 'application/json';
                    extension = 'json';
                    break;
                case 'csv':
                    content = this.convertToCSV(data);
                    mimeType = 'text/csv';
                    extension = 'csv';
                    break;
                case 'txt':
                    content = this.convertToText(data);
                    mimeType = 'text/plain';
                    extension = 'txt';
                    break;
                default:
                    throw new Error(`不支持的导出格式: ${format}`);
            }

            // 创建下载
            this.downloadFile(content, `${filename}.${extension}`, mimeType);

            // 更新统计
            this.stats.exportCount++;

            // 发布导出事件
            if (this.eventBus) {
                this.eventBus.emit('preview:exported', {
                    format: format,
                    filename: filename,
                    timestamp: Date.now()
                });
            }

        } catch (error) {
            console.error('❌ [PreviewManager] 预览导出失败', error);
            
            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('preview:export-error', {
                    error: error.message,
                    timestamp: Date.now()
                });
            }
        }
    }

    /**
     * 打印预览
     * @param {Object} options - 打印选项
     */
    printPreview(options = {}) {
        try {
            const {
                data = this.currentPreview?.data,
                format = 'table'
            } = options;

            if (!data) {
                throw new Error('没有可打印的数据');
            }

            console.log('👁️ [PreviewManager] 打印预览', { format });

            // 生成打印内容
            const printContent = this.generatePreview({ data, format });
            
            // 创建打印窗口
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>MDAC数据预览</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .preview-table { border-collapse: collapse; width: 100%; }
                        .preview-table th, .preview-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        .preview-table th { background-color: #f2f2f2; }
                        .section-title { color: #333; margin-top: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <h1>MDAC数据预览</h1>
                    <p>生成时间: ${new Date().toLocaleString()}</p>
                    ${printContent}
                </body>
                </html>
            `);
            
            printWindow.document.close();
            printWindow.print();

            // 更新统计
            this.stats.printCount++;

            // 发布打印事件
            if (this.eventBus) {
                this.eventBus.emit('preview:printed', {
                    format: format,
                    timestamp: Date.now()
                });
            }

        } catch (error) {
            console.error('❌ [PreviewManager] 预览打印失败', error);
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PreviewManager;
} else {
    window.PreviewManager = PreviewManager;
}

console.log('✅ [PreviewManager] 预览管理器已加载');
