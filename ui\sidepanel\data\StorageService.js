/**
 * 存储服务 - 统一的数据存储和管理
 *
 * 功能概述：
 * - 封装Chrome存储API（sync、local、session）
 * - 提供缓存机制和数据压缩
 * - 支持批量操作和事务处理
 * - 提供数据加密和验证功能
 * - 统计存储使用情况和性能监控
 *
 * 依赖关系：
 * - EventBus: 事件通信
 * - Chrome Storage API: 底层存储
 *
 * 主要模块：
 * - StorageOperations: 基础存储操作
 * - CacheManager: 缓存管理
 * - DataProcessor: 数据处理（压缩/加密）
 * - BatchProcessor: 批量操作处理
 * - UsageMonitor: 使用情况监控
 *
 * 创建日期: 2025-01-11
 * 重构日期: 2025-01-11
 * 版本: 2.0.0 (重构优化版本)
 */

// 防止重复声明
if (typeof StorageService !== 'undefined') {
    console.warn('⚠️ [StorageService] 类已存在，跳过重复声明');
} else {

class StorageService {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 存储配置
        this.config = {
            enableSync: true,
            enableLocal: true,
            enableSession: true,
            enableCache: true,
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 100
        };

        // 缓存系统
        this.cache = new Map();
        this.cacheTimestamps = new Map();
        
        // 存储统计
        this.stats = {
            syncOperations: 0,
            localOperations: 0,
            sessionOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };

        // 存储键前缀
        this.keyPrefixes = {
            config: 'mdac_config_',
            data: 'mdac_data_',
            cache: 'mdac_cache_',
            temp: 'mdac_temp_',
            user: 'mdac_user_'
        };

        console.log('💾 [StorageService] 存储服务已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听存储请求
        this.eventBus.on('storage:set', (data) => {
            this.handleSetRequest(data);
        });

        this.eventBus.on('storage:get', (data) => {
            this.handleGetRequest(data);
        });

        this.eventBus.on('storage:remove', (data) => {
            this.handleRemoveRequest(data);
        });

        this.eventBus.on('storage:clear', (data) => {
            this.handleClearRequest(data);
        });

        // 监听存储变化
        if (chrome.storage && chrome.storage.onChanged) {
            chrome.storage.onChanged.addListener((changes, areaName) => {
                this.handleStorageChange(changes, areaName);
            });
        }
    }

    /**
     * 设置数据到存储
     * @param {string} key - 存储键名
     * @param {*} value - 要存储的值
     * @param {Object} options - 存储选项
     * @param {string} [options.area='sync'] - 存储区域 (sync/local/session)
     * @param {string} [options.prefix='data'] - 键名前缀
     * @param {number|null} [options.ttl=null] - 生存时间(毫秒)
     * @param {boolean} [options.compress=false] - 是否压缩数据
     * @param {boolean} [options.encrypt=false] - 是否加密数据
     * @returns {Promise<boolean>} 操作是否成功
     * @throws {Error} 存储操作失败时抛出错误
     *
     * @example
     * // 基本用法
     * await storageService.set('userConfig', { theme: 'dark' });
     *
     * // 带选项的用法
     * await storageService.set('sensitiveData', data, {
     *   area: 'local',
     *   encrypt: true,
     *   ttl: 3600000 // 1小时
     * });
     */
    async set(key, value, options = {}) {
        try {
            // 解析和验证选项
            const config = this._parseSetOptions(options);
            console.log(`💾 [StorageService] 设置数据: ${key}`, { area: config.area, prefix: config.prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, config.prefix);

            // 处理数据（压缩/加密）
            const processedValue = await this._processValueForStorage(value, config);

            // 创建带元数据的数据对象
            const dataWithMeta = this._createDataWithMetadata(processedValue, config);

            // 执行存储操作
            await this._performStorageSet(config.area, fullKey, dataWithMeta);

            // 后处理操作（缓存、统计、事件）
            this._handleSetPostProcessing(key, fullKey, config.area, dataWithMeta);

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据设置失败', error);
            this.stats.errors++;
            
            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('storage:error', {
                    operation: 'set',
                    key: key,
                    error: error.message,
                    timestamp: Date.now()
                });
            }
            
            throw error;
        }
    }

    /**
     * 解析set操作的选项参数
     * @private
     * @param {Object} options - 原始选项
     * @returns {Object} 解析后的配置对象
     */
    _parseSetOptions(options) {
        return {
            area: options.area || 'sync',
            prefix: options.prefix || 'data',
            ttl: options.ttl || null,
            compress: Boolean(options.compress),
            encrypt: Boolean(options.encrypt)
        };
    }

    /**
     * 处理要存储的值（压缩/加密）
     * @private
     * @param {*} value - 原始值
     * @param {Object} config - 配置对象
     * @returns {Promise<*>} 处理后的值
     */
    async _processValueForStorage(value, config) {
        let processedValue = value;

        if (config.compress) {
            processedValue = await this.compressData(processedValue);
        }

        if (config.encrypt) {
            processedValue = await this.encryptData(processedValue);
        }

        return processedValue;
    }

    /**
     * 创建带元数据的数据对象
     * @private
     * @param {*} processedValue - 处理后的值
     * @param {Object} config - 配置对象
     * @returns {Object} 带元数据的数据对象
     */
    _createDataWithMetadata(processedValue, config) {
        return {
            value: processedValue,
            timestamp: Date.now(),
            ttl: config.ttl,
            compressed: config.compress,
            encrypted: config.encrypt,
            version: '1.0'
        };
    }

    /**
     * 执行实际的存储操作
     * @private
     * @param {string} area - 存储区域
     * @param {string} fullKey - 完整键名
     * @param {Object} dataWithMeta - 带元数据的数据
     * @returns {Promise<void>}
     */
    async _performStorageSet(area, fullKey, dataWithMeta) {
        await this.setToArea(area, fullKey, dataWithMeta);
    }

    /**
     * 处理set操作的后续处理（缓存、统计、事件）
     * @private
     * @param {string} key - 原始键名
     * @param {string} fullKey - 完整键名
     * @param {string} area - 存储区域
     * @param {Object} dataWithMeta - 带元数据的数据
     */
    _handleSetPostProcessing(key, fullKey, area, dataWithMeta) {
        // 更新缓存
        if (this.config.enableCache) {
            this.updateCache(fullKey, dataWithMeta);
        }

        // 更新统计
        this.updateStats(area, 'set');

        // 发布设置完成事件
        if (this.eventBus) {
            this.eventBus.emit('storage:set-complete', {
                key: key,
                fullKey: fullKey,
                area: area,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 从存储中获取数据
     * @param {string} key - 存储键名
     * @param {Object} options - 获取选项
     * @param {string} [options.area='sync'] - 存储区域 (sync/local/session)
     * @param {string} [options.prefix='data'] - 键名前缀
     * @param {*} [options.defaultValue=null] - 默认值
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<*>} 获取到的数据或默认值
     * @throws {Error} 获取操作失败时抛出错误
     *
     * @example
     * // 基本用法
     * const config = await storageService.get('userConfig');
     *
     * // 带默认值的用法
     * const theme = await storageService.get('theme', {
     *   defaultValue: 'light',
     *   area: 'local'
     * });
     */
    async get(key, options = {}) {
        try {
            // 解析选项
            const config = this._parseGetOptions(options);
            console.log(`💾 [StorageService] 获取数据: ${key}`, { area: config.area, prefix: config.prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, config.prefix);

            // 尝试从缓存获取
            const cachedResult = this._tryGetFromCache(fullKey, config.useCache);
            if (cachedResult.found) {
                return cachedResult.value;
            }

            // 从存储获取数据
            const dataWithMeta = await this.getFromArea(config.area, fullKey);
            if (!dataWithMeta) {
                return config.defaultValue;
            }

            // 验证数据有效性（TTL检查）
            const isValid = await this._validateDataTTL(dataWithMeta, key, config);
            if (!isValid) {
                return config.defaultValue;
            }

            // 处理数据（解密/解压缩）
            const processedValue = await this._processValueFromStorage(dataWithMeta);

            // 更新缓存
            this._updateCacheAfterGet(fullKey, dataWithMeta, config.useCache);

            // 更新统计和发布事件
            this._handleGetPostProcessing(key, config.area);

            return processedValue;

        } catch (error) {
            console.error('❌ [StorageService] 数据获取失败', error);
            this.stats.errors++;

            // 发布错误事件
            if (this.eventBus) {
                this.eventBus.emit('storage:error', {
                    operation: 'get',
                    key: key,
                    error: error.message,
                    timestamp: Date.now()
                });
            }

            return options.defaultValue || null;
        }
    }

    /**
     * 解析get操作的选项参数
     * @private
     * @param {Object} options - 原始选项
     * @returns {Object} 解析后的配置对象
     */
    _parseGetOptions(options) {
        return {
            area: options.area || 'sync',
            prefix: options.prefix || 'data',
            defaultValue: options.defaultValue || null,
            useCache: options.useCache !== false // 默认为true
        };
    }

    /**
     * 尝试从缓存获取数据
     * @private
     * @param {string} fullKey - 完整键名
     * @param {boolean} useCache - 是否使用缓存
     * @returns {Object} 缓存结果 {found: boolean, value: *}
     */
    _tryGetFromCache(fullKey, useCache) {
        if (useCache && this.config.enableCache) {
            const cachedData = this.getFromCache(fullKey);
            if (cachedData !== null) {
                this.stats.cacheHits++;
                return { found: true, value: cachedData.value };
            }
            this.stats.cacheMisses++;
        }
        return { found: false, value: null };
    }

    /**
     * 验证数据的TTL有效性
     * @private
     * @param {Object} dataWithMeta - 带元数据的数据
     * @param {string} key - 原始键名
     * @param {Object} config - 配置对象
     * @returns {Promise<boolean>} 数据是否有效
     */
    async _validateDataTTL(dataWithMeta, key, config) {
        if (dataWithMeta.ttl && Date.now() - dataWithMeta.timestamp > dataWithMeta.ttl) {
            console.log(`⏰ [StorageService] 数据已过期: ${key}`);
            await this.remove(key, { area: config.area, prefix: config.prefix });
            return false;
        }
        return true;
    }

    /**
     * 处理从存储获取的值（解密/解压缩）
     * @private
     * @param {Object} dataWithMeta - 带元数据的数据
     * @returns {Promise<*>} 处理后的值
     */
    async _processValueFromStorage(dataWithMeta) {
        let processedValue = dataWithMeta.value;

        if (dataWithMeta.encrypted) {
            processedValue = await this.decryptData(processedValue);
        }

        if (dataWithMeta.compressed) {
            processedValue = await this.decompressData(processedValue);
        }

        return processedValue;
    }

    /**
     * 获取操作后更新缓存
     * @private
     * @param {string} fullKey - 完整键名
     * @param {Object} dataWithMeta - 带元数据的数据
     * @param {boolean} useCache - 是否使用缓存
     */
    _updateCacheAfterGet(fullKey, dataWithMeta, useCache) {
        if (useCache && this.config.enableCache) {
            this.updateCache(fullKey, dataWithMeta);
        }
    }

    /**
     * 处理get操作的后续处理（统计、事件）
     * @private
     * @param {string} key - 原始键名
     * @param {string} area - 存储区域
     */
    _handleGetPostProcessing(key, area) {
        // 更新统计
        this.updateStats(area, 'get');
    }

    /**
     * 移除数据
     * @param {string} key - 键
     * @param {Object} options - 选项
     */
    async remove(key, options = {}) {
        try {
            const {
                area = 'sync',
                prefix = 'data'
            } = options;

            console.log(`💾 [StorageService] 移除数据: ${key}`, { area, prefix });

            // 构建完整键名
            const fullKey = this.buildKey(key, prefix);

            // 从存储区域移除
            await this.removeFromArea(area, fullKey);

            // 从缓存移除
            this.removeFromCache(fullKey);

            // 更新统计
            this.updateStats(area, 'remove');

            // 发布移除完成事件
            if (this.eventBus) {
                this.eventBus.emit('storage:remove-complete', {
                    key: key,
                    fullKey: fullKey,
                    area: area,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据移除失败', error);
            this.stats.errors++;
            throw error;
        }
    }

    /**
     * 清除数据
     * @param {Object} options - 选项
     */
    async clear(options = {}) {
        try {
            const {
                area = 'sync',
                prefix = null,
                confirm = false
            } = options;

            if (!confirm) {
                throw new Error('清除操作需要确认');
            }

            console.log(`💾 [StorageService] 清除数据`, { area, prefix });

            if (prefix) {
                // 清除特定前缀的数据
                await this.clearByPrefix(area, prefix);
            } else {
                // 清除整个存储区域
                await this.clearArea(area);
            }

            // 清除相关缓存
            if (prefix) {
                this.clearCacheByPrefix(prefix);
            } else {
                this.clearCache();
            }

            // 发布清除完成事件
            if (this.eventBus) {
                this.eventBus.emit('storage:clear-complete', {
                    area: area,
                    prefix: prefix,
                    timestamp: Date.now()
                });
            }

            return true;

        } catch (error) {
            console.error('❌ [StorageService] 数据清除失败', error);
            this.stats.errors++;
            throw error;
        }
    }

    /**
     * 批量操作
     * @param {Array} operations - 操作列表
     */
    async batch(operations) {
        try {
            console.log(`💾 [StorageService] 执行批量操作: ${operations.length} 个`);

            const results = [];
            
            for (const operation of operations) {
                const { type, key, value, options } = operation;
                
                try {
                    let result;
                    switch (type) {
                        case 'set':
                            result = await this.set(key, value, options);
                            break;
                        case 'get':
                            result = await this.get(key, options);
                            break;
                        case 'remove':
                            result = await this.remove(key, options);
                            break;
                        default:
                            throw new Error(`未知的操作类型: ${type}`);
                    }
                    
                    results.push({
                        success: true,
                        operation: operation,
                        result: result
                    });
                    
                } catch (error) {
                    results.push({
                        success: false,
                        operation: operation,
                        error: error.message
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            console.log(`✅ [StorageService] 批量操作完成: ${successCount}/${operations.length} 成功`);

            return results;

        } catch (error) {
            console.error('❌ [StorageService] 批量操作失败', error);
            throw error;
        }
    }

    /**
     * 存储到指定区域
     * @param {string} area - 存储区域
     * @param {string} key - 键
     * @param {*} value - 值
     */
    async setToArea(area, key, value) {
        const data = { [key]: value };
        
        switch (area) {
            case 'sync':
                if (this.config.enableSync && chrome.storage.sync) {
                    await chrome.storage.sync.set(data);
                } else {
                    throw new Error('Sync存储不可用');
                }
                break;
                
            case 'local':
                if (this.config.enableLocal && chrome.storage.local) {
                    await chrome.storage.local.set(data);
                } else {
                    throw new Error('Local存储不可用');
                }
                break;
                
            case 'session':
                if (this.config.enableSession && chrome.storage.session) {
                    await chrome.storage.session.set(data);
                } else {
                    // 降级到sessionStorage
                    sessionStorage.setItem(key, JSON.stringify(value));
                }
                break;
                
            default:
                throw new Error(`未知的存储区域: ${area}`);
        }
    }

    /**
     * 从指定区域获取
     * @param {string} area - 存储区域
     * @param {string} key - 键
     */
    async getFromArea(area, key) {
        let result;
        
        switch (area) {
            case 'sync':
                if (this.config.enableSync && chrome.storage.sync) {
                    result = await chrome.storage.sync.get(key);
                } else {
                    return null;
                }
                break;
                
            case 'local':
                if (this.config.enableLocal && chrome.storage.local) {
                    result = await chrome.storage.local.get(key);
                } else {
                    return null;
                }
                break;
                
            case 'session':
                if (this.config.enableSession && chrome.storage.session) {
                    result = await chrome.storage.session.get(key);
                } else {
                    // 降级到sessionStorage
                    const data = sessionStorage.getItem(key);
                    return data ? JSON.parse(data) : null;
                }
                break;
                
            default:
                throw new Error(`未知的存储区域: ${area}`);
        }
        
        return result[key] || null;
    }

    /**
     * 从指定区域移除
     * @param {string} area - 存储区域
     * @param {string} key - 键
     */
    async removeFromArea(area, key) {
        switch (area) {
            case 'sync':
                if (chrome.storage.sync) {
                    await chrome.storage.sync.remove(key);
                }
                break;
                
            case 'local':
                if (chrome.storage.local) {
                    await chrome.storage.local.remove(key);
                }
                break;
                
            case 'session':
                if (chrome.storage.session) {
                    await chrome.storage.session.remove(key);
                } else {
                    sessionStorage.removeItem(key);
                }
                break;
        }
    }

    /**
     * 清除存储区域
     * @param {string} area - 存储区域
     */
    async clearArea(area) {
        switch (area) {
            case 'sync':
                if (chrome.storage.sync) {
                    await chrome.storage.sync.clear();
                }
                break;
                
            case 'local':
                if (chrome.storage.local) {
                    await chrome.storage.local.clear();
                }
                break;
                
            case 'session':
                if (chrome.storage.session) {
                    await chrome.storage.session.clear();
                } else {
                    sessionStorage.clear();
                }
                break;
        }
    }

    /**
     * 按前缀清除
     * @param {string} area - 存储区域
     * @param {string} prefix - 前缀
     */
    async clearByPrefix(area, prefix) {
        // 获取所有键
        let allData;
        
        switch (area) {
            case 'sync':
                allData = await chrome.storage.sync.get(null);
                break;
            case 'local':
                allData = await chrome.storage.local.get(null);
                break;
            case 'session':
                if (chrome.storage.session) {
                    allData = await chrome.storage.session.get(null);
                } else {
                    allData = {};
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        allData[key] = sessionStorage.getItem(key);
                    }
                }
                break;
        }

        // 找到匹配前缀的键
        const keysToRemove = Object.keys(allData).filter(key => 
            key.startsWith(this.keyPrefixes[prefix] || prefix)
        );

        // 批量移除
        for (const key of keysToRemove) {
            await this.removeFromArea(area, key);
        }
    }

    /**
     * 构建键名
     * @param {string} key - 原始键
     * @param {string} prefix - 前缀类型
     */
    buildKey(key, prefix) {
        const prefixStr = this.keyPrefixes[prefix] || prefix + '_';
        return prefixStr + key;
    }

    /**
     * 更新缓存
     * @param {string} key - 键
     * @param {*} data - 数据
     */
    updateCache(key, data) {
        if (this.cache.size >= this.config.maxCacheSize) {
            // 移除最旧的缓存项
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
            this.cacheTimestamps.delete(oldestKey);
        }

        this.cache.set(key, data);
        this.cacheTimestamps.set(key, Date.now());
    }

    /**
     * 从缓存获取
     * @param {string} key - 键
     */
    getFromCache(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        const timestamp = this.cacheTimestamps.get(key);
        if (Date.now() - timestamp > this.config.cacheTimeout) {
            this.cache.delete(key);
            this.cacheTimestamps.delete(key);
            return null;
        }

        return this.cache.get(key);
    }

    /**
     * 从缓存移除
     * @param {string} key - 键
     */
    removeFromCache(key) {
        this.cache.delete(key);
        this.cacheTimestamps.delete(key);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        this.cacheTimestamps.clear();
    }

    /**
     * 按前缀清除缓存
     * @param {string} prefix - 前缀
     */
    clearCacheByPrefix(prefix) {
        const prefixStr = this.keyPrefixes[prefix] || prefix + '_';
        
        for (const key of this.cache.keys()) {
            if (key.startsWith(prefixStr)) {
                this.cache.delete(key);
                this.cacheTimestamps.delete(key);
            }
        }
    }

    /**
     * 更新统计
     * @param {string} area - 存储区域
     * @param {string} operation - 操作类型
     */
    updateStats(area, operation) {
        const statKey = area + 'Operations';
        if (this.stats[statKey] !== undefined) {
            this.stats[statKey]++;
        }
    }

    /**
     * 压缩数据
     * @param {*} data - 数据
     */
    async compressData(data) {
        // 简单的JSON压缩（实际项目中可以使用更好的压缩算法）
        return JSON.stringify(data);
    }

    /**
     * 解压数据
     * @param {string} compressedData - 压缩的数据
     */
    async decompressData(compressedData) {
        return JSON.parse(compressedData);
    }

    /**
     * 加密数据
     * @param {*} data - 数据
     */
    async encryptData(data) {
        // 简单的Base64编码（实际项目中应使用真正的加密）
        return btoa(JSON.stringify(data));
    }

    /**
     * 解密数据
     * @param {string} encryptedData - 加密的数据
     */
    async decryptData(encryptedData) {
        return JSON.parse(atob(encryptedData));
    }

    /**
     * 处理存储变化
     * @param {Object} changes - 变化对象
     * @param {string} areaName - 存储区域名称
     */
    handleStorageChange(changes, areaName) {
        console.log(`💾 [StorageService] 存储变化检测: ${areaName}`, changes);

        // 更新缓存
        Object.keys(changes).forEach(key => {
            if (changes[key].newValue === undefined) {
                // 数据被删除
                this.removeFromCache(key);
            } else {
                // 数据被更新
                this.updateCache(key, changes[key].newValue);
            }
        });

        // 发布存储变化事件
        if (this.eventBus) {
            this.eventBus.emit('storage:changed', {
                changes: changes,
                area: areaName,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 处理设置请求
     * @param {Object} data - 请求数据
     */
    async handleSetRequest(data) {
        const { key, value, options, callback } = data;
        
        try {
            const result = await this.set(key, value, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理获取请求
     * @param {Object} data - 请求数据
     */
    async handleGetRequest(data) {
        const { key, options, callback } = data;
        
        try {
            const result = await this.get(key, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理移除请求
     * @param {Object} data - 请求数据
     */
    async handleRemoveRequest(data) {
        const { key, options, callback } = data;
        
        try {
            const result = await this.remove(key, options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 处理清除请求
     * @param {Object} data - 请求数据
     */
    async handleClearRequest(data) {
        const { options, callback } = data;
        
        try {
            const result = await this.clear(options);
            if (callback) callback(null, result);
        } catch (error) {
            if (callback) callback(error, null);
        }
    }

    /**
     * 获取存储统计
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            cacheHitRate: this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) || 0
        };
    }

    /**
     * 获取存储使用情况
     */
    async getUsage() {
        const usage = {};
        
        try {
            if (chrome.storage.sync) {
                usage.sync = await chrome.storage.sync.getBytesInUse();
            }
            if (chrome.storage.local) {
                usage.local = await chrome.storage.local.getBytesInUse();
            }
            if (chrome.storage.session) {
                usage.session = await chrome.storage.session.getBytesInUse();
            }
        } catch (error) {
            console.warn('⚠️ [StorageService] 无法获取存储使用情况', error);
        }
        
        return usage;
    }

    /**
     * 销毁存储服务
     */
    destroy() {
        // 清除缓存
        this.clearCache();
        
        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('storage:set');
            this.eventBus.off('storage:get');
            this.eventBus.off('storage:remove');
            this.eventBus.off('storage:clear');
        }

        console.log('🗑️ [StorageService] 存储服务已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageService;
} else {
    window.StorageService = StorageService;
}

console.log('✅ [StorageService] 存储服务模块已加载');

} // 结束重复声明检查
