/**
 * FillMonitor模块 - 兼容性存根
 * Content Script Adapter中已提供FillMonitor，此文件确保路径存在
 */

console.log('📊 [modules/fill-monitor.js] 兼容性存根文件已加载 - FillMonitor在adapter中提供');

// 确保FillMonitor可用的后备方案
if (typeof window.FillMonitor === 'undefined') {
    console.warn('⚠️ [modules/fill-monitor.js] FillMonitor未定义，可能是adapter加载失败');
    
    // 提供基础的兼容性实现
    window.FillMonitor = class {
        constructor() {
            console.log('📊 [FillMonitor] 后备实现已初始化');
        }
        startMonitoring() { /* 空实现 */ }
        stopMonitoring() { /* 空实现 */ }
        getStats() { return {}; }
    };
}
