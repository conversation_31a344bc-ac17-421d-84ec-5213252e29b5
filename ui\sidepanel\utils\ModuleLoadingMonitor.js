/**
 * 模块加载状态监控器
 * 实时监控模块加载进度和状态
 * 创建日期: 2025-01-11
 */

// 防止重复声明 - 修复版本
if (typeof window.ModuleLoadingMonitor !== 'undefined') {
    console.warn('⚠️ [ModuleLoadingMonitor] 类已存在，跳过重复声明');
} else {

class ModuleLoadingMonitor {
    constructor() {
        this.loadingStatus = {
            total: 0,
            loaded: 0,
            failed: 0,
            progress: 0,
            startTime: null,
            endTime: null,
            loadingTime: 0
        };

        this.moduleStates = new Map();
        this.ui = null;
        this.isVisible = false;
        
        console.log('📊 [ModuleLoadingMonitor] 模块加载监控器已初始化');
        
        this.initializeEventListeners();
        this.createUI();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 等待事件总线可用
        const waitForEventBus = () => {
            if (window.mdacEventBus) {
                this.setupEventListeners();
            } else {
                setTimeout(waitForEventBus, 100);
            }
        };
        waitForEventBus();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const eventBus = window.mdacEventBus;

        // 监听模块加载进度
        eventBus.on('module:loading-progress', (data) => {
            this.updateProgress(data);
        });

        // 监听所有模块加载完成
        eventBus.on('module:all-loaded', (data) => {
            this.onAllModulesLoaded(data);
        });

        // 监听单个模块加载状态
        eventBus.on('module:loaded', (data) => {
            this.onModuleLoaded(data);
        });

        eventBus.on('module:failed', (data) => {
            this.onModuleFailed(data);
        });

        console.log('✅ [ModuleLoadingMonitor] 事件监听器已设置');
    }

    /**
     * 创建监控UI
     */
    createUI() {
        // 创建容器
        this.ui = document.createElement('div');
        this.ui.id = 'mdac-module-loading-monitor';
        this.ui.className = 'module-loading-monitor';
        
        this.ui.innerHTML = `
            <div class="monitor-header">
                <span class="monitor-title">📦 模块加载状态</span>
                <button class="monitor-toggle" id="monitorToggle">−</button>
            </div>
            <div class="monitor-content" id="monitorContent">
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">等待开始...</div>
                </div>
                <div class="status-section">
                    <div class="status-item">
                        <span class="status-label">已加载:</span>
                        <span class="status-value" id="loadedCount">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">总计:</span>
                        <span class="status-value" id="totalCount">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">失败:</span>
                        <span class="status-value error" id="failedCount">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">耗时:</span>
                        <span class="status-value" id="loadingTime">--</span>
                    </div>
                </div>
                <div class="modules-list" id="modulesList">
                    <!-- 模块列表将动态生成 -->
                </div>
            </div>
        `;

        // 添加样式
        this.injectStyles();

        // 添加到页面
        document.body.appendChild(this.ui);

        // 绑定事件
        this.bindUIEvents();

        // 默认隐藏
        this.hide();
    }

    /**
     * 注入样式
     */
    injectStyles() {
        const styleId = 'mdac-module-monitor-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .module-loading-monitor {
                position: fixed;
                top: 20px;
                left: 20px;
                width: 350px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 12px;
            }

            .monitor-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: #f8f9fa;
                border-bottom: 1px solid #ddd;
                border-radius: 8px 8px 0 0;
            }

            .monitor-title {
                font-weight: 600;
                color: #333;
            }

            .monitor-toggle {
                border: none;
                background: none;
                font-size: 16px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .monitor-content {
                padding: 12px;
            }

            .progress-section {
                margin-bottom: 12px;
            }

            .progress-bar {
                width: 100%;
                height: 6px;
                background: #e9ecef;
                border-radius: 3px;
                overflow: hidden;
                margin-bottom: 6px;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745, #20c997);
                width: 0%;
                transition: width 0.3s ease;
            }

            .progress-text {
                text-align: center;
                color: #666;
                font-size: 11px;
            }

            .status-section {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                margin-bottom: 12px;
            }

            .status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 8px;
                background: #f8f9fa;
                border-radius: 4px;
            }

            .status-label {
                color: #666;
                font-size: 10px;
            }

            .status-value {
                font-weight: 600;
                color: #333;
            }

            .status-value.error {
                color: #dc3545;
            }

            .modules-list {
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 4px;
            }

            .module-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 3px 6px;
                margin: 1px 0;
                border-radius: 3px;
                font-size: 10px;
            }

            .module-item.loading {
                background: #fff3cd;
                color: #856404;
            }

            .module-item.loaded {
                background: #d4edda;
                color: #155724;
            }

            .module-item.failed {
                background: #f8d7da;
                color: #721c24;
            }

            .module-name {
                flex: 1;
            }

            .module-status {
                font-size: 9px;
                padding: 1px 4px;
                border-radius: 2px;
                background: rgba(0,0,0,0.1);
            }

            .module-loading-monitor.collapsed .monitor-content {
                display: none;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 绑定UI事件
     */
    bindUIEvents() {
        const toggleBtn = this.ui.querySelector('#monitorToggle');
        toggleBtn.addEventListener('click', () => {
            this.toggle();
        });

        // 双击标题栏切换显示/隐藏
        const header = this.ui.querySelector('.monitor-header');
        header.addEventListener('dblclick', () => {
            this.isVisible ? this.hide() : this.show();
        });
    }

    /**
     * 更新进度
     */
    updateProgress(data) {
        const { moduleName, loaded, total, progress } = data;

        // 更新状态
        this.loadingStatus.loaded = loaded;
        this.loadingStatus.total = total;
        this.loadingStatus.progress = progress;

        if (!this.loadingStatus.startTime) {
            this.loadingStatus.startTime = Date.now();
            this.show(); // 开始加载时显示监控器
        }

        // 更新UI
        this.updateUI();

        // 添加/更新模块状态
        this.moduleStates.set(moduleName, {
            name: moduleName,
            status: 'loaded',
            timestamp: Date.now()
        });

        this.updateModulesList();

        console.log(`📊 [ModuleLoadingMonitor] 进度更新: ${moduleName} (${progress}%)`);
    }

    /**
     * 模块加载完成
     */
    onModuleLoaded(data) {
        const { moduleName } = data;
        this.moduleStates.set(moduleName, {
            name: moduleName,
            status: 'loaded',
            timestamp: Date.now()
        });
        this.updateModulesList();
    }

    /**
     * 模块加载失败
     */
    onModuleFailed(data) {
        const { moduleName, error } = data;
        this.loadingStatus.failed++;
        
        this.moduleStates.set(moduleName, {
            name: moduleName,
            status: 'failed',
            error: error,
            timestamp: Date.now()
        });
        
        this.updateUI();
        this.updateModulesList();
    }

    /**
     * 所有模块加载完成
     */
    onAllModulesLoaded(data) {
        this.loadingStatus.endTime = Date.now();
        this.loadingStatus.loadingTime = this.loadingStatus.endTime - this.loadingStatus.startTime;
        
        console.log('✅ [ModuleLoadingMonitor] 所有模块加载完成', data);
        
        this.updateUI();
        
        // 3秒后自动隐藏（如果没有错误）
        if (this.loadingStatus.failed === 0) {
            setTimeout(() => {
                this.hide();
            }, 3000);
        }
    }

    /**
     * 更新UI
     */
    updateUI() {
        const progressFill = this.ui.querySelector('#progressFill');
        const progressText = this.ui.querySelector('#progressText');
        const loadedCount = this.ui.querySelector('#loadedCount');
        const totalCount = this.ui.querySelector('#totalCount');
        const failedCount = this.ui.querySelector('#failedCount');
        const loadingTime = this.ui.querySelector('#loadingTime');

        // 更新进度条
        progressFill.style.width = `${this.loadingStatus.progress}%`;

        // 更新进度文本
        progressText.textContent = `${this.loadingStatus.progress}% (${this.loadingStatus.loaded}/${this.loadingStatus.total})`;

        // 更新计数
        loadedCount.textContent = this.loadingStatus.loaded;
        totalCount.textContent = this.loadingStatus.total;
        failedCount.textContent = this.loadingStatus.failed;

        // 更新耗时
        if (this.loadingStatus.loadingTime > 0) {
            loadingTime.textContent = `${this.loadingStatus.loadingTime}ms`;
        } else if (this.loadingStatus.startTime) {
            const elapsed = Date.now() - this.loadingStatus.startTime;
            loadingTime.textContent = `${elapsed}ms`;
        }
    }

    /**
     * 更新模块列表
     */
    updateModulesList() {
        const modulesList = this.ui.querySelector('#modulesList');
        
        const modules = Array.from(this.moduleStates.values())
            .sort((a, b) => a.timestamp - b.timestamp);

        modulesList.innerHTML = modules.map(module => {
            const statusIcon = {
                loading: '⏳',
                loaded: '✅',
                failed: '❌'
            }[module.status] || '⏳';

            return `
                <div class="module-item ${module.status}">
                    <span class="module-name">${module.name}</span>
                    <span class="module-status">${statusIcon}</span>
                </div>
            `;
        }).join('');
    }

    /**
     * 显示监控器
     */
    show() {
        this.ui.style.display = 'block';
        this.isVisible = true;
    }

    /**
     * 隐藏监控器
     */
    hide() {
        this.ui.style.display = 'none';
        this.isVisible = false;
    }

    /**
     * 切换折叠状态
     */
    toggle() {
        const content = this.ui.querySelector('#monitorContent');
        const toggleBtn = this.ui.querySelector('#monitorToggle');
        
        if (this.ui.classList.contains('collapsed')) {
            this.ui.classList.remove('collapsed');
            toggleBtn.textContent = '−';
        } else {
            this.ui.classList.add('collapsed');
            toggleBtn.textContent = '+';
        }
    }

    /**
     * 获取状态报告
     */
    getStatusReport() {
        return {
            loadingStatus: { ...this.loadingStatus },
            moduleStates: Array.from(this.moduleStates.values()),
            timestamp: Date.now()
        };
    }

    /**
     * 销毁监控器
     */
    destroy() {
        if (this.ui && this.ui.parentNode) {
            this.ui.parentNode.removeChild(this.ui);
        }
        
        const style = document.getElementById('mdac-module-monitor-styles');
        if (style && style.parentNode) {
            style.parentNode.removeChild(style);
        }
        
        console.log('🗑️ [ModuleLoadingMonitor] 监控器已销毁');
    }
}

// 导出类和创建全局实例 - 确保在任何情况下都能正确导出
if (typeof window.ModuleLoadingMonitor === 'undefined') {
    window.ModuleLoadingMonitor = ModuleLoadingMonitor;
    // 创建全局实例
    window.mdacModuleLoadingMonitor = new ModuleLoadingMonitor();
}

console.log('✅ [ModuleLoadingMonitor] 模块加载监控器已加载');

} // 结束重复声明检查
