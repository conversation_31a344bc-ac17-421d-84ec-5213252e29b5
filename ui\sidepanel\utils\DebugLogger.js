/**
 * 调试日志工具 - 增强的调试和日志记录功能
 * 提供分级日志、性能监控和调试信息收集
 * 创建日期: 2025-01-11
 * 修复日期: 2025-07-12
 */

// 防止重复声明的新方法 - 使用简单检查
(function() {
    'use strict';

    // 如果DebugLogger已存在且功能正常，则跳过
    if (typeof window.DebugLogger === 'function') {
        console.log('✅ [DebugLogger] 已存在且正常，跳过重新定义');
        return;
    }

    class DebugLogger {
    constructor(eventBus = window.mdacEventBus) {
        this.eventBus = eventBus;
        
        // 日志配置
        this.config = {
            enabled: true,
            level: 'INFO', // DEBUG, INFO, WARN, ERROR
            maxLogs: 1000,
            enableConsole: true,
            enableStorage: true,
            enablePerformance: true
        };

        // 日志级别
        this.levels = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3
        };

        // 日志存储
        this.logs = [];
        this.performanceLogs = [];
        
        // 性能监控
        this.performanceMarks = new Map();
        this.performanceMeasures = [];

        // 日志样式
        this.styles = {
            DEBUG: 'color: #6c757d; font-weight: normal;',
            INFO: 'color: #17a2b8; font-weight: normal;',
            WARN: 'color: #ffc107; font-weight: bold;',
            ERROR: 'color: #dc3545; font-weight: bold;'
        };

        console.log('🐛 [DebugLogger] 调试日志工具已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (this.eventBus) {
            // 监听配置变更
            this.eventBus.on('debug:config-changed', (config) => {
                this.updateConfig(config);
            });

            // 监听日志导出请求
            this.eventBus.on('debug:export-logs', () => {
                return this.exportLogs();
            });

            // 监听日志清理请求
            this.eventBus.on('debug:clear-logs', () => {
                this.clearLogs();
            });
        }

        // 监听未捕获的错误
        window.addEventListener('error', (event) => {
            this.error('UNCAUGHT_ERROR', `${event.message} at ${event.filename}:${event.lineno}:${event.colno}`, {
                error: event.error,
                stack: event.error?.stack
            });
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.error('UNHANDLED_REJECTION', `Unhandled promise rejection: ${event.reason}`, {
                reason: event.reason,
                promise: event.promise
            });
        });
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('🔧 [DebugLogger] 配置已更新', this.config);
    }

    /**
     * 检查日志级别是否应该输出
     * @param {string} level - 日志级别
     */
    shouldLog(level) {
        if (!this.config.enabled) return false;
        return this.levels[level] >= this.levels[this.config.level];
    }

    /**
     * 记录日志
     * @param {string} level - 日志级别
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    log(level, module, message, data = null) {
        if (!this.shouldLog(level)) return;

        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            module,
            message,
            data,
            id: this.generateId()
        };

        // 添加到日志存储
        this.logs.push(logEntry);
        this.enforceLogLimit();

        // 控制台输出
        if (this.config.enableConsole) {
            this.outputToConsole(logEntry);
        }

        // 存储到本地
        if (this.config.enableStorage) {
            this.saveToStorage();
        }

        // 发布日志事件
        if (this.eventBus) {
            this.eventBus.emit('debug:log-added', logEntry);
        }
    }

    /**
     * 调试级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    debug(module, message, data = null) {
        this.log('DEBUG', module, message, data);
    }

    /**
     * 信息级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    info(module, message, data = null) {
        this.log('INFO', module, message, data);
    }

    /**
     * 警告级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    warn(module, message, data = null) {
        this.log('WARN', module, message, data);
    }

    /**
     * 错误级别日志
     * @param {string} module - 模块名称
     * @param {string} message - 日志消息
     * @param {*} data - 附加数据
     */
    error(module, message, data = null) {
        this.log('ERROR', module, message, data);
    }

    /**
     * 输出到控制台
     * @param {Object} logEntry - 日志条目
     */
    outputToConsole(logEntry) {
        const { timestamp, level, module, message, data } = logEntry;
        const timeStr = new Date(timestamp).toLocaleTimeString();
        const style = this.styles[level];
        
        const logMessage = `%c[${timeStr}] [${level}] [${module}] ${message}`;
        
        if (data !== null) {
            console.log(logMessage, style, data);
        } else {
            console.log(logMessage, style);
        }
    }

    /**
     * 开始性能监控
     * @param {string} name - 性能标记名称
     * @param {Object} metadata - 元数据
     */
    startPerformance(name, metadata = {}) {
        if (!this.config.enablePerformance) return;

        const mark = {
            name,
            startTime: performance.now(),
            metadata,
            id: this.generateId()
        };

        this.performanceMarks.set(name, mark);
        
        this.debug('PERFORMANCE', `开始性能监控: ${name}`, metadata);
    }

    /**
     * 结束性能监控
     * @param {string} name - 性能标记名称
     * @param {Object} additionalData - 额外数据
     */
    endPerformance(name, additionalData = {}) {
        if (!this.config.enablePerformance) return;

        const mark = this.performanceMarks.get(name);
        if (!mark) {
            this.warn('PERFORMANCE', `性能标记未找到: ${name}`);
            return;
        }

        const endTime = performance.now();
        const duration = endTime - mark.startTime;

        const measure = {
            name,
            startTime: mark.startTime,
            endTime,
            duration,
            metadata: mark.metadata,
            additionalData,
            timestamp: new Date().toISOString(),
            id: this.generateId()
        };

        this.performanceMeasures.push(measure);
        this.performanceMarks.delete(name);

        this.info('PERFORMANCE', `性能监控完成: ${name} (${duration.toFixed(2)}ms)`, measure);

        // 发布性能事件
        if (this.eventBus) {
            this.eventBus.emit('debug:performance-measured', measure);
        }

        return measure;
    }

    /**
     * 记录性能快照
     * @param {string} name - 快照名称
     * @param {Object} data - 性能数据
     */
    recordPerformanceSnapshot(name, data = {}) {
        if (!this.config.enablePerformance) return;

        const snapshot = {
            name,
            timestamp: new Date().toISOString(),
            memory: this.getMemoryInfo(),
            timing: this.getTimingInfo(),
            data,
            id: this.generateId()
        };

        this.performanceLogs.push(snapshot);
        
        this.debug('PERFORMANCE', `性能快照: ${name}`, snapshot);
        
        return snapshot;
    }

    /**
     * 获取内存信息
     */
    getMemoryInfo() {
        if (performance.memory) {
            return {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }

    /**
     * 获取时序信息
     */
    getTimingInfo() {
        if (performance.timing) {
            const timing = performance.timing;
            return {
                navigationStart: timing.navigationStart,
                loadEventEnd: timing.loadEventEnd,
                domContentLoadedEventEnd: timing.domContentLoadedEventEnd,
                pageLoadTime: timing.loadEventEnd - timing.navigationStart,
                domReadyTime: timing.domContentLoadedEventEnd - timing.navigationStart
            };
        }
        return null;
    }

    /**
     * 获取日志统计
     */
    getLogStats() {
        const stats = {
            total: this.logs.length,
            byLevel: {},
            byModule: {},
            timeRange: null
        };

        // 按级别统计
        for (const level of Object.keys(this.levels)) {
            stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
        }

        // 按模块统计
        this.logs.forEach(log => {
            stats.byModule[log.module] = (stats.byModule[log.module] || 0) + 1;
        });

        // 时间范围
        if (this.logs.length > 0) {
            const timestamps = this.logs.map(log => new Date(log.timestamp).getTime());
            stats.timeRange = {
                start: new Date(Math.min(...timestamps)).toISOString(),
                end: new Date(Math.max(...timestamps)).toISOString()
            };
        }

        return stats;
    }

    /**
     * 搜索日志
     * @param {Object} criteria - 搜索条件
     */
    searchLogs(criteria = {}) {
        const { level, module, message, startTime, endTime, limit = 100 } = criteria;
        
        let results = [...this.logs];

        // 按级别过滤
        if (level) {
            results = results.filter(log => log.level === level);
        }

        // 按模块过滤
        if (module) {
            results = results.filter(log => log.module.includes(module));
        }

        // 按消息内容过滤
        if (message) {
            results = results.filter(log => log.message.includes(message));
        }

        // 按时间范围过滤
        if (startTime) {
            const start = new Date(startTime).getTime();
            results = results.filter(log => new Date(log.timestamp).getTime() >= start);
        }

        if (endTime) {
            const end = new Date(endTime).getTime();
            results = results.filter(log => new Date(log.timestamp).getTime() <= end);
        }

        // 限制结果数量
        return results.slice(-limit);
    }

    /**
     * 导出日志
     * @param {string} format - 导出格式 ('json' | 'csv' | 'txt')
     */
    exportLogs(format = 'json') {
        const exportData = {
            logs: this.logs,
            performanceLogs: this.performanceLogs,
            performanceMeasures: this.performanceMeasures,
            stats: this.getLogStats(),
            exportTime: new Date().toISOString(),
            config: this.config
        };

        switch (format) {
            case 'json':
                return JSON.stringify(exportData, null, 2);
                
            case 'csv':
                return this.convertToCSV(this.logs);
                
            case 'txt':
                return this.convertToText(this.logs);
                
            default:
                return exportData;
        }
    }

    /**
     * 转换为CSV格式
     * @param {Array} logs - 日志数组
     */
    convertToCSV(logs) {
        const headers = ['Timestamp', 'Level', 'Module', 'Message', 'Data'];
        const rows = logs.map(log => [
            log.timestamp,
            log.level,
            log.module,
            log.message,
            log.data ? JSON.stringify(log.data) : ''
        ]);

        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    /**
     * 转换为文本格式
     * @param {Array} logs - 日志数组
     */
    convertToText(logs) {
        return logs.map(log => {
            const time = new Date(log.timestamp).toLocaleString();
            const dataStr = log.data ? ` | Data: ${JSON.stringify(log.data)}` : '';
            return `[${time}] [${log.level}] [${log.module}] ${log.message}${dataStr}`;
        }).join('\n');
    }

    /**
     * 保存到本地存储
     */
    async saveToStorage() {
        try {
            const data = {
                logs: this.logs.slice(-500), // 只保存最近500条
                performanceLogs: this.performanceLogs.slice(-100),
                timestamp: Date.now()
            };

            await chrome.storage.local.set({ 'mdac_debug_logs': data });
        } catch (error) {
            console.error('保存调试日志失败:', error);
        }
    }

    /**
     * 从本地存储加载
     */
    async loadFromStorage() {
        try {
            const result = await chrome.storage.local.get(['mdac_debug_logs']);
            if (result.mdac_debug_logs) {
                this.logs = result.mdac_debug_logs.logs || [];
                this.performanceLogs = result.mdac_debug_logs.performanceLogs || [];
                console.log('调试日志已从存储加载');
            }
        } catch (error) {
            console.error('加载调试日志失败:', error);
        }
    }

    /**
     * 清除所有日志
     */
    clearLogs() {
        this.logs = [];
        this.performanceLogs = [];
        this.performanceMeasures = [];
        this.performanceMarks.clear();

        // 清除存储
        chrome.storage.local.remove(['mdac_debug_logs']);

        this.info('DEBUG_LOGGER', '所有日志已清除');

        // 发布清除事件
        if (this.eventBus) {
            this.eventBus.emit('debug:logs-cleared');
        }
    }

    /**
     * 强制执行日志数量限制
     */
    enforceLogLimit() {
        if (this.logs.length > this.config.maxLogs) {
            const excess = this.logs.length - this.config.maxLogs;
            this.logs.splice(0, excess);
        }

        if (this.performanceLogs.length > 200) {
            this.performanceLogs.splice(0, this.performanceLogs.length - 200);
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 创建调试会话
     * @param {string} sessionName - 会话名称
     */
    createDebugSession(sessionName) {
        const session = {
            name: sessionName,
            startTime: Date.now(),
            logs: [],
            performance: []
        };

        this.info('DEBUG_SESSION', `开始调试会话: ${sessionName}`, session);
        return session;
    }

    /**
     * 销毁调试日志工具
     */
    destroy() {
        // 保存最终状态
        this.saveToStorage();

        // 清理事件监听器
        if (this.eventBus) {
            this.eventBus.off('debug:config-changed');
            this.eventBus.off('debug:export-logs');
            this.eventBus.off('debug:clear-logs');
        }

        // 清理性能标记
        this.performanceMarks.clear();

        this.info('DEBUG_LOGGER', '调试日志工具已销毁');
    }
}

    // 立即注册到全局对象
    window.DebugLogger = DebugLogger;
    console.log('✅ [DebugLogger] 类已注册到全局对象');

    // 导出类 - 兼容 ultimate-bootstrap 属性保护系统
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = DebugLogger;
    }

    console.log('✅ [DebugLogger] 调试日志工具已加载完成');

})();
