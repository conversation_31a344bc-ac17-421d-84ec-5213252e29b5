<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC Chrome Extension - 项目知识图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 100vw;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 10px 0;
        }

        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 10px;
        }

        .tab {
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .tab:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .tab.active {
            background: white;
            color: #333;
        }

        .graph-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 20px;
            margin: 20px 0;
            min-height: 80vh;
        }

        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .legend-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            color: #666;
        }

        .module-details {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 500px;
            width: 90%;
        }

        .module-details h3 {
            margin-top: 0;
            color: #667eea;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #999;
        }

        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        .timeline {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            margin: 20px 0;
        }

        .timeline-item {
            display: flex;
            margin: 15px 0;
            align-items: center;
        }

        .timeline-date {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            margin-right: 20px;
            font-weight: bold;
            min-width: 120px;
            text-align: center;
        }

        .timeline-content {
            flex: 1;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        #svg {
            width: 100%;
            height: 80vh;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node:hover {
            filter: brightness(1.2);
        }

        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
        }

        .node-text {
            font-size: 12px;
            text-anchor: middle;
            pointer-events: none;
            font-weight: bold;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }

        .architecture-diagram {
            display: none;
            padding: 20px;
        }

        .layer {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid;
        }

        .layer.core { 
            background: #e3f2fd; 
            border-color: #2196f3; 
        }
        .layer.service { 
            background: #f3e5f5; 
            border-color: #9c27b0; 
        }
        .layer.application { 
            background: #e8f5e8; 
            border-color: #4caf50; 
        }
        .layer.presentation { 
            background: #fff3e0; 
            border-color: #ff9800; 
        }

        .layer h3 {
            margin-top: 0;
            font-size: 1.3em;
        }

        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .module-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .module-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .module-desc {
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
        }

        .dependency-lines {
            stroke: #999;
            stroke-width: 1px;
            stroke-dasharray: 5,5;
            opacity: 0.7;
        }

        .bootstrap-flow {
            display: none;
            padding: 20px;
        }

        .flow-step {
            margin: 15px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }

        .flow-step::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #667eea;
            border-radius: 50%;
        }

        .flow-step h4 {
            margin-top: 0;
            color: #667eea;
        }

        .responsive-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 MDAC Chrome Extension</h1>
            <p>马来西亚数字入境卡 AI 智能分析工具 - 项目知识图谱</p>
            <p>模块化架构 • Chrome Extension Manifest V3 • AI 驱动</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">25+</div>
                <div class="stat-label">模块化组件</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">7</div>
                <div class="stat-label">架构层级</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4,601</div>
                <div class="stat-label">原始代码行数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">模块化完成度</div>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('graph')">📊 依赖关系图</button>
            <button class="tab" onclick="showTab('architecture')">🏗️ 架构层级</button>
            <button class="tab" onclick="showTab('bootstrap')">🚀 启动流程</button>
            <button class="tab" onclick="showTab('timeline')">📅 发展历程</button>
        </div>

        <!-- 模块依赖关系图 -->
        <div id="graph-view" class="graph-container">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-circle" style="background: #2196f3;"></div>
                    <span>核心模块 (Core)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #9c27b0;"></div>
                    <span>AI 功能 (AI)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #4caf50;"></div>
                    <span>表单处理 (Form)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #ff9800;"></div>
                    <span>UI 组件 (UI)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #f44336;"></div>
                    <span>数据管理 (Data)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #795548;"></div>
                    <span>工具模块 (Utils)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-circle" style="background: #607d8b;"></div>
                    <span>特色功能 (Features)</span>
                </div>
            </div>
            
            <div class="responsive-controls">
                <button class="control-btn" onclick="resetGraph()">🔄 重置视图</button>
                <button class="control-btn" onclick="focusCore()">🎯 聚焦核心</button>
                <button class="control-btn" onclick="showAllConnections()">🔗 显示所有连接</button>
                <button class="control-btn" onclick="toggleLabels()">📝 切换标签</button>
            </div>

            <svg id="svg"></svg>
        </div>

        <!-- 架构层级图 -->
        <div id="architecture-view" class="graph-container architecture-diagram">
            <h2 style="text-align: center; margin-bottom: 30px;">🏗️ MDAC 模块化架构</h2>
            
            <div class="layer core">
                <h3>🔧 核心层 (Core Layer)</h3>
                <p>提供系统基础架构和模块管理功能</p>
                <div class="module-grid">
                    <div class="module-card" onclick="showModuleDetails('EventBus')">
                        <div class="module-name">EventBus</div>
                        <div class="module-desc">事件总线系统，模块间通信基础</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('StateManager')">
                        <div class="module-name">StateManager</div>
                        <div class="module-desc">全局状态管理，支持持久化</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('EventManager')">
                        <div class="module-name">EventManager</div>
                        <div class="module-desc">高级事件管理，优先级和批处理</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ModuleRegistry')">
                        <div class="module-name">ModuleRegistry</div>
                        <div class="module-desc">模块注册和依赖管理</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ModuleLoader')">
                        <div class="module-name">ModuleLoader</div>
                        <div class="module-desc">动态模块加载器</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('SidePanelCore')">
                        <div class="module-name">SidePanelCore</div>
                        <div class="module-desc">侧边栏核心功能</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ModuleInitializer')">
                        <div class="module-name">ModuleInitializer</div>
                        <div class="module-desc">模块初始化管理</div>
                    </div>
                </div>
            </div>

            <div class="layer service">
                <h3>⚙️ 服务层 (Service Layer)</h3>
                <p>提供核心业务服务和数据处理</p>
                <div class="module-grid">
                    <div class="module-card" onclick="showModuleDetails('AIService')">
                        <div class="module-name">AIService</div>
                        <div class="module-desc">Google Gemini AI 集成服务</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('StorageService')">
                        <div class="module-name">StorageService</div>
                        <div class="module-desc">Chrome 存储 API 封装</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('MessageHelper')">
                        <div class="module-name">MessageHelper</div>
                        <div class="module-desc">消息传递服务</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('DataManager')">
                        <div class="module-name">DataManager</div>
                        <div class="module-desc">数据管理和缓存</div>
                    </div>
                </div>
            </div>

            <div class="layer application">
                <h3>📱 应用层 (Application Layer)</h3>
                <p>实现具体业务功能和用户交互</p>
                <div class="module-grid">
                    <div class="module-card" onclick="showModuleDetails('FormFiller')">
                        <div class="module-name">FormFiller</div>
                        <div class="module-desc">智能表单填充引擎</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('TextParser')">
                        <div class="module-name">TextParser</div>
                        <div class="module-desc">AI 文本解析和提取</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ImageProcessor')">
                        <div class="module-name">ImageProcessor</div>
                        <div class="module-desc">图像识别和处理</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('DataValidator')">
                        <div class="module-name">DataValidator</div>
                        <div class="module-desc">数据验证和清洗</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('FieldMatcher')">
                        <div class="module-name">FieldMatcher</div>
                        <div class="module-desc">表单字段智能匹配</div>
                    </div>
                </div>
            </div>

            <div class="layer presentation">
                <h3>🎨 表现层 (Presentation Layer)</h3>
                <p>用户界面和交互体验</p>
                <div class="module-grid">
                    <div class="module-card" onclick="showModuleDetails('UIRenderer')">
                        <div class="module-name">UIRenderer</div>
                        <div class="module-desc">UI 渲染和更新</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ModalManager')">
                        <div class="module-name">ModalManager</div>
                        <div class="module-desc">模态框和弹窗管理</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('ProgressVisualizer')">
                        <div class="module-name">ProgressVisualizer</div>
                        <div class="module-desc">进度显示和可视化</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('CityViewer')">
                        <div class="module-name">CityViewer</div>
                        <div class="module-desc">城市选择器组件</div>
                    </div>
                    <div class="module-card" onclick="showModuleDetails('AutoParseManager')">
                        <div class="module-name">AutoParseManager</div>
                        <div class="module-desc">自动解析管理器</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bootstrap 启动流程 -->
        <div id="bootstrap-view" class="graph-container bootstrap-flow">
            <h2 style="text-align: center; margin-bottom: 30px;">🚀 Bootstrap 启动流程</h2>
            
            <div class="flow-step">
                <h4>1. HTML 加载 (ui-sidepanel.html)</h4>
                <p>侧边栏 HTML 文件加载，引入 ultimate-bootstrap.js</p>
                <div class="code-example">
&lt;script src="sidepanel/core/ultimate-bootstrap.js"&gt;&lt;/script&gt;
                </div>
            </div>

            <div class="flow-step">
                <h4>2. Bootstrap 初始化</h4>
                <p>UltimateModuleBootstrap 创建实例，设置防重复声明保护</p>
                <div class="code-example">
window.mdacUltimateBootstrap = new UltimateModuleBootstrap();
// 保护 25+ 个核心类防止重复声明
                </div>
            </div>

            <div class="flow-step">
                <h4>3. 模块定义加载</h4>
                <p>按优先级定义所有模块及其依赖关系</p>
                <div class="code-example">
Priority 1-8: 核心模块 (EventBus, StateManager, EventManager...)
Priority 10-12: 工具模块 (DateFormatter, MessageHelper...)
Priority 20-21: 数据模块 (StorageService, DataManager...)
Priority 30-32: 表单模块 (DataValidator, FieldMatcher...)
                </div>
            </div>

            <div class="flow-step">
                <h4>4. 依赖解析与加载</h4>
                <p>根据依赖关系按正确顺序加载所有模块</p>
                <div class="code-example">
// 检查依赖关系
areDependenciesSatisfied(moduleName)
// 安全加载脚本
loadScript(path, moduleName)
                </div>
            </div>

            <div class="flow-step">
                <h4>5. 主应用启动</h4>
                <p>等待所有核心模块加载完成后启动 MDACModularSidePanel</p>
                <div class="code-example">
// 等待核心模块: EventBus, EventManager, StateManager...
waitForBootstrapAndInitialize()
mdacModularSidePanel = new MDACModularSidePanel();
                </div>
            </div>

            <div class="flow-step">
                <h4>6. 模块化初始化</h4>
                <p>按层级顺序初始化所有功能模块</p>
                <div class="code-example">
initializeCoreModules()      // 核心模块
initializeUtilModules()      // 工具模块
initializeDataModules()      // 数据模块
initializeFunctionalModules() // 功能模块
initializeUIModules()        // UI 模块
                </div>
            </div>

            <div class="flow-step">
                <h4>7. 系统就绪</h4>
                <p>所有模块初始化完成，系统进入工作状态</p>
                <div class="code-example">
✅ 25+ 模块全部加载成功
✅ 事件系统正常工作
✅ AI 功能可用
✅ 表单填充就绪
                </div>
            </div>
        </div>

        <!-- 项目发展时间线 -->
        <div id="timeline-view" class="graph-container" style="display: none;">
            <h2 style="text-align: center; margin-bottom: 30px;">📅 MDAC 项目发展历程</h2>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-date">2024-12</div>
                    <div class="timeline-content">
                        <h4>🎯 项目启动</h4>
                        <p>开始开发 MDAC Chrome Extension，目标是为马来西亚数字入境卡提供 AI 辅助填表功能</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-01</div>
                    <div class="timeline-content">
                        <h4>📝 单体架构实现</h4>
                        <p>完成基础功能开发，但代码集中在 4,601 行的单体文件中，维护困难</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-01-10</div>
                    <div class="timeline-content">
                        <h4>🔧 模块化重构启动</h4>
                        <p>开始大规模模块化重构，将单体架构拆分为 25+ 个独立模块</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-01-11</div>
                    <div class="timeline-content">
                        <h4>🏗️ 架构分层设计</h4>
                        <p>建立四层架构：核心层、服务层、应用层、表现层，确立事件驱动模式</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-01-11</div>
                    <div class="timeline-content">
                        <h4>⚠️ 依赖冲突解决</h4>
                        <p>发现并解决模块重复声明问题，开发多版本 Bootstrap 系统</p>
                        <ul>
                            <li>simple-bootstrap.js - 基础版本</li>
                            <li>enhanced-bootstrap.js - 增强版本</li>
                            <li>ultimate-bootstrap.js - 终极版本</li>
                        </ul>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-01-12</div>
                    <div class="timeline-content">
                        <h4>🧹 项目结构清理</h4>
                        <p>大规模清理项目结构，删除冗余文件，统一配置，优化性能</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-date">2025-07-12</div>
                    <div class="timeline-content">
                        <h4>✅ 项目完成</h4>
                        <p>模块化重构完成，系统稳定运行，具备以下特性：</p>
                        <ul>
                            <li>零"Identifier already declared"错误</li>
                            <li>完善的错误恢复机制</li>
                            <li>高度模块化的架构</li>
                            <li>AI 驱动的智能填表</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模块详情弹窗 -->
    <div class="overlay" onclick="hideModuleDetails()"></div>
    <div class="module-details">
        <button class="close-btn" onclick="hideModuleDetails()">×</button>
        <h3 id="detail-title">模块详情</h3>
        <div id="detail-content">加载中...</div>
    </div>

    <script>
        // 模块定义数据
        const modules = {
            // 核心模块
            EventBus: {
                category: 'core',
                color: '#2196f3',
                dependencies: [],
                description: '事件总线系统，提供模块间通信的基础设施。支持事件发布、订阅和取消订阅。',
                features: ['事件发布订阅', '异步通信', '解耦模块', '事件过滤'],
                path: 'ui/sidepanel/core/EventBus.js',
                priority: 1,
                essential: true
            },
            StateManager: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus'],
                description: '全局状态管理器，负责应用状态的存储、更新和持久化。',
                features: ['状态持久化', '状态变更监听', '数据恢复', '状态同步'],
                path: 'ui/sidepanel/core/StateManager.js',
                priority: 3,
                essential: true
            },
            EventManager: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus', 'StateManager'],
                description: '高级事件管理器，提供事件优先级、批处理和复杂事件调度功能。',
                features: ['事件优先级', '批处理', '事件调度', '性能优化'],
                path: 'ui/sidepanel/core/EventManager.js',
                priority: 4,
                essential: true
            },
            ModuleRegistry: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus'],
                description: '模块注册器，管理所有模块的注册、依赖关系和生命周期。',
                features: ['模块注册', '依赖管理', '生命周期', '版本控制'],
                path: 'ui/sidepanel/core/ModuleRegistry.js',
                priority: 7,
                essential: true
            },
            ModuleLoader: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus', 'ModuleRegistry'],
                description: '动态模块加载器，支持懒加载、预加载和错误处理。',
                features: ['动态加载', '懒加载', '错误恢复', '加载优化'],
                path: 'ui/sidepanel/core/ModuleLoader.js',
                priority: 8,
                essential: true
            },
            SidePanelCore: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus', 'StateManager', 'EventManager'],
                description: '侧边栏核心功能模块，负责侧边栏的基础操作和模块协调。',
                features: ['核心功能', '模块协调', '生命周期管理', '通信中心'],
                path: 'ui/sidepanel/core/SidePanelCore.js',
                priority: 5,
                essential: true
            },
            ModuleInitializer: {
                category: 'core',
                color: '#2196f3',
                dependencies: ['EventBus'],
                description: '模块初始化管理器，控制模块初始化顺序和错误恢复。',
                features: ['初始化控制', '顺序管理', '错误恢复', '状态追踪'],
                path: 'ui/sidepanel/core/ModuleInitializer.js',
                priority: 6,
                essential: true
            },

            // AI 功能模块
            AIService: {
                category: 'ai',
                color: '#9c27b0',
                dependencies: ['EventBus'],
                description: 'Google Gemini AI 集成服务，提供文本和图像的智能分析功能。',
                features: ['Gemini 2.5 Flash', '文本分析', '图像识别', 'OCR'],
                path: 'ui/sidepanel/ai/AIService.js',
                priority: 50,
                apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent'
            },
            TextParser: {
                category: 'ai',
                color: '#9c27b0',
                dependencies: ['EventBus'],
                description: 'AI 文本解析器，智能提取和解析用户输入的文本信息。',
                features: ['智能解析', '信息提取', '格式识别', '数据清洗'],
                path: 'ui/sidepanel/ai/TextParser.js',
                priority: 51
            },
            ImageProcessor: {
                category: 'ai',
                color: '#9c27b0',
                dependencies: ['EventBus'],
                description: '图像处理器，提供图像识别、OCR 和图像预处理功能。',
                features: ['图像识别', 'OCR 功能', '图像预处理', '格式转换'],
                path: 'ui/sidepanel/ai/ImageProcessor.js',
                priority: 52
            },

            // 表单处理模块
            DataValidator: {
                category: 'form',
                color: '#4caf50',
                dependencies: ['EventBus'],
                description: '数据验证器，验证表单数据的有效性和完整性。',
                features: ['数据验证', '格式检查', '必填验证', '规则引擎'],
                path: 'ui/sidepanel/form/DataValidator.js',
                priority: 30
            },
            FieldMatcher: {
                category: 'form',
                color: '#4caf50',
                dependencies: ['EventBus'],
                description: '表单字段匹配器，智能识别和匹配网页表单字段。',
                features: ['字段识别', '智能匹配', '规则学习', '自适应'],
                path: 'ui/sidepanel/form/FieldMatcher.js',
                priority: 31
            },
            FormFiller: {
                category: 'form',
                color: '#4caf50',
                dependencies: ['EventBus', 'FieldMatcher', 'DataValidator'],
                description: '智能表单填充引擎，自动填充表单并处理复杂交互。',
                features: ['自动填充', '智能交互', '错误处理', '进度追踪'],
                path: 'ui/sidepanel/form/FormFiller.js',
                priority: 32
            },

            // UI 组件模块
            UIRenderer: {
                category: 'ui',
                color: '#ff9800',
                dependencies: ['EventBus', 'StateManager', 'ModalManager'],
                description: 'UI 渲染器，负责用户界面的渲染和更新。',
                features: ['界面渲染', '状态同步', '响应式设计', '性能优化'],
                path: 'ui/sidepanel/ui/UIRenderer.js',
                priority: 41
            },
            ModalManager: {
                category: 'ui',
                color: '#ff9800',
                dependencies: ['EventBus'],
                description: '模态框管理器，统一管理所有弹窗和模态对话框。',
                features: ['弹窗管理', '层级控制', '事件处理', '动画效果'],
                path: 'ui/sidepanel/ui/ModalManager.js',
                priority: 40
            },
            ProgressVisualizer: {
                category: 'ui',
                color: '#ff9800',
                dependencies: ['EventBus'],
                description: '进度可视化器，显示操作进度和状态信息。',
                features: ['进度显示', '状态可视化', '动态更新', '用户反馈'],
                path: 'ui/sidepanel/ui/ProgressVisualizer.js',
                priority: 42
            },

            // 数据管理模块
            StorageService: {
                category: 'data',
                color: '#f44336',
                dependencies: ['EventBus'],
                description: 'Chrome 存储 API 封装，提供数据持久化和缓存功能。',
                features: ['数据持久化', '缓存管理', 'Chrome Storage', '同步存储'],
                path: 'ui/sidepanel/data/StorageService.js',
                priority: 20
            },
            DataManager: {
                category: 'data',
                color: '#f44336',
                dependencies: ['EventBus', 'StorageService', 'StateManager'],
                description: '数据管理器，负责数据的统一管理、缓存和同步。',
                features: ['数据管理', '缓存策略', '数据同步', '版本控制'],
                path: 'ui/sidepanel/data/DataManager.js',
                priority: 21
            },
            PreviewManager: {
                category: 'data',
                color: '#f44336',
                dependencies: ['EventBus', 'DataManager'],
                description: '预览管理器，管理数据预览和展示功能。',
                features: ['数据预览', '实时更新', '格式化显示', '交互控制'],
                path: 'ui/sidepanel/data/PreviewManager.js',
                priority: 22
            },

            // 工具模块
            DateFormatter: {
                category: 'utils',
                color: '#795548',
                dependencies: [],
                description: '日期格式化工具，处理各种日期格式的转换和显示。',
                features: ['日期格式化', '时区处理', '本地化', '格式转换'],
                path: 'ui/sidepanel/utils/DateFormatter.js',
                priority: 10
            },
            MessageHelper: {
                category: 'utils',
                color: '#795548',
                dependencies: ['EventBus'],
                description: '消息助手，处理 Chrome Extension 的消息传递。',
                features: ['消息传递', 'Chrome API', '异步通信', '错误处理'],
                path: 'ui/sidepanel/utils/MessageHelper.js',
                priority: 11
            },
            DebugLogger: {
                category: 'utils',
                color: '#795548',
                dependencies: ['EventBus'],
                description: '调试日志器，提供开发和生产环境的日志功能。',
                features: ['日志记录', '级别控制', '性能监控', '错误追踪'],
                path: 'ui/sidepanel/utils/DebugLogger.js',
                priority: 2
            },

            // 特色功能模块
            CityViewer: {
                category: 'features',
                color: '#607d8b',
                dependencies: ['EventBus', 'ModalManager'],
                description: '城市选择器，提供马来西亚城市的选择和展示功能。',
                features: ['城市选择', '地图集成', 'API 集成', '智能搜索'],
                path: 'ui/sidepanel/features/CityViewer.js',
                priority: 60
            },
            AutoParseManager: {
                category: 'features',
                color: '#607d8b',
                dependencies: ['EventBus', 'StateManager', 'AIService'],
                description: '自动解析管理器，智能解析和处理用户输入的各种信息。',
                features: ['自动解析', 'AI 辅助', '智能识别', '批处理'],
                path: 'ui/sidepanel/features/AutoParseManager.js',
                priority: 61
            },
            ConfidenceEvaluator: {
                category: 'features',
                color: '#607d8b',
                dependencies: ['EventBus', 'DataValidator'],
                description: '置信度评估器，评估 AI 解析结果的准确性和可信度。',
                features: ['置信度评估', '准确性分析', '质量评分', '建议提示'],
                path: 'ui/sidepanel/features/ConfidenceEvaluator.js',
                priority: 62
            }
        };

        // 当前显示的标签
        let currentTab = 'graph';
        let showLabels = true;
        let svg, simulation, nodes, links;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeGraph();
        });

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有视图
            document.getElementById('graph-view').style.display = 'none';
            document.getElementById('architecture-view').style.display = 'none';
            document.getElementById('bootstrap-view').style.display = 'none';
            document.getElementById('timeline-view').style.display = 'none';
            
            // 移除所有活动标签
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的视图和标签
            document.getElementById(tabName + '-view').style.display = 'block';
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // 初始化图形
        function initializeGraph() {
            const container = document.getElementById('svg');
            const rect = container.getBoundingClientRect();
            
            svg = d3.select('#svg')
                .attr('width', rect.width)
                .attr('height', rect.height);

            // 准备节点和连接数据
            const nodeData = Object.keys(modules).map(name => ({
                id: name,
                ...modules[name]
            }));

            const linkData = [];
            nodeData.forEach(node => {
                node.dependencies.forEach(dep => {
                    if (modules[dep]) {
                        linkData.push({
                            source: dep,
                            target: node.id
                        });
                    }
                });
            });

            // 创建力导向图
            simulation = d3.forceSimulation(nodeData)
                .force('link', d3.forceLink(linkData).id(d => d.id).distance(100))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(rect.width / 2, rect.height / 2))
                .force('collision', d3.forceCollide().radius(40));

            // 添加连接线
            links = svg.append('g')
                .selectAll('line')
                .data(linkData)
                .enter().append('line')
                .attr('class', 'link')
                .attr('stroke-width', 2);

            // 添加节点
            const nodeGroup = svg.append('g')
                .selectAll('g')
                .data(nodeData)
                .enter().append('g')
                .attr('class', 'node')
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));

            // 节点圆圈
            nodeGroup.append('circle')
                .attr('r', d => d.essential ? 25 : 20)
                .attr('fill', d => d.color)
                .attr('stroke', '#fff')
                .attr('stroke-width', 3)
                .on('click', function(event, d) {
                    showModuleDetails(d.id);
                });

            // 节点标签
            nodes = nodeGroup.append('text')
                .attr('class', 'node-text')
                .attr('dy', 4)
                .text(d => d.id)
                .style('display', showLabels ? 'block' : 'none');

            // 更新位置
            simulation.on('tick', () => {
                links
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                nodeGroup
                    .attr('transform', d => `translate(${d.x},${d.y})`);
            });
        }

        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // 控制函数
        function resetGraph() {
            simulation.alpha(1).restart();
        }

        function focusCore() {
            const coreModules = Object.keys(modules).filter(name => modules[name].category === 'core');
            svg.selectAll('.node circle')
                .transition()
                .duration(500)
                .attr('opacity', d => coreModules.includes(d.id) ? 1 : 0.3);
        }

        function showAllConnections() {
            svg.selectAll('.node circle')
                .transition()
                .duration(500)
                .attr('opacity', 1);
        }

        function toggleLabels() {
            showLabels = !showLabels;
            svg.selectAll('.node-text')
                .style('display', showLabels ? 'block' : 'none');
        }

        // 模块详情
        function showModuleDetails(moduleName) {
            const module = modules[moduleName];
            if (!module) return;

            document.getElementById('detail-title').textContent = moduleName;
            document.getElementById('detail-content').innerHTML = `
                <p><strong>类别:</strong> ${getCategoryName(module.category)}</p>
                <p><strong>描述:</strong> ${module.description}</p>
                <p><strong>文件路径:</strong> <code>${module.path}</code></p>
                <p><strong>优先级:</strong> ${module.priority}</p>
                <p><strong>依赖模块:</strong> ${module.dependencies.length > 0 ? module.dependencies.join(', ') : '无'}</p>
                ${module.essential ? '<p><strong>🔥 核心模块</strong></p>' : ''}
                <h4>主要功能:</h4>
                <ul>
                    ${module.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
                ${module.apiEndpoint ? `<p><strong>API 端点:</strong> <code>${module.apiEndpoint}</code></p>` : ''}
            `;

            document.querySelector('.overlay').style.display = 'block';
            document.querySelector('.module-details').style.display = 'block';
        }

        function hideModuleDetails() {
            document.querySelector('.overlay').style.display = 'none';
            document.querySelector('.module-details').style.display = 'none';
        }

        function getCategoryName(category) {
            const names = {
                'core': '核心模块',
                'ai': 'AI 功能',
                'form': '表单处理',
                'ui': 'UI 组件',
                'data': '数据管理',
                'utils': '工具模块',
                'features': '特色功能'
            };
            return names[category] || category;
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            if (currentTab === 'graph') {
                const container = document.getElementById('svg');
                const rect = container.getBoundingClientRect();
                svg.attr('width', rect.width).attr('height', rect.height);
                simulation.force('center', d3.forceCenter(rect.width / 2, rect.height / 2));
                simulation.alpha(1).restart();
            }
        });
    </script>
</body>
</html>
