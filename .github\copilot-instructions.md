---
type: "always_apply"
---

# MDAC Chrome Extension AI Coding Agent Instructions

## Project Overview

**MDAC AI智能分析工具** is a sophisticated Chrome Extension (Manifest V3) that provides AI-powered form analysis and auto-filling for Malaysia Digital Arrival Card websites. The project has undergone extensive modularization, transforming from a 4,601-line monolith into 25+ focused modules.

### Core Architecture
- **Modular Design**: Event-driven architecture with 7 module categories (core, AI, form, UI, features, data, utils)
- **Chrome Extension V3**: Service worker, side panel API, content script injection
- **AI Integration**: Google Gemini 2.5 Flash API for intelligent text/image processing
- **Target Website**: `https://imigresen-online.imi.gov.my/*`

## Critical Architecture Knowledge

### Bootstrap System Architecture

**The project currently uses ultimate-bootstrap.js as the primary bootstrap system:**

1. **ultimate-bootstrap.js** (Current Implementation)
   - Advanced module loading with priority-based dependencies
   - Property protection against duplicate declarations
   - Emergency mode and degraded operation support
   - 25+ modules with proper dependency resolution
   - ✅ **Currently loaded in ui-sidepanel.html**

2. **enhanced-bootstrap.js** (Mentioned in docs but not found in codebase)
3. **simple-bootstrap.js** (Mentioned in docs but not found in codebase)

**Critical:** The project currently uses `ultimate-bootstrap.js` in `ui-sidepanel.html`. This bootstrap prevents "Identifier already declared" errors through property protection.

```javascript
// ✅ Correct: Use the bootstrap system
// Files load automatically via ui/sidepanel/core/ultimate-bootstrap.js
// This prevents "Identifier already declared" errors

// ❌ Wrong: Direct script loading causes duplicate declarations
<script src="ui/sidepanel/core/EventBus.js"></script>
```

### Event-Driven Communication
All modules communicate through a global event bus. This is the backbone of the entire system:

```javascript
// Module-to-module communication
window.mdacEventBus.emit('ai:analysis-complete', data);
window.mdacEventBus.on('form:field-detected', handler);

// Check if event bus is ready
if (!window.mdacEventBus) {
  console.error('❌ EventBus not initialized - module loading failed');
}
```

### Module Development Pattern
Every module follows this exact structure:

```javascript
/**
 * ModuleName - Description
 * Dependencies: [EventBus, StateManager, ...]
 * Global Export: window.ModuleName
 */
class ModuleName {
    constructor(eventBus, stateManager) {
        this.eventBus = eventBus;
        this.stateManager = stateManager;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;
        // Setup logic here
        this.initialized = true;
        console.log(`✅ [${this.constructor.name}] 模块初始化完成`);
    }
}

// CRITICAL: Global export for module registry
window.ModuleName = ModuleName;
```

## Development Workflows

### Loading Order is Critical
Modules must load in priority order or the system breaks:

1. **Priority 1**: EventBus, DebugLogger (foundation)
2. **Priority 2**: ModuleRegistry, StateManager, EventManager (core)
3. **Priority 3**: StorageService, DataManager (data layer)
4. **Priority 4**: FormFiller, DataValidator (business logic)
5. **Priority 5**: AIService, UI components (features)

### 🚨 Module Loading Error Recovery
If you see `⚠️ [UltimateBootstrap] 模块 XXX 脚本已加载但全局对象未找到` errors:

**Root Cause**: Modules using improper duplicate declaration protection that prevents global export.

**Quick Fix Commands**:
```javascript
// 1. Check which bootstrap is loaded
console.log('Bootstrap loaded:', !!window.mdacUltimateBootstrap);

// 2. Verify specific module availability
console.log('EventManager available:', !!window.EventManager);
console.log('StateManager available:', !!window.StateManager);

// 3. Check module loading status
console.log('Ultimate bootstrap status:', window.mdacUltimateBootstrap?.getStats());

// 4. Force reload specific modules if needed
if (!window.EventManager && window.mdacUltimateBootstrap) {
  window.mdacUltimateBootstrap.loadModule('EventManager');
}
```

**Code Pattern Fix**:
When creating new modules, use this pattern to prevent export issues:
```javascript
// ❌ Wrong: Complex condition that may prevent export
if (window.ModuleName && typeof window.ModuleName === 'function') {
    console.warn('Module exists, skipping');
} else {
    class ModuleName { ... }
}
// Export happens outside conditional block, causing undefined errors

// ✅ Correct: Simple condition with safe export
if (typeof window.ModuleName !== 'undefined') {
    console.warn('Module exists, skipping');
} else {
    class ModuleName { ... }
}
// Export with safety check
if (typeof window.ModuleName === 'undefined') {
    window.ModuleName = ModuleName;
}
```

### Debugging Workflow
```bash
# 1. Open Chrome DevTools Console
# 2. Check initialization status
console.log(window.mdacModularSidePanel?.getModuleStatus());

# 3. Enable debug mode
window.mdacModularSidePanel?.toggleDebugMode();

# 4. Check for failed modules
console.log('Failed modules:', window.mdacModularSidePanel?.getModuleStatus()?.failedModules);

# 5. Bootstrap diagnostics
console.log('Bootstrap stats:', window.mdacUltimateBootstrap?.getStats());
console.log('Loading report:', window.mdacBootstrapReport);
```

### Testing Workflow
```javascript
// Run all integration tests
const test = new ModularIntegrationTest();
await test.runAllTests();

// Test specific module loading
window.mdacUltimateBootstrap.loadModule('FailedModuleName');

// Check bootstrap stats
console.log('Bootstrap stats:', window.mdacUltimateBootstrap.getStats());
```

### Adding New Modules
1. Create module in appropriate `ui/sidepanel/[category]/` folder
2. Add to `manifest.json` web_accessible_resources
3. Update ultimate-bootstrap.js module definitions
4. Add priority and dependencies correctly
5. Test module loading order

## Chrome Extension Specific Patterns

### Message Passing Between Components
```javascript
// Background ↔ Side Panel
chrome.runtime.sendMessage({
    action: 'ai-analyze-text',
    data: { text: userInput }
});

// Content Script ↔ Side Panel (via background)
chrome.runtime.sendMessage({
    action: 'content-log', 
    type: 'form-field-detected',
    data: fieldInfo
});

// Always handle async responses with error checking
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'ai-analyze-text') {
        handleAIAnalysis(request.data)
            .then(result => sendResponse({ success: true, data: result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Keep channel open
    }
});
```

### Content Script Integration Challenges
The content script uses adapter pattern to bridge old and new systems:

```javascript
// content-script-adapter.js provides compatibility classes
// OLD API: new MDACLogger() 
// NEW: Forwards to modular DebugLogger via messaging

// Available adapter classes:
// - MDACLogger, MDACDebugConsole, FormFieldDetector
// - ErrorRecoveryManager, FillMonitor, ProgressVisualizer

// When working with content scripts, remember:
// 1. Content scripts can't access window.mdacModularSidePanel directly
// 2. Use chrome.runtime.sendMessage for communication
// 3. Adapter classes maintain API compatibility
```

### Side Panel API Usage
```javascript
// Open side panel programmatically
chrome.sidePanel.open({ tabId: tabs[0].id });

// Side panel automatically loads ui/ui-sidepanel.html
// Which then loads ultimate-bootstrap.js → all modules
```

## Critical Pitfalls to Avoid

### Module Loading Issues (Most Common)
```javascript
// ❌ NEVER load modules directly in HTML - causes "Identifier already declared"
<script src="ui/sidepanel/core/EventBus.js"></script>
<script src="ui/sidepanel/core/StateManager.js"></script>

// ✅ ALWAYS use the bootstrap system
<script src="ui/sidepanel/core/ultimate-bootstrap.js"></script>
// Everything else loads automatically with proper dependency order
```

### Event System Anti-patterns
```javascript
// ❌ Don't create multiple EventBus instances
const myEventBus = new EventBus(); // Wrong!

// ✅ Use the global instance
window.mdacEventBus.emit('my-event', data);

// ❌ Don't access modules before they're loaded
window.mdacModularSidePanel.modules.aiService.analyze(); // May be undefined!

// ✅ Check initialization first
if (window.mdacModularSidePanel?.isInitialized()) {
    const status = window.mdacModularSidePanel.getModuleStatus();
    if (status.loaded.includes('AIService')) {
        // Safe to use
    }
}
```

### Chrome Extension Gotchas
```javascript
// ❌ Don't use synchronous storage
const data = chrome.storage.sync.get(['key']); // Wrong!

// ✅ Always use async/await
const data = await chrome.storage.sync.get(['key']);

// ❌ Don't ignore chrome.runtime.lastError
chrome.tabs.sendMessage(tabId, message);

// ✅ Always check for errors
chrome.tabs.sendMessage(tabId, message, (response) => {
    if (chrome.runtime.lastError) {
        console.error('Message failed:', chrome.runtime.lastError);
    }
});
```

### 🔧 Configuration Management

#### AI Configuration
```javascript
// Access AI config through global
const config = window.MDAC_AI_CONFIG || window.GEMINI_CONFIG;

// Validate configuration before use
if (!config?.apiKey) {
    throw new Error('AI configuration missing');
}
```

#### Feature Flags
```javascript
// Check feature availability
const autoParseEnabled = this.stateManager.getState('autoParseEnabled');
if (autoParseEnabled) {
    await this.autoParseManager.startParsing();
}
```

### 📚 Key Dependencies

#### External Libraries
- **Bootstrap 5.3.0**: UI framework
- **Google Fonts**: Typography (Roboto, Material Icons)

#### Chrome APIs
- `chrome.sidePanel`: Main UI container
- `chrome.storage`: Data persistence
- `chrome.scripting`: Content script injection
- `chrome.runtime`: Message passing
- `chrome.tabs`: Tab management

#### AI Services
- **Google Gemini 2.5 Flash**: Primary AI service
- **Endpoint**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent`
- **Default API Key**: Pre-configured in `config/ai-config.js` 
- **Model**: `gemini-2.5-flash-lite-preview-06-17`

### 🏗️ Actual Project Architecture

#### Bootstrap Loading Chain
```
ui-sidepanel.html → ultimate-bootstrap.js → ui-sidepanel-modular.js → 25+ modules
```

#### Configuration Files Priority
```javascript
// Primary configs loaded in order:
window.GEMINI_CONFIG          // config/ai-config.js
window.MDAC_AI_CONFIG         // config/enhanced-ai-config.js  
window.MDAC_FIELD_CONFIG      // Field mappings
window.MDAC_PERFORMANCE_CONFIG // Performance settings
```

#### Critical Global Variables
```javascript
window.mdacUltimateBootstrap      // Bootstrap instance
window.mdacEventBus              // Event system
window.mdacModularSidePanel      // Main app instance
window.mdacBootstrapReport       // Loading diagnostics
```

### 🔄 Migration Notes

#### Legacy Module System
- Old modules in `/modules/` are being migrated to modular structure
- Use compatibility layer (`LegacyAdapter.js`) when needed
- Gradually phase out legacy dependencies

#### Recent Solutions
- **Solution 1** (simple-bootstrap.js): Basic unified loading ✅ Completed
- **Solution 2** (ultimate-bootstrap.js): Advanced dependency management ✅ Current Implementation

### 📋 Development Checklist

When adding new features:
- [ ] Define module dependencies clearly
- [ ] Implement proper error handling
- [ ] Add logging for debugging
- [ ] Test in isolation and integration
- [ ] Update module registry if needed
- [ ] Document public interfaces
- [ ] Ensure Chrome extension compatibility
- [ ] Test on target websites (imigresen-online.imi.gov.my)

When debugging issues:
- [ ] Check browser console for errors
- [ ] Verify module loading order
- [ ] Confirm EventBus initialization
- [ ] Validate Chrome extension permissions
- [ ] Test with ultimate-bootstrap.js retry mechanisms
- [ ] Use provided testing tools

### 🎯 Success Metrics

- ✅ Zero "Identifier already declared" errors
- ✅ Zero "ModuleRegistry is not defined" errors
- ✅ All 25+ modules load successfully
- ✅ Retry mechanisms handle network issues
- ✅ Degraded mode available for critical failures
- ✅ User-friendly error feedback
- ✅ Comprehensive error logging

---

**Remember**: This is a sophisticated modular Chrome extension with complex dependency management. Always use the ultimate bootstrap system, follow the event-driven architecture, and maintain the high code quality standards established in the codebase.

When in doubt, check the existing module implementations in `ui/sidepanel/` for patterns and refer to the comprehensive documentation in `MODULAR_REFACTOR_SUMMARY.md` and related reports.
