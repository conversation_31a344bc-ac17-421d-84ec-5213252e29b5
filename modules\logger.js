/**
 * Logger模块 - 兼容性存根
 * Content Script Adapter中已提供MDACLogger，此文件确保路径存在
 * 创建日期: 2025-01-11
 */

// 此文件为空，因为MDACLogger已在content-script-adapter.js中定义
// 保留此文件仅为确保content-script.js中的模块加载路径有效

console.log('📝 [modules/logger.js] 兼容性存根文件已加载 - MDACLogger在adapter中提供');

// 确保MDACLogger可用的后备方案
if (typeof window.MDACLogger === 'undefined') {
    console.warn('⚠️ [modules/logger.js] MDACLogger未定义，可能是adapter加载失败');
    
    // 提供基础的兼容性实现
    window.MDACLogger = class {
        constructor() {
            this.logs = [];
            console.log('📝 [MDACLogger] 后备日志器已初始化');
        }
        
        log(level, module, message, data = null) {
            console.log(`[${level}] [${module}] ${message}`, data || '');
        }
        
        info(module, message, data = null) { this.log('INFO', module, message, data); }
        warn(module, message, data = null) { this.log('WARN', module, message, data); }
        error(module, message, data = null) { this.log('ERROR', module, message, data); }
        debug(module, message, data = null) { this.log('DEBUG', module, message, data); }
        clear() { this.logs = []; }
        getLogs() { return this.logs; }
        setEnabled(enabled) { this.isEnabled = enabled; }
        outputToConsole() { /* 空实现 */ }
    };
    
    if (!window.mdacLogger) {
        window.mdacLogger = new window.MDACLogger();
    }
}
