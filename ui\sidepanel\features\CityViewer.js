/**
 * 城市查看器 - 马来西亚城市数据查看和管理
 * 负责显示、搜索和管理马来西亚城市信息
 * 创建日期: 2025-01-11
 */

class CityViewer {
    constructor(eventBus = window.mdacEventBus, modalManager = null) {
        this.eventBus = eventBus;
        this.modalManager = modalManager;
        
        // 城市数据
        this.cityData = null;
        this.filteredCities = [];
        this.currentView = 'list'; // list, grid, map
        
        // 搜索和过滤
        this.searchQuery = '';
        this.selectedState = '';
        this.sortBy = 'name'; // name, state, population
        this.sortOrder = 'asc'; // asc, desc
        
        // 分页
        this.currentPage = 1;
        this.itemsPerPage = 20;
        
        // UI元素
        this.container = null;
        this.searchInput = null;
        this.stateFilter = null;
        this.cityList = null;
        
        // 配置
        this.config = {
            enableMap: true,
            enableExport: true,
            enableFavorites: true,
            showPopulation: true,
            showCoordinates: false
        };

        // 收藏城市
        this.favoriteCities = new Set();
        
        // 统计信息
        this.stats = {
            totalCities: 0,
            totalStates: 0,
            searchCount: 0,
            lastUpdated: null
        };

        console.log('🏙️ [CityViewer] 城市查看器已初始化');
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        if (!this.eventBus) return;

        // 监听城市查看器显示请求
        this.eventBus.on('cityViewer:show', (options) => {
            this.show(options);
        });

        // 监听城市选择请求
        this.eventBus.on('cityViewer:select-city', (callback) => {
            this.showCitySelector(callback);
        });

        // 监听配置更新
        this.eventBus.on('cityViewer:config-updated', (config) => {
            this.updateConfig(config);
        });
    }

    /**
     * 初始化城市查看器
     */
    async initialize() {
        try {
            console.log('🏙️ [CityViewer] 开始初始化');

            // 加载城市数据
            await this.loadCityData();
            
            // 加载收藏城市
            await this.loadFavorites();
            
            // 初始化统计信息
            this.updateStats();

            console.log('✅ [CityViewer] 初始化完成');

            // 发布初始化完成事件
            if (this.eventBus) {
                this.eventBus.emit('cityViewer:initialized', {
                    totalCities: this.stats.totalCities,
                    totalStates: this.stats.totalStates,
                    timestamp: Date.now()
                });
            }

        } catch (error) {
            console.error('❌ [CityViewer] 初始化失败', error);
            throw error;
        }
    }

    /**
     * 加载城市数据
     */
    async loadCityData() {
        try {
            // 尝试从本地存储加载
            const cachedData = await this.loadFromStorage();
            if (cachedData && this.isDataValid(cachedData)) {
                this.cityData = cachedData;
                console.log('📋 [CityViewer] 从缓存加载城市数据');
                return;
            }

            // 从文件加载
            const response = await fetch(chrome.runtime.getURL('data/malaysia-states-cities.json'));
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.cityData = await response.json();
            
            // 处理和验证数据
            this.processCityData();
            
            // 保存到本地存储
            await this.saveToStorage();

            console.log('📋 [CityViewer] 城市数据加载完成', {
                states: Object.keys(this.cityData).length,
                totalCities: this.getTotalCityCount()
            });

        } catch (error) {
            console.error('❌ [CityViewer] 城市数据加载失败', error);
            
            // 使用备用数据
            this.cityData = this.getFallbackData();
        }
    }

    /**
     * 处理城市数据
     */
    processCityData() {
        if (!this.cityData || typeof this.cityData !== 'object') {
            throw new Error('无效的城市数据格式');
        }

        // 标准化数据格式
        Object.keys(this.cityData).forEach(state => {
            if (Array.isArray(this.cityData[state])) {
                // 如果是简单数组，转换为对象格式
                this.cityData[state] = this.cityData[state].map(city => {
                    if (typeof city === 'string') {
                        return {
                            name: city,
                            nameEn: this.translateToEnglish(city),
                            nameMy: this.translateToMalay(city),
                            state: state,
                            coordinates: null,
                            population: null,
                            postcode: null
                        };
                    }
                    return {
                        ...city,
                        state: state,
                        nameEn: city.nameEn || this.translateToEnglish(city.name),
                        nameMy: city.nameMy || this.translateToMalay(city.name)
                    };
                });
            }
        });

        // 添加搜索索引
        this.buildSearchIndex();
    }

    /**
     * 构建搜索索引
     */
    buildSearchIndex() {
        this.searchIndex = new Map();
        
        Object.entries(this.cityData).forEach(([state, cities]) => {
            cities.forEach((city, index) => {
                const searchTerms = [
                    city.name.toLowerCase(),
                    city.nameEn?.toLowerCase(),
                    city.nameMy?.toLowerCase(),
                    state.toLowerCase()
                ].filter(Boolean);

                searchTerms.forEach(term => {
                    if (!this.searchIndex.has(term)) {
                        this.searchIndex.set(term, []);
                    }
                    this.searchIndex.get(term).push({ state, city, index });
                });
            });
        });
    }

    /**
     * 显示城市查看器
     * @param {Object} options - 显示选项
     */
    show(options = {}) {
        if (!this.modalManager) {
            console.error('❌ [CityViewer] ModalManager不可用');
            return;
        }

        const {
            title = '马来西亚城市查看器',
            width = '900px',
            height = '700px',
            initialState = '',
            initialSearch = ''
        } = options;

        // 设置初始状态
        this.selectedState = initialState;
        this.searchQuery = initialSearch;

        // 创建城市查看器界面
        const content = this.createViewerInterface();

        // 显示模态框
        const modal = this.modalManager.showModal('custom', {
            title: title,
            content: content,
            width: width,
            height: height,
            showHeader: true,
            showFooter: true,
            buttons: [
                {
                    text: '关闭',
                    action: 'close',
                    class: 'secondary'
                },
                {
                    text: '导出数据',
                    action: 'export',
                    class: 'primary',
                    value: 'export'
                }
            ]
        });

        // 绑定事件
        this.bindViewerEvents(modal);

        // 初始化界面
        this.initializeViewer();

        return modal;
    }

    /**
     * 创建查看器界面
     */
    createViewerInterface() {
        return `
            <div class="city-viewer">
                <div class="viewer-header">
                    <div class="search-section">
                        <div class="search-group">
                            <input type="text" 
                                   id="city-search" 
                                   class="search-input" 
                                   placeholder="搜索城市名称..."
                                   value="${this.searchQuery}">
                            <button class="search-button" id="search-button">🔍</button>
                        </div>
                        
                        <div class="filter-group">
                            <select id="state-filter" class="state-filter">
                                <option value="">所有州属</option>
                                ${this.getStateOptions()}
                            </select>
                            
                            <select id="sort-select" class="sort-select">
                                <option value="name">按名称排序</option>
                                <option value="state">按州属排序</option>
                                <option value="population">按人口排序</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="view-controls">
                        <div class="view-buttons">
                            <button class="view-button active" data-view="list">📋 列表</button>
                            <button class="view-button" data-view="grid">⊞ 网格</button>
                            ${this.config.enableMap ? '<button class="view-button" data-view="map">🗺️ 地图</button>' : ''}
                        </div>
                        
                        <div class="stats-info">
                            <span id="result-count">0 个城市</span>
                        </div>
                    </div>
                </div>
                
                <div class="viewer-content">
                    <div class="city-list" id="city-list">
                        <!-- 城市列表将在这里动态生成 -->
                    </div>
                    
                    <div class="pagination" id="pagination">
                        <!-- 分页控件将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="viewer-footer">
                    <div class="footer-stats">
                        <span>总计: ${this.stats.totalCities} 个城市，${this.stats.totalStates} 个州属</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取州属选项
     */
    getStateOptions() {
        if (!this.cityData) return '';
        
        return Object.keys(this.cityData)
            .sort()
            .map(state => `<option value="${state}" ${state === this.selectedState ? 'selected' : ''}>${state}</option>`)
            .join('');
    }

    /**
     * 绑定查看器事件
     * @param {Object} modal - 模态框对象
     */
    bindViewerEvents(modal) {
        const modalElement = modal.element;
        
        // 搜索事件
        const searchInput = modalElement.querySelector('#city-search');
        const searchButton = modalElement.querySelector('#search-button');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.debounceSearch();
            });
            
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // 过滤事件
        const stateFilter = modalElement.querySelector('#state-filter');
        if (stateFilter) {
            stateFilter.addEventListener('change', (e) => {
                this.selectedState = e.target.value;
                this.performSearch();
            });
        }

        // 排序事件
        const sortSelect = modalElement.querySelector('#sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.performSearch();
            });
        }

        // 视图切换事件
        const viewButtons = modalElement.querySelectorAll('.view-button');
        viewButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.switchView(view);
                
                // 更新按钮状态
                viewButtons.forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // 城市列表事件委托
        const cityList = modalElement.querySelector('#city-list');
        if (cityList) {
            cityList.addEventListener('click', (e) => {
                this.handleCityListClick(e);
            });
        }

        // 模态框按钮事件
        modalElement.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'export') {
                this.exportCityData();
            }
        });
    }

    /**
     * 初始化查看器
     */
    initializeViewer() {
        // 执行初始搜索
        this.performSearch();
        
        // 设置容器引用
        this.container = document.querySelector('.city-viewer');
        this.searchInput = document.querySelector('#city-search');
        this.stateFilter = document.querySelector('#state-filter');
        this.cityList = document.querySelector('#city-list');
    }

    /**
     * 防抖搜索
     */
    debounceSearch() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        this.searchTimeout = setTimeout(() => {
            this.performSearch();
        }, 300);
    }

    /**
     * 执行搜索
     */
    performSearch() {
        console.log('🔍 [CityViewer] 执行搜索', {
            query: this.searchQuery,
            state: this.selectedState,
            sortBy: this.sortBy
        });

        // 更新统计
        this.stats.searchCount++;

        // 获取所有城市
        let allCities = this.getAllCities();

        // 应用搜索过滤
        if (this.searchQuery.trim()) {
            allCities = this.filterBySearch(allCities, this.searchQuery.trim());
        }

        // 应用州属过滤
        if (this.selectedState) {
            allCities = allCities.filter(city => city.state === this.selectedState);
        }

        // 排序
        allCities = this.sortCities(allCities, this.sortBy, this.sortOrder);

        // 更新过滤结果
        this.filteredCities = allCities;

        // 重置分页
        this.currentPage = 1;

        // 渲染结果
        this.renderCities();
        this.renderPagination();
        this.updateResultCount();

        // 发布搜索事件
        if (this.eventBus) {
            this.eventBus.emit('cityViewer:search-performed', {
                query: this.searchQuery,
                state: this.selectedState,
                resultCount: this.filteredCities.length,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取所有城市
     */
    getAllCities() {
        const cities = [];

        if (!this.cityData) return cities;

        Object.entries(this.cityData).forEach(([state, stateCities]) => {
            stateCities.forEach(city => {
                cities.push({
                    ...city,
                    state: state
                });
            });
        });

        return cities;
    }

    /**
     * 按搜索条件过滤
     * @param {Array} cities - 城市列表
     * @param {string} query - 搜索查询
     */
    filterBySearch(cities, query) {
        const queryLower = query.toLowerCase();

        return cities.filter(city => {
            return (
                city.name.toLowerCase().includes(queryLower) ||
                city.nameEn?.toLowerCase().includes(queryLower) ||
                city.nameMy?.toLowerCase().includes(queryLower) ||
                city.state.toLowerCase().includes(queryLower)
            );
        });
    }

    /**
     * 排序城市
     * @param {Array} cities - 城市列表
     * @param {string} sortBy - 排序字段
     * @param {string} order - 排序顺序
     */
    sortCities(cities, sortBy, order = 'asc') {
        return cities.sort((a, b) => {
            let valueA, valueB;

            switch (sortBy) {
                case 'name':
                    valueA = a.name.toLowerCase();
                    valueB = b.name.toLowerCase();
                    break;
                case 'state':
                    valueA = a.state.toLowerCase();
                    valueB = b.state.toLowerCase();
                    break;
                case 'population':
                    valueA = a.population || 0;
                    valueB = b.population || 0;
                    break;
                default:
                    valueA = a.name.toLowerCase();
                    valueB = b.name.toLowerCase();
            }

            if (order === 'desc') {
                return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
            } else {
                return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
            }
        });
    }

    /**
     * 渲染城市列表
     */
    renderCities() {
        if (!this.cityList) return;

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageCities = this.filteredCities.slice(startIndex, endIndex);

        if (pageCities.length === 0) {
            this.cityList.innerHTML = this.createEmptyState();
            return;
        }

        let html = '';

        switch (this.currentView) {
            case 'list':
                html = this.renderListView(pageCities);
                break;
            case 'grid':
                html = this.renderGridView(pageCities);
                break;
            case 'map':
                html = this.renderMapView(pageCities);
                break;
            default:
                html = this.renderListView(pageCities);
        }

        this.cityList.innerHTML = html;
    }

    /**
     * 渲染列表视图
     * @param {Array} cities - 城市列表
     */
    renderListView(cities) {
        return `
            <div class="city-list-view">
                <div class="list-header">
                    <div class="header-cell name">城市名称</div>
                    <div class="header-cell state">州属</div>
                    ${this.config.showPopulation ? '<div class="header-cell population">人口</div>' : ''}
                    <div class="header-cell actions">操作</div>
                </div>
                ${cities.map(city => this.createCityListItem(city)).join('')}
            </div>
        `;
    }

    /**
     * 创建城市列表项
     * @param {Object} city - 城市信息
     */
    createCityListItem(city) {
        const isFavorite = this.favoriteCities.has(this.getCityId(city));

        return `
            <div class="city-item" data-city-id="${this.getCityId(city)}">
                <div class="item-cell name">
                    <div class="city-name">${city.name}</div>
                    ${city.nameEn ? `<div class="city-name-en">${city.nameEn}</div>` : ''}
                    ${city.nameMy ? `<div class="city-name-my">${city.nameMy}</div>` : ''}
                </div>
                <div class="item-cell state">${city.state}</div>
                ${this.config.showPopulation ? `<div class="item-cell population">${city.population || 'N/A'}</div>` : ''}
                <div class="item-cell actions">
                    <button class="action-button favorite ${isFavorite ? 'active' : ''}"
                            data-action="favorite"
                            title="${isFavorite ? '取消收藏' : '添加收藏'}">
                        ${isFavorite ? '★' : '☆'}
                    </button>
                    <button class="action-button select"
                            data-action="select"
                            title="选择此城市">
                        ✓
                    </button>
                    <button class="action-button info"
                            data-action="info"
                            title="查看详情">
                        ℹ
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染网格视图
     * @param {Array} cities - 城市列表
     */
    renderGridView(cities) {
        return `
            <div class="city-grid-view">
                ${cities.map(city => this.createCityGridItem(city)).join('')}
            </div>
        `;
    }

    /**
     * 创建城市网格项
     * @param {Object} city - 城市信息
     */
    createCityGridItem(city) {
        const isFavorite = this.favoriteCities.has(this.getCityId(city));

        return `
            <div class="city-card" data-city-id="${this.getCityId(city)}">
                <div class="card-header">
                    <h4 class="city-name">${city.name}</h4>
                    <button class="favorite-button ${isFavorite ? 'active' : ''}"
                            data-action="favorite">
                        ${isFavorite ? '★' : '☆'}
                    </button>
                </div>
                <div class="card-content">
                    <div class="city-info">
                        <div class="info-item">
                            <span class="label">州属:</span>
                            <span class="value">${city.state}</span>
                        </div>
                        ${city.nameEn ? `
                            <div class="info-item">
                                <span class="label">英文:</span>
                                <span class="value">${city.nameEn}</span>
                            </div>
                        ` : ''}
                        ${city.population ? `
                            <div class="info-item">
                                <span class="label">人口:</span>
                                <span class="value">${city.population.toLocaleString()}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-button select" data-action="select">选择</button>
                    <button class="action-button info" data-action="info">详情</button>
                </div>
            </div>
        `;
    }

    /**
     * 渲染地图视图
     * @param {Array} cities - 城市列表
     */
    renderMapView(cities) {
        return `
            <div class="city-map-view">
                <div class="map-placeholder">
                    <div class="map-icon">🗺️</div>
                    <div class="map-text">地图功能开发中</div>
                    <div class="map-hint">将显示 ${cities.length} 个城市的位置</div>
                </div>
                <div class="map-city-list">
                    ${cities.slice(0, 10).map(city => `
                        <div class="map-city-item" data-city-id="${this.getCityId(city)}">
                            <span class="city-name">${city.name}</span>
                            <span class="city-state">${city.state}</span>
                        </div>
                    `).join('')}
                    ${cities.length > 10 ? `<div class="more-cities">还有 ${cities.length - 10} 个城市...</div>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 创建空状态
     */
    createEmptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">🏙️</div>
                <div class="empty-title">未找到城市</div>
                <div class="empty-message">
                    ${this.searchQuery ? `没有找到包含 "${this.searchQuery}" 的城市` : '没有符合条件的城市'}
                </div>
                <button class="empty-action" onclick="this.closest('.city-viewer').querySelector('#city-search').value=''; this.closest('.city-viewer').querySelector('#city-search').dispatchEvent(new Event('input'))">
                    清除搜索
                </button>
            </div>
        `;
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const paginationElement = document.querySelector('#pagination');
        if (!paginationElement) return;

        const totalPages = Math.ceil(this.filteredCities.length / this.itemsPerPage);

        if (totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }

        let html = '<div class="pagination-controls">';

        // 上一页
        html += `
            <button class="page-button ${this.currentPage === 1 ? 'disabled' : ''}"
                    data-page="${this.currentPage - 1}"
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                ‹ 上一页
            </button>
        `;

        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            html += `<button class="page-button" data-page="1">1</button>`;
            if (startPage > 2) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="page-button ${i === this.currentPage ? 'active' : ''}"
                        data-page="${i}">
                    ${i}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="page-ellipsis">...</span>`;
            }
            html += `<button class="page-button" data-page="${totalPages}">${totalPages}</button>`;
        }

        // 下一页
        html += `
            <button class="page-button ${this.currentPage === totalPages ? 'disabled' : ''}"
                    data-page="${this.currentPage + 1}"
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                下一页 ›
            </button>
        `;

        html += '</div>';

        paginationElement.innerHTML = html;

        // 绑定分页事件
        paginationElement.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-button') && !e.target.disabled) {
                const page = parseInt(e.target.dataset.page);
                if (page && page !== this.currentPage) {
                    this.currentPage = page;
                    this.renderCities();
                    this.renderPagination();
                }
            }
        });
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CityViewer;
} else {
    window.CityViewer = CityViewer;
}

console.log('✅ [CityViewer] 城市查看器已加载');
