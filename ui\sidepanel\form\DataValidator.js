/**
 * 数据验证器 - 表单数据验证和格式转换
 * 负责验证数据完整性、格式正确性和业务逻辑
 * 创建日期: 2025-01-11
 */

class DataValidator {
    constructor(eventBus = window.mdacEventBus, dateFormatter = null) {
        this.eventBus = eventBus;
        this.dateFormatter = dateFormatter || (window.DateFormatter ? new DateFormatter() : null);
        
        // 验证规则配置
        this.validationRules = {
            // 个人信息验证规则
            personalInfo: {
                name: {
                    required: true,
                    minLength: 2,
                    maxLength: 50,
                    pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
                    errorMessage: '姓名必须为2-50个字符，只能包含中文、英文和空格'
                },
                gender: {
                    required: false,
                    enum: ['男', '女', 'Male', 'Female', 'M', 'F'],
                    normalize: (value) => {
                        const v = value.toLowerCase();
                        if (v.includes('男') || v.includes('male') || v === 'm') return '男';
                        if (v.includes('女') || v.includes('female') || v === 'f') return '女';
                        return value;
                    },
                    errorMessage: '性别必须为：男、女、Male、Female、M、F'
                },
                birthDate: {
                    required: false,
                    type: 'date',
                    format: 'DD/MM/YYYY',
                    minAge: 0,
                    maxAge: 120,
                    errorMessage: '出生日期格式不正确或年龄不合理'
                },
                nationality: {
                    required: false,
                    minLength: 2,
                    maxLength: 30,
                    errorMessage: '国籍长度必须为2-30个字符'
                },
                passportNumber: {
                    required: false,
                    pattern: /^[A-Z][0-9]{8}$/,
                    normalize: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, ''),
                    errorMessage: '护照号码格式不正确（应为1个字母+8个数字）'
                }
            },
            
            // 联系信息验证规则
            contactInfo: {
                phone: {
                    required: false,
                    pattern: /^\+?[0-9\-\s\(\)]{8,15}$/,
                    normalize: (value) => {
                        let phone = value.replace(/[\(\)\s\-]/g, '');
                        if (phone.startsWith('0')) {
                            phone = '+60' + phone.substring(1);
                        } else if (!phone.startsWith('+')) {
                            phone = '+' + phone;
                        }
                        return phone;
                    },
                    errorMessage: '电话号码格式不正确'
                },
                email: {
                    required: false,
                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                    normalize: (value) => value.toLowerCase().trim(),
                    errorMessage: '邮箱格式不正确'
                },
                address: {
                    required: false,
                    minLength: 5,
                    maxLength: 200,
                    errorMessage: '地址长度必须为5-200个字符'
                }
            },
            
            // 旅行信息验证规则
            travelInfo: {
                destination: {
                    required: false,
                    minLength: 2,
                    maxLength: 50,
                    errorMessage: '目的地长度必须为2-50个字符'
                },
                entryDate: {
                    required: false,
                    type: 'date',
                    format: 'DD/MM/YYYY',
                    minDate: 'today',
                    errorMessage: '入境日期不能早于今天'
                },
                exitDate: {
                    required: false,
                    type: 'date',
                    format: 'DD/MM/YYYY',
                    minDate: 'entryDate',
                    errorMessage: '离境日期不能早于入境日期'
                },
                accommodation: {
                    required: false,
                    minLength: 5,
                    maxLength: 200,
                    errorMessage: '住宿地址长度必须为5-200个字符'
                }
            },
            
            // 交通信息验证规则
            transportInfo: {
                flightNumber: {
                    required: false,
                    pattern: /^[A-Z]{2}[0-9]{1,4}$/,
                    normalize: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, ''),
                    errorMessage: '航班号格式不正确（应为2个字母+1-4个数字）'
                },
                vehicleNumber: {
                    required: false,
                    pattern: /^[A-Z0-9]+$/,
                    normalize: (value) => value.toUpperCase().replace(/[^A-Z0-9]/g, ''),
                    validator: (value) => {
                        // 马来西亚车牌号验证
                        const patterns = [
                            /^[A-Z]{1,3}[0-9]{1,4}[A-Z]?$/,
                            /^[A-Z]{2}[0-9]{4}[A-Z]$/
                        ];
                        return patterns.some(pattern => pattern.test(value));
                    },
                    errorMessage: '车牌号格式不正确'
                },
                modeOfTravel: {
                    required: false,
                    enum: ['AIR', 'LAND', 'SEA'],
                    normalize: (value) => value.toUpperCase(),
                    errorMessage: '交通方式必须为：AIR、LAND、SEA'
                }
            }
        };

        // 业务逻辑验证规则
        this.businessRules = [
            {
                name: 'dateLogic',
                description: '日期逻辑验证',
                validator: this.validateDateLogic.bind(this)
            },
            {
                name: 'transportLogic',
                description: '交通信息逻辑验证',
                validator: this.validateTransportLogic.bind(this)
            },
            {
                name: 'completeness',
                description: '数据完整性验证',
                validator: this.validateCompleteness.bind(this)
            }
        ];

        console.log('✅ [DataValidator] 数据验证器已初始化');
    }

    /**
     * 验证数据
     * @param {Object} data - 要验证的数据
     * @param {Object} options - 验证选项
     */
    validateData(data, options = {}) {
        try {
            console.log('🔍 [DataValidator] 开始数据验证', { data, options });

            const {
                strict = false,
                skipBusinessRules = false,
                normalizeData = true
            } = options;

            const validationResult = {
                isValid: true,
                errors: [],
                warnings: [],
                fieldValidation: {},
                normalizedData: normalizeData ? JSON.parse(JSON.stringify(data)) : data,
                timestamp: Date.now()
            };

            // 字段级验证
            this.validateFields(data, validationResult, strict);

            // 数据标准化
            if (normalizeData) {
                this.normalizeData(validationResult.normalizedData, validationResult);
            }

            // 业务逻辑验证
            if (!skipBusinessRules) {
                this.validateBusinessLogic(validationResult.normalizedData, validationResult);
            }

            // 设置总体验证状态
            validationResult.isValid = validationResult.errors.length === 0;

            console.log('✅ [DataValidator] 数据验证完成', {
                isValid: validationResult.isValid,
                errorCount: validationResult.errors.length,
                warningCount: validationResult.warnings.length
            });

            // 发布验证完成事件
            if (this.eventBus) {
                this.eventBus.emit('data:validation-complete', validationResult);
            }

            return validationResult;

        } catch (error) {
            console.error('❌ [DataValidator] 数据验证失败', error);
            
            const errorResult = {
                isValid: false,
                errors: [`验证过程出错: ${error.message}`],
                warnings: [],
                fieldValidation: {},
                normalizedData: data,
                timestamp: Date.now()
            };

            // 发布验证错误事件
            if (this.eventBus) {
                this.eventBus.emit('data:validation-error', errorResult);
            }

            return errorResult;
        }
    }

    /**
     * 验证字段
     * @param {Object} data - 数据
     * @param {Object} result - 验证结果
     * @param {boolean} strict - 严格模式
     */
    validateFields(data, result, strict) {
        Object.entries(this.validationRules).forEach(([category, fields]) => {
            if (!data[category]) return;

            Object.entries(fields).forEach(([fieldName, rule]) => {
                const value = data[category][fieldName];
                const fieldPath = `${category}.${fieldName}`;
                
                const fieldResult = this.validateSingleField(value, rule, fieldPath, strict);
                
                if (fieldResult.errors.length > 0) {
                    result.errors.push(...fieldResult.errors);
                    result.fieldValidation[fieldPath] = 'error';
                } else if (fieldResult.warnings.length > 0) {
                    result.warnings.push(...fieldResult.warnings);
                    result.fieldValidation[fieldPath] = 'warning';
                } else if (value !== undefined && value !== '') {
                    result.fieldValidation[fieldPath] = 'valid';
                }
            });
        });
    }

    /**
     * 验证单个字段
     * @param {*} value - 字段值
     * @param {Object} rule - 验证规则
     * @param {string} fieldPath - 字段路径
     * @param {boolean} strict - 严格模式
     */
    validateSingleField(value, rule, fieldPath, strict) {
        const fieldResult = {
            errors: [],
            warnings: []
        };

        // 检查必填字段
        if (rule.required && (value === undefined || value === null || value === '')) {
            fieldResult.errors.push(`${fieldPath} 是必填字段`);
            return fieldResult;
        }

        // 如果值为空且非必填，跳过验证
        if (value === undefined || value === null || value === '') {
            return fieldResult;
        }

        const stringValue = String(value).trim();

        // 长度验证
        if (rule.minLength && stringValue.length < rule.minLength) {
            fieldResult.errors.push(`${fieldPath} 长度不能少于 ${rule.minLength} 个字符`);
        }

        if (rule.maxLength && stringValue.length > rule.maxLength) {
            fieldResult.errors.push(`${fieldPath} 长度不能超过 ${rule.maxLength} 个字符`);
        }

        // 正则表达式验证
        if (rule.pattern && !rule.pattern.test(stringValue)) {
            fieldResult.errors.push(rule.errorMessage || `${fieldPath} 格式不正确`);
        }

        // 枚举值验证
        if (rule.enum && !rule.enum.includes(stringValue)) {
            fieldResult.errors.push(`${fieldPath} 必须为以下值之一: ${rule.enum.join(', ')}`);
        }

        // 日期验证
        if (rule.type === 'date') {
            const dateValidation = this.validateDate(stringValue, rule, fieldPath);
            fieldResult.errors.push(...dateValidation.errors);
            fieldResult.warnings.push(...dateValidation.warnings);
        }

        // 自定义验证器
        if (rule.validator && typeof rule.validator === 'function') {
            try {
                const isValid = rule.validator(stringValue);
                if (!isValid) {
                    fieldResult.errors.push(rule.errorMessage || `${fieldPath} 验证失败`);
                }
            } catch (error) {
                fieldResult.errors.push(`${fieldPath} 验证器执行失败: ${error.message}`);
            }
        }

        return fieldResult;
    }

    /**
     * 验证日期
     * @param {string} dateStr - 日期字符串
     * @param {Object} rule - 验证规则
     * @param {string} fieldPath - 字段路径
     */
    validateDate(dateStr, rule, fieldPath) {
        const result = { errors: [], warnings: [] };

        if (!this.dateFormatter) {
            result.warnings.push(`${fieldPath} 无法验证日期格式（日期格式化器不可用）`);
            return result;
        }

        try {
            const parsed = this.dateFormatter.parseDate(dateStr);
            
            if (!parsed.isValid) {
                result.errors.push(`${fieldPath} 日期格式不正确: ${parsed.error}`);
                return result;
            }

            const date = new Date(parsed.year, parsed.month - 1, parsed.day);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 年龄验证
            if (rule.minAge !== undefined || rule.maxAge !== undefined) {
                const age = Math.floor((today - date) / (365.25 * 24 * 60 * 60 * 1000));
                
                if (rule.minAge !== undefined && age < rule.minAge) {
                    result.errors.push(`${fieldPath} 年龄不能小于 ${rule.minAge} 岁`);
                }
                
                if (rule.maxAge !== undefined && age > rule.maxAge) {
                    result.errors.push(`${fieldPath} 年龄不能大于 ${rule.maxAge} 岁`);
                }
            }

            // 最小日期验证
            if (rule.minDate) {
                let minDate;
                if (rule.minDate === 'today') {
                    minDate = today;
                } else if (typeof rule.minDate === 'string') {
                    // 可能是其他字段的引用，暂时跳过
                    return result;
                } else {
                    minDate = new Date(rule.minDate);
                }

                if (date < minDate) {
                    result.errors.push(`${fieldPath} 不能早于 ${minDate.toLocaleDateString()}`);
                }
            }

        } catch (error) {
            result.errors.push(`${fieldPath} 日期验证失败: ${error.message}`);
        }

        return result;
    }

    /**
     * 数据标准化
     * @param {Object} data - 数据
     * @param {Object} result - 验证结果
     */
    normalizeData(data, result) {
        Object.entries(this.validationRules).forEach(([category, fields]) => {
            if (!data[category]) return;

            Object.entries(fields).forEach(([fieldName, rule]) => {
                const value = data[category][fieldName];
                
                if (value !== undefined && value !== null && value !== '') {
                    if (rule.normalize && typeof rule.normalize === 'function') {
                        try {
                            data[category][fieldName] = rule.normalize(String(value));
                        } catch (error) {
                            result.warnings.push(`${category}.${fieldName} 标准化失败: ${error.message}`);
                        }
                    }
                }
            });
        });
    }

    /**
     * 业务逻辑验证
     * @param {Object} data - 数据
     * @param {Object} result - 验证结果
     */
    validateBusinessLogic(data, result) {
        this.businessRules.forEach(rule => {
            try {
                const ruleResult = rule.validator(data);
                if (ruleResult.errors) {
                    result.errors.push(...ruleResult.errors);
                }
                if (ruleResult.warnings) {
                    result.warnings.push(...ruleResult.warnings);
                }
            } catch (error) {
                result.warnings.push(`业务规则 ${rule.name} 验证失败: ${error.message}`);
            }
        });
    }

    /**
     * 验证日期逻辑
     * @param {Object} data - 数据
     */
    validateDateLogic(data) {
        const result = { errors: [], warnings: [] };

        if (data.travelInfo && data.travelInfo.entryDate && data.travelInfo.exitDate) {
            try {
                const entryParts = data.travelInfo.entryDate.split('/');
                const exitParts = data.travelInfo.exitDate.split('/');
                
                const entryDate = new Date(entryParts[2], entryParts[1] - 1, entryParts[0]);
                const exitDate = new Date(exitParts[2], exitParts[1] - 1, exitParts[0]);

                if (exitDate <= entryDate) {
                    result.errors.push('离境日期必须晚于入境日期');
                }

                // 检查停留时间是否合理
                const stayDays = Math.ceil((exitDate - entryDate) / (1000 * 60 * 60 * 24));
                if (stayDays > 365) {
                    result.warnings.push('停留时间超过一年，请确认日期是否正确');
                }

            } catch (error) {
                result.warnings.push('日期格式验证失败，无法进行逻辑验证');
            }
        }

        return result;
    }

    /**
     * 验证交通信息逻辑
     * @param {Object} data - 数据
     */
    validateTransportLogic(data) {
        const result = { errors: [], warnings: [] };

        if (data.transportInfo) {
            const { flightNumber, vehicleNumber, modeOfTravel } = data.transportInfo;

            // 自动推断交通方式
            if (!modeOfTravel) {
                if (flightNumber) {
                    data.transportInfo.modeOfTravel = 'AIR';
                } else if (vehicleNumber) {
                    data.transportInfo.modeOfTravel = 'LAND';
                }
            }

            // 验证交通方式与交通工具的一致性
            if (modeOfTravel === 'AIR' && !flightNumber) {
                result.warnings.push('选择了航空交通但未提供航班号');
            }

            if (modeOfTravel === 'LAND' && !vehicleNumber) {
                result.warnings.push('选择了陆路交通但未提供车牌号');
            }

            if (flightNumber && modeOfTravel && modeOfTravel !== 'AIR') {
                result.errors.push('提供了航班号但交通方式不是航空');
            }

            if (vehicleNumber && modeOfTravel && modeOfTravel !== 'LAND') {
                result.errors.push('提供了车牌号但交通方式不是陆路');
            }
        }

        return result;
    }

    /**
     * 验证数据完整性
     * @param {Object} data - 数据
     */
    validateCompleteness(data) {
        const result = { errors: [], warnings: [] };

        // 检查基本信息完整性
        if (data.personalInfo) {
            const requiredFields = ['name'];
            const missingFields = requiredFields.filter(field => 
                !data.personalInfo[field] || data.personalInfo[field].trim() === ''
            );

            if (missingFields.length > 0) {
                result.warnings.push(`缺少重要个人信息: ${missingFields.join(', ')}`);
            }
        }

        // 检查旅行信息完整性
        if (data.travelInfo) {
            if (data.travelInfo.entryDate && !data.travelInfo.exitDate) {
                result.warnings.push('提供了入境日期但缺少离境日期');
            }

            if (data.travelInfo.exitDate && !data.travelInfo.entryDate) {
                result.warnings.push('提供了离境日期但缺少入境日期');
            }
        }

        return result;
    }

    /**
     * 批量验证数据
     * @param {Array} dataArray - 数据数组
     * @param {Object} options - 验证选项
     */
    batchValidateData(dataArray, options = {}) {
        if (!Array.isArray(dataArray)) {
            throw new Error('输入必须是数据数组');
        }

        console.log(`🔍 [DataValidator] 开始批量验证 ${dataArray.length} 个数据项`);

        const results = dataArray.map((data, index) => {
            try {
                const result = this.validateData(data, options);
                return {
                    index,
                    success: true,
                    result
                };
            } catch (error) {
                return {
                    index,
                    success: false,
                    error: error.message,
                    data: data
                };
            }
        });

        const successful = results.filter(r => r.success && r.result.isValid);
        const failed = results.filter(r => !r.success || !r.result.isValid);

        const batchResult = {
            total: dataArray.length,
            successful: successful.length,
            failed: failed.length,
            results: results,
            successRate: successful.length / dataArray.length,
            timestamp: Date.now()
        };

        console.log(`✅ [DataValidator] 批量验证完成: ${successful.length}/${dataArray.length} 通过验证`);

        return batchResult;
    }

    /**
     * 添加自定义验证规则
     * @param {string} category - 类别
     * @param {string} fieldName - 字段名
     * @param {Object} rule - 验证规则
     */
    addValidationRule(category, fieldName, rule) {
        if (!this.validationRules[category]) {
            this.validationRules[category] = {};
        }
        
        this.validationRules[category][fieldName] = rule;
        
        console.log(`📋 [DataValidator] 添加验证规则: ${category}.${fieldName}`);
    }

    /**
     * 添加业务规则
     * @param {Object} rule - 业务规则
     */
    addBusinessRule(rule) {
        if (!rule.name || !rule.validator) {
            throw new Error('业务规则必须包含name和validator');
        }
        
        this.businessRules.push(rule);
        
        console.log(`📋 [DataValidator] 添加业务规则: ${rule.name}`);
    }

    /**
     * 获取验证统计
     */
    getValidationStats() {
        const totalRules = Object.values(this.validationRules).reduce((total, category) => {
            return total + Object.keys(category).length;
        }, 0);

        return {
            totalFieldRules: totalRules,
            businessRules: this.businessRules.length,
            categories: Object.keys(this.validationRules),
            hasDateFormatter: !!this.dateFormatter
        };
    }

    /**
     * 销毁数据验证器
     */
    destroy() {
        // 清理事件监听器
        if (this.eventBus) {
            // 移除相关事件监听器
        }

        console.log('🗑️ [DataValidator] 数据验证器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataValidator;
} else {
    window.DataValidator = DataValidator;
}

console.log('✅ [DataValidator] 数据验证器模块已加载');
